import logging
import requests
from flask import jsonify

'''
调用difyApi接口获取历史消息
'''
def get_dify_his_messages(user, conversation_id, api_key):
    logging.info("Received request for messages with user: %s, conversation_id: %s", user, conversation_id)

    # 构建请求 URL
    url = f"https://xinli-agent.hedacapital.com/v1/messages?user={user}&conversation_id={conversation_id}"

    # 设置请求头
    headers = {
        'Authorization': api_key
    }

    try:
        # 发送 GET 请求
        response = requests.get(url, headers=headers)

        # 检查响应状态码
        if response.status_code == 200:
            # 解析响应数据
            data = response.json()
            data_list = data.get("data", [])
            return data_list[-3:] if len(data_list) > 3 else data_list
            # return jsonify(data)
        else:
            logging.error("Failed to fetch messages. Status code: %d", response.status_code)
            return jsonify({"error": "Failed to fetch messages"}), response.status_code
    except requests.exceptions.RequestException as e:
        logging.error("Request failed: %s", e)
        return jsonify({"error": "Request failed"}), 500