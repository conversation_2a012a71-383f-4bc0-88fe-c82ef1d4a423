"""
响应工具类
提供统一的API响应格式
"""
from flask import jsonify
from typing import Any, Dict, Optional


def success_response(data: Any = None, message: str = "操作成功", status_code: int = 200) -> tuple:
    """
    成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        status_code: HTTP状态码
        
    Returns:
        Flask响应元组
    """
    response = {
        "success": True,
        "message": message,
        "data": data,
        "code": status_code
    }
    return jsonify(response), status_code


def error_response(message: str = "操作失败", status_code: int = 400, error_code: str = None) -> tuple:
    """
    错误响应
    
    Args:
        message: 错误消息
        status_code: HTTP状态码
        error_code: 错误代码
        
    Returns:
        Flask响应元组
    """
    response = {
        "success": False,
        "message": message,
        "data": None,
        "code": status_code
    }
    
    if error_code:
        response["error_code"] = error_code
    
    return jsonify(response), status_code


def paginated_response(data: list, total: int, page: int = 1, page_size: int = 20, 
                      message: str = "查询成功") -> tuple:
    """
    分页响应
    
    Args:
        data: 数据列表
        total: 总数量
        page: 当前页码
        page_size: 每页大小
        message: 响应消息
        
    Returns:
        Flask响应元组
    """
    response = {
        "success": True,
        "message": message,
        "data": data,
        "pagination": {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        },
        "code": 200
    }
    return jsonify(response), 200
