# ... existing code ...
import logging

from openai import OpenAI


def ask_volcengine_llm(prompt):
    client = OpenAI(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key
        # api_key=os.environ.get("ARK_API_KEY"),
        api_key="36e2edda-bc55-4f0f-a050-036fda847e90",
    )

    # Non-streaming:
    completion = client.chat.completions.create(
        # 指定您创建的方舟推理接入点 ID，此处已帮您修改为您的推理接入点 ID
        model="doubao-pro-256k-241115",
        # model="deepseek-r1-250120",
        # model="doubao-1-5-vision-pro-32k-250115",
        messages=[
            {"role": "system", "content": "你是问题提问助手"},
            # {"role": "user", "content": "常见的十字花科植物有哪些？"},
            {"role": "user", "content": prompt},
        ],
    )
    logging.info("问题建议如下：%s",completion.choices[0].message.content)
    return completion.choices[0].message.content

    # # Streaming:
    # print("----- streaming request -----")
    # stream = client.chat.completions.create(
    #     # 指定您创建的方舟推理接入点 ID，此处已帮您修改为您的推理接入点 ID
    #     model="doubao-pro-256k-241115",
    #     messages=[
    #         {"role": "system", "content": "你是人工智能助手"},
    #         {"role": "user", "content": "常见的十字花科植物有哪些？"},
    #     ],
    #     # 响应内容是否流式返回
    #     stream=True,
    # )
    # for chunk in stream:
    #     if not chunk.choices:
    #         continue
    #     print(chunk.choices[0].delta.content, end="")
    # print()
