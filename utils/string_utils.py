from typing import Optional, Union, List, Dict, Any


class StringUtils:
    """
    字符串工具类，提供字符串相关的校验方法
    """
    
    @staticmethod
    def is_empty(string: Optional[str]) -> bool:
        """
        判断字符串是否为None或空字符串
        
        Args:
            string: 待检查的字符串
            
        Returns:
            bool: 如果字符串为None或空字符串，返回True，否则返回False
        """
        return string is None or string == ""
    
    @staticmethod
    def is_not_empty(string: Optional[str]) -> bool:
        """
        判断字符串是否不为None且不为空字符串
        
        Args:
            string: 待检查的字符串
            
        Returns:
            bool: 如果字符串不为None且不为空字符串，返回True，否则返回False
        """
        return not StringUtils.is_empty(string)
    
    @staticmethod
    def is_blank(string: Optional[str]) -> bool:
        """
        判断字符串是否为None、空字符串或仅包含空白字符
        
        Args:
            string: 待检查的字符串
            
        Returns:
            bool: 如果字符串为None、空字符串或仅包含空白字符，返回True，否则返回False
        """
        return string is None or string.strip() == ""
    
    @staticmethod
    def is_not_blank(string: Optional[str]) -> bool:
        """
        判断字符串是否不为None、不为空字符串且不仅包含空白字符
        
        Args:
            string: 待检查的字符串
            
        Returns:
            bool: 如果字符串不为None、不为空字符串且不仅包含空白字符，返回True，否则返回False
        """
        return not StringUtils.is_blank(string)
    
    @staticmethod
    def default_if_empty(string: Optional[str], default_value: str) -> str:
        """
        如果字符串为None或空字符串，则返回默认值
        
        Args:
            string: 待检查的字符串
            default_value: 默认值
            
        Returns:
            str: 如果字符串为None或空字符串，返回默认值，否则返回原字符串
        """
        return default_value if StringUtils.is_empty(string) else string
    
    @staticmethod
    def default_if_blank(string: Optional[str], default_value: str) -> str:
        """
        如果字符串为None、空字符串或仅包含空白字符，则返回默认值
        
        Args:
            string: 待检查的字符串
            default_value: 默认值
            
        Returns:
            str: 如果字符串为None、空字符串或仅包含空白字符，返回默认值，否则返回原字符串
        """
        return default_value if StringUtils.is_blank(string) else string
    
    @staticmethod
    def truncate(string: str, max_length: int, suffix: str = "...") -> str:
        """
        截断字符串，如果超过最大长度，则添加后缀
        
        Args:
            string: 待截断的字符串
            max_length: 最大长度
            suffix: 后缀，默认为"..."
            
        Returns:
            str: 截断后的字符串
        """
        if string is None:
            return ""
        if len(string) <= max_length:
            return string
        return string[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def equals_ignore_case(str1: Optional[str], str2: Optional[str]) -> bool:
        """
        忽略大小写比较两个字符串是否相等
        
        Args:
            str1: 第一个字符串
            str2: 第二个字符串
            
        Returns:
            bool: 如果两个字符串忽略大小写后相等，返回True，否则返回False
        """
        if str1 is None and str2 is None:
            return True
        if str1 is None or str2 is None:
            return False
        return str1.lower() == str2.lower()
    
    @staticmethod
    def contains_ignore_case(string: str, substring: str) -> bool:
        """
        忽略大小写检查字符串是否包含子字符串
        
        Args:
            string: 字符串
            substring: 子字符串
            
        Returns:
            bool: 如果字符串忽略大小写后包含子字符串，返回True，否则返回False
        """
        if string is None or substring is None:
            return False
        return substring.lower() in string.lower()
    
    @staticmethod
    def is_numeric(string: Optional[str]) -> bool:
        """
        判断字符串是否为数字（整数或浮点数）
        
        Args:
            string: 待检查的字符串
            
        Returns:
            bool: 如果字符串是数字，返回True，否则返回False
        """
        if StringUtils.is_empty(string):
            return False
        try:
            float(string)
            return True
        except ValueError:
            return False
