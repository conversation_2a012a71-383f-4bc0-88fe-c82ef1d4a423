"""
请求参数处理工具
提供请求参数验证和转换的通用函数
"""
from typing import Optional, Dict, Any, Tuple
from flask import request


def get_pagination_params() -> Tuple[int, int]:
    """
    获取并验证分页参数
    
    Returns:
        Tuple[int, int]: (page_no, page_size) 元组
    """
    try:
        page_no = request.args.get('pageNo', 1, type=int)
        page_size = request.args.get('pageSize', 20, type=int)
        
        # 验证并修正参数
        if page_no < 1:
            page_no = 1
        if page_size < 1:
            page_size = 100
        if page_size > 100:
            page_size = 100
            
        return page_no, page_size
    except Exception:
        # 出现异常时返回默认值
        return 1, 100


def get_query_param(name: str, default=None, param_type=None) -> Any:
    """
    获取查询参数并转换为指定类型

    Args:
        name: 参数名
        default: 默认值
        param_type: 参数类型转换函数

    Returns:
        转换后的参数值
    """
    try:
        return request.args.get(name, default, type=param_type)
    except (ValueError, TypeError):
        # 类型转换失败时返回默认值
        return default


def get_int_param(name: str, default=None) -> Optional[int]:
    """
    获取整数类型的查询参数
    
    Args:
        name: 参数名
        default: 默认值
        
    Returns:
        整数参数值或None
    """
    return get_query_param(name, default, int)


def get_str_param(name: str, default=None) -> Optional[str]:
    """
    获取字符串类型的查询参数
    
    Args:
        name: 参数名
        default: 默认值
        
    Returns:
        字符串参数值或None
    """
    return get_query_param(name, default, str)


def get_bool_param(name: str, default=None) -> Optional[bool]:
    """
    获取布尔类型的查询参数
    
    Args:
        name: 参数名
        default: 默认值
        
    Returns:
        布尔参数值或None
    """
    return get_query_param(name, default, bool)


def get_float_param(name: str, default=None) -> Optional[float]:
    """
    获取浮点数类型的查询参数
    
    Args:
        name: 参数名
        default: 默认值
        
    Returns:
        浮点数参数值或None
    """
    return get_query_param(name, default, float)


def get_json_body() -> Dict[str, Any]:
    """
    获取请求的JSON数据
    
    Returns:
        请求体中的JSON数据，如果无效则返回空字典
    """
    try:
        return request.get_json() or {}
    except Exception:
        return {} 