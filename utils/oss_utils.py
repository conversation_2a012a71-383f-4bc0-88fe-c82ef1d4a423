import oss2

import config
from utils.AliyunOssConfig import AliyunOssConfig

# 创建 OSS 客户端
access_key_id = str(AliyunOssConfig.access_key_id)
access_key_secret = str(AliyunOssConfig.access_key_secret)
endpoint = str(AliyunOssConfig.endpoint)
bucket_name = str(AliyunOssConfig.bucket_name)
oss_base_url = str(AliyunOssConfig.oss_base_url)


auth = oss2.Auth(access_key_id, access_key_secret)
bucket = oss2.Bucket(auth, endpoint, bucket_name)


def upload_to_oss(file_path, object_name):
    """
    上传文件到 OSS
    :param file_path: 本地文件路径
    :param object_name: OSS 中的对象名称
    :return: OSS 文件访问 URL
    """
    try:
        bucket.put_object_from_file(object_name, file_path)
        return f"{oss_base_url}/{object_name}"
    except Exception as e:
        raise Exception(f"Failed to upload file to OSS: {str(e)}")