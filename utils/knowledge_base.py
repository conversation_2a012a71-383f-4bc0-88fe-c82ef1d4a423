import logging

import pymysql
import json

from config import config


def search_knowledge_base():
    """
    简单示例：根据上下文和问题检索本地知识库
    """
    knowledge = load_local_knowledge_base("knowledge_base.json")
    # 简单返回全部知识点
    return knowledge

def load_local_knowledge_base(file_path):
    """
    从文件加载本地知识库
    :param file_path: 知识库文件路径
    :return: 知识库字典
    """
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            knowledge_base = json.load(file)
        return knowledge_base
    except FileNotFoundError:
        logging.error("知识库文件未找到")
        return {}
    except json.JSONDecodeError:
        logging.error("知识库文件格式错误")
        return {}

"""
获取数据库中的数据
"""
def get_database_knowledge_base(env,select_sql):
    """
        从 MySQL 数据库加载知识库
        :param db_config: 数据库配置字典
        :return: 知识库字典
        """

    try:
        # 获取数据库配置
        config_data = config.conf()
        datasources = config_data.get('datasources', {})
        main_config = datasources.get('main', {})

        # 连接到 MySQL 数据库
        conn = pymysql.connect(
            host=main_config.get("host", ""),
            user=main_config.get("user", ""),
            password=main_config.get("password", ""),
            database=main_config.get("database", ""),
            port=main_config.get("port", 3306),
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        cursor = conn.cursor()  # 使用字典形式返回结果
        logging.info("连接数据库成功")
        # 执行查询语句
        cursor.execute(select_sql)
        logging.info("查询成功")
        # 获取查询结果
        knowledge_base = cursor.fetchall()
        # 关闭连接
        cursor.close()
        conn.close()

        return knowledge_base

    except pymysql.Error as err:
        logging.error(f"数据库错误: {err}")
        return {}
    except Exception as e:
        logging.error(f"未知错误: {e}")
        return {}


'''
根据 dify 上下文信息提取出问题和答案
'''
# def extract_answers_and_last_messages(data):
#     contextList = []
#     for item in data:
#         answer = item.get("answer")  # 提取 answer 字段
#         last_message_text = item["message"][-1]["text"] if item["message"] else None  # 提取 message 列表最后一个元素的 text 字段
#         context = {
#             "answer": answer,
#             "problem": last_message_text
#         }
#         contextList.append(context)
#     return contextList


'''
调用dify上下文信息接口，解析数据
'''
def extract_answers_and_last_his_messages(data):
    contextList = []
    for item in data:
        answer = item.get("answer")  # 提取 answer 字段
        query = item.get("query")
        context = {
            "answer": answer,
            "problem": query
        }
        contextList.append(context)
    return contextList
