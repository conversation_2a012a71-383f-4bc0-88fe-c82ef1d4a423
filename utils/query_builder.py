"""
查询构建器工具模块
提供通用的查询条件构建功能
"""
from typing import Dict, Any, List, Optional
from sqlalchemy import or_, and_


class QueryBuilder:
    """通用查询构建器"""
    
    def __init__(self, model_class):
        """
        初始化查询构建器
        
        Args:
            model_class: SQLAlchemy模型类
        """
        self.model_class = model_class
        self.filters = []
    
    def add_base_filter(self, condition):
        """添加基础过滤条件"""
        self.filters.append(condition)
        return self
    
    def add_keyword_search(self, keyword: Optional[str], *fields):
        """
        添加关键词搜索条件
        
        Args:
            keyword: 搜索关键词
            *fields: 要搜索的字段列表
        """
        if keyword:
            search_conditions = []
            for field in fields:
                search_conditions.append(field.like(f'%{keyword}%'))
            self.filters.append(or_(*search_conditions))
        return self
    
    def add_exact_match(self, field_name: str, value: Any):
        """
        添加精确匹配条件
        
        Args:
            field_name: 字段名
            value: 匹配值
        """
        if value is not None:
            field = getattr(self.model_class, field_name)
            self.filters.append(field == value)
        return self
    
    def add_range_filter(self, field_name: str, min_value: Any = None, max_value: Any = None):
        """
        添加范围过滤条件
        
        Args:
            field_name: 字段名
            min_value: 最小值
            max_value: 最大值
        """
        field = getattr(self.model_class, field_name)
        if min_value is not None:
            self.filters.append(field >= min_value)
        if max_value is not None:
            self.filters.append(field <= max_value)
        return self
    
    def add_in_filter(self, field_name: str, values: List[Any]):
        """
        添加IN条件过滤
        
        Args:
            field_name: 字段名
            values: 值列表
        """
        if values:
            field = getattr(self.model_class, field_name)
            self.filters.append(field.in_(values))
        return self
    
    def add_custom_filter(self, condition):
        """添加自定义过滤条件"""
        if condition is not None:
            self.filters.append(condition)
        return self
    
    def build(self):
        """构建最终的查询条件"""
        if not self.filters:
            return None
        return and_(*self.filters)
    
    def get_query(self):
        """获取查询对象"""
        condition = self.build()
        if condition is not None:
            return self.model_class.query.filter(condition)
        return self.model_class.query

    def add_ordering(self, query, order_by: str = 'id', desc: bool = True):
        """
        添加排序条件

        Args:
            query: 查询对象
            order_by: 排序字段
            desc: 是否降序
        """
        field = getattr(self.model_class, order_by)
        if desc:
            return query.order_by(field.desc())
        return query.order_by(field)

    def get_paginated_query(self, filters: Dict[str, Any],
                          limit: int = 20, offset: int = 0,
                          order_by: str = 'id', desc: bool = True):
        """
        获取分页查询结果

        Args:
            filters: 过滤条件
            limit: 限制数量
            offset: 偏移量
            order_by: 排序字段
            desc: 是否降序

        Returns:
            tuple: (查询结果列表, 总数)
        """
        # 构建查询条件
        self.add_filters_dict(filters)
        base_query = self.get_query()

        # 获取总数
        total = base_query.count()

        # 获取分页数据
        ordered_query = self.add_ordering(base_query, order_by, desc)
        results = ordered_query.limit(limit).offset(offset).all()

        return results, total

    def add_filters_dict(self, filters: Dict[str, Any]):
        """
        根据字典添加过滤条件（可被子类重写以实现特定逻辑）

        Args:
            filters: 过滤条件字典
        """
        # 默认实现：遍历filters并尝试添加精确匹配条件
        for field_name, value in filters.items():
            if value is not None and hasattr(self.model_class, field_name):
                self.add_exact_match(field_name, value)
        return self

