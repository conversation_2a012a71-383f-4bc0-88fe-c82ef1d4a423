"""
通用装饰器模块
提供常用的装饰器函数，用于异常处理、参数验证等
"""
import logging
from functools import wraps
from flask import jsonify
from http import HTTPStatus
from typing import Type, Any

from utils.response_utils import error_response
from utils.request import get_json_body


def handle_exceptions(f):
    """
    统一异常处理装饰器
    
    自动捕获并处理函数中的异常，返回标准化的错误响应
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            # 业务逻辑错误（如数据验证失败、资源不存在等）
            logging.warning(f"业务逻辑错误 in {f.__name__}: {e}")
            return error_response(
                message=str(e),
                status_code=HTTPStatus.BAD_REQUEST
            )
        except Exception as e:
            # 系统错误
            logging.error(f"系统错误 in {f.__name__}: {e}")
            return error_response(
                message=f"服务器错误: {str(e)}",
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR
            )
    return decorated_function


def validate_json_body(model_class: Type[Any]):
    """
    JSON请求体验证装饰器
    
    Args:
        model_class: Pydantic模型类，用于验证请求数据
        
    使用示例:
        @validate_json_body(UserCreateModel)
        def create_user(user_data: UserCreateModel):
            # user_data 已经是验证过的模型实例
            pass
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            json_data = get_json_body()
            if not json_data:
                return jsonify(error_response(
                    code=HTTPStatus.BAD_REQUEST, 
                    message="无效的请求数据"
                )), HTTPStatus.BAD_REQUEST
            
            try:
                validated_data = model_class(**json_data)
                # 将验证后的数据作为第一个参数传递给原函数
                return f(validated_data, *args, **kwargs)
            except Exception as e:
                return jsonify(error_response(
                    code=HTTPStatus.BAD_REQUEST, 
                    message=f"数据验证失败: {str(e)}"
                )), HTTPStatus.BAD_REQUEST
        return decorated_function
    return decorator


def log_request(f):
    """
    请求日志装饰器
    
    记录API请求的基本信息
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        logging.info(f"API请求: {f.__name__}")
        result = f(*args, **kwargs)
        logging.info(f"API响应: {f.__name__} 完成")
        return result
    return decorated_function


def require_params(*required_params):
    """
    必需参数验证装饰器
    
    Args:
        *required_params: 必需的参数名列表
        
    使用示例:
        @require_params('user_id', 'action')
        def update_user_status(user_id, action):
            pass
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查路径参数
            missing_params = []
            for param in required_params:
                if param not in kwargs:
                    missing_params.append(param)
            
            if missing_params:
                return jsonify(error_response(
                    code=HTTPStatus.BAD_REQUEST,
                    message=f"缺少必需参数: {', '.join(missing_params)}"
                )), HTTPStatus.BAD_REQUEST
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
