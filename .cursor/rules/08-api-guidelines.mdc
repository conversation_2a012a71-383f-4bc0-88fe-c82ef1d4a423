# API开发规范

本项目的API开发遵循RESTful设计原则，以下是开发新API或修改现有API时需要遵循的规范。

## RESTful路径设计

- 使用名词而非动词表示资源
- 资源路径使用复数形式
- 使用嵌套资源表示从属关系

```
# 推荐
GET /ai-answer/applications        # 获取应用列表
GET /ai-answer/applications/123    # 获取特定应用
POST /ai-answer/applications       # 创建新应用

# 不推荐
GET /ai-answer/get-applications
GET /ai-answer/application/get/123
POST /ai-answer/create-application
```

## HTTP方法

- GET: 获取资源
- POST: 创建资源
- PUT: 全量更新资源
- PATCH: 部分更新资源
- DELETE: 删除资源

## 请求参数规范

### 查询参数

- 分页参数统一使用 `pageNo` 和 `pageSize` [[memory:3424050]]
- 过滤参数使用有意义的名称，如 `status`, `keyword` 等
- 参数名称使用 camelCase 格式

### 请求体

- 使用JSON格式
- 字段名使用camelCase格式
- 使用Pydantic模型进行数据验证

## 响应规范

### 状态码

- 200: 成功 (GET, PUT, PATCH)
- 201: 创建成功 (POST)
- 204: 删除成功 (DELETE)
- 400: 客户端错误 (参数错误等)
- 401: 未授权 (认证失败)
- 403: 禁止访问 (权限不足)
- 404: 资源不存在
- 500: 服务器错误

### 响应结构

所有API响应必须使用统一的格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

分页响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [],
    "total": 100,
    "pageNo": 1,
    "pageSize": 20
  }
}
```
