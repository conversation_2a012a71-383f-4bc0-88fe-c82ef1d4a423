# 多智能体模块 (multi_agent/)

多智能体模块提供AI应用的创建、管理和调用功能，遵循MVC架构设计。

## 核心文件

- [multi_agent/__init__.py](mdc:multi_agent/__init__.py) - 模块初始化
- [multi_agent/ai_application_controller.py](mdc:multi_agent/ai_application_controller.py) - 控制器层，处理HTTP请求
- [multi_agent/ai_application_service.py](mdc:multi_agent/ai_application_service.py) - 服务层，处理业务逻辑
- [multi_agent/ai_application_model.py](mdc:multi_agent/ai_application_model.py) - 模型层，定义数据结构

## API端点

通过Blueprint注册在`/multiagent/application`下的API端点：

- `GET /multiagent/application` - 获取应用列表，支持分页和过滤
- `POST /multiagent/application` - 创建新应用
- `GET /multiagent/application/<app_id>` - 获取单个应用详情
- `PUT /multiagent/application/<app_id>` - 更新应用信息
- `DELETE /multiagent/application/<app_id>` - 删除应用
- `GET /multiagent/application/get-by-app-key` - 通过appKey获取应用

## 数据模型

应用使用两种主要的数据模型：
1. `AIApplicationDO` - 数据对象，用于数据库操作
2. `AIApplicationVO` - 视图对象，用于API响应

## 分页处理

所有列表查询API都支持分页，使用`pageNo`和`pageSize`参数 [[memory:3424050]]。
