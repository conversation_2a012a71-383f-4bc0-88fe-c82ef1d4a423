# 敏感词过滤模块 (sensitive_words/)

敏感词过滤模块提供文本内容审核功能，支持本地词库和阿里云内容安全服务两种模式。

## 核心文件

- [sensitive_words/__init__.py](mdc:sensitive_words/__init__.py) - 模块初始化
- [sensitive_words/sensitive_words_service.py](mdc:sensitive_words/sensitive_words_service.py) - 敏感词服务主类
- [sensitive_words/vocabulary_manager.py](mdc:sensitive_words/vocabulary_manager.py) - 词汇表管理
- [sensitive_words/ali_sensitive_words.py](mdc:sensitive_words/ali_sensitive_words.py) - 阿里云内容安全集成
- [sensitive_words/custom_sensitive_words.py](mdc:sensitive_words/custom_sensitive_words.py) - 自定义敏感词过滤

## 词库结构

词库文件位于`sensitive_words/file/vocabulary/`目录下，包含多个分类词库：
- 反动词库
- 暴恐词库
- 色情词库
- 贪腐词库
- 民生词库
- 其他词库
- 补充词库

## 主要功能

### DFA算法过滤

系统使用确定有限自动机(DFA)算法进行本地敏感词过滤，具有高效的特点。

### 词库管理

```python
from sensitive_words.vocabulary_manager import VocabularyManager

# 初始化词库管理器
vm = VocabularyManager()
# 加载词库
vm.load_vocabulary()
# 检查文本
result = vm.check_text("待检查的文本内容")
```

### 阿里云内容安全

系统集成了阿里云内容安全服务，可用于云端文本审核：

```python
from sensitive_words.ali_sensitive_words import AliSensitiveWords

# 初始化阿里云内容安全
ali_sw = AliSensitiveWords(access_key_id, access_key_secret)
# 检查文本
result = ali_sw.check_text("待检查的文本内容")
```
