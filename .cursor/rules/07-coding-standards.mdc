# 编码规范

在开发和维护此项目时，请遵循以下编码规范和最佳实践。

## Python代码风格

1. **PEP 8**: 遵循[PEP 8](https://pep8.org/)风格指南
   - 使用4个空格缩进
   - 行长度限制在120字符
   - 类名使用CamelCase
   - 函数和变量名使用snake_case
   - 常量使用ALL_CAPS

2. **文档字符串**: 为模块、类和函数添加docstring
   ```python
   def function_name(param1, param2):
       """
       函数功能简述
       
       Args:
           param1: 参数1的描述
           param2: 参数2的描述
           
       Returns:
           返回值描述
           
       Raises:
           可能抛出的异常
       """
       pass
   ```

3. **类型注解**: 使用类型提示
   ```python
   def add(a: int, b: int) -> int:
       return a + b
   ```

## 项目特定规范

1. **日志记录**:
   - 使用logging模块而非print语句
   - 正确选择日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)

2. **异常处理**:
   - 使用 `@handle_exceptions` 装饰器进行全局异常处理
   - 具体异常处理使用 try-except 块

3. **API规范**:
   - 使用标准的HTTP状态码
   - API响应使用统一的返回格式
   - 使用 `@log_request` 装饰器记录API请求

4. **数据库操作**:
   - 使用参数化查询避免SQL注入
   - 事务操作必须使用with语句或装饰器

5. **配置管理**:
   - 不要在代码中硬编码配置项
   - 所有配置通过config模块访问
