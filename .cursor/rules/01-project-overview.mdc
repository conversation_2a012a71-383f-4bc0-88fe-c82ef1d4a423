# AI智能问答系统项目概览

这是一个基于Python和Flask框架开发的智能问答系统，集成了多个大语言模型（LLM）服务，支持知识库检索和上下文对话功能。

## 项目架构

- **主入口**: [run.py](mdc:run.py) - 应用启动入口，负责加载配置并启动Flask服务
- **配置管理**: [config/config.py](mdc:config/config.py) - 管理项目配置，支持多环境部署
- **应用创建**: [app/__init__.py](mdc:app/__init__.py) - Flask应用创建和初始化

## 核心框架
- **Web框架**: Flask 3.1.0
- **跨域支持**: flask-cors 5.0.1
- **数据验证**: pydantic 2.11.3
- **数据库**: MySQL (mysql-connector-python 9.3.0)

## 项目结构
```
.
├── app/                   # 主应用模块
├── config/                # 配置管理模块
├── retrieval/             # 知识库检索模块
├── suggest/               # 智能建议模块
├── multi_agent/           # 多智能体模块
├── utils/                 # 工具类模块
├── sensitive_words/       # 敏感词过滤模块
├── run.py                 # 应用启动入口
├── requirements.txt       # 项目依赖
├── Dockerfile             # Docker构建文件
├── start.sh               # 启动脚本
└── stop.sh                # 停止脚本
```
