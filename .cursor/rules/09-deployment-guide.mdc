# 部署与运行指南

本项目支持本地开发环境和Docker容器部署两种方式。

## 本地开发环境

1. **环境准备**:
   - Python 3.12
   - MySQL数据库

2. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

3. **配置文件**:
   确保`config/config.dev.json`中包含正确的数据库连接信息和其他必要配置。

4. **启动服务**:
   ```bash
   # 使用启动脚本
   ./start.sh dev 5000
   
   # 或直接运行
   python run.py dev
   ```

5. **停止服务**:
   ```bash
   # 使用停止脚本
   ./stop.sh
   ```

## Docker部署

使用[Dockerfile](mdc:Dockerfile)可以构建一个完整的容器镜像：

1. **构建镜像**:
   ```bash
   docker buildx build --platform linux/amd64 -t ai-answer:latest --load .
   ```

2. **运行容器**:
   ```bash
   docker run -d -p 9000:9000 --name ai-answer \
     -v /path/to/config:/app/config \
     ai-answer:latest
   ```

3. **查看日志**:
   ```bash
   docker logs -f ai-answer
   ```

## 环境变量

可以通过环境变量覆盖配置项：

- `APP_ENV`: 运行环境 (dev/prod)
- `DB_HOST`: 数据库主机
- `DB_PORT`: 数据库端口
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_NAME`: 数据库名称

## 健康检查

服务启动后，可以通过访问 `/ai-answer/health` 端点检查服务状态。
