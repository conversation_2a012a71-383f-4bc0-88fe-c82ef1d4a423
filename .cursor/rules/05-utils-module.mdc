# 工具模块 (utils/)

工具模块提供整个系统使用的通用工具函数和辅助类，包括请求处理、响应格式化、数据库操作等。

## 核心文件

- [utils/request.py](mdc:utils/request.py) - HTTP请求处理工具
- [utils/response_utils.py](mdc:utils/response_utils.py) - HTTP响应格式化工具
- [utils/decorators.py](mdc:utils/decorators.py) - 装饰器工具（异常处理、日志记录等）
- [utils/database_sql.py](mdc:utils/database_sql.py) - 数据库操作工具
- [utils/knowledge_base.py](mdc:utils/knowledge_base.py) - 知识库工具
- [utils/utils.py](mdc:utils/utils.py) - 通用工具函数

## 常用工具函数

### 请求处理

```python
from utils.request import get_pagination_params, get_str_param, get_int_param

# 获取分页参数
page_no, page_size = get_pagination_params()  # 默认返回 page_no=1, page_size=20

# 获取请求参数
keyword = get_str_param('keyword')  # 字符串参数
status = get_int_param('status')    # 整数参数
```

### 响应格式化

```python
from utils.response_utils import success_response, error_response, paginated_response

# 成功响应 - 直接返回Flask响应元组
return success_response(data={'id': 1}, message="操作成功")

# 错误响应
return error_response(message="参数错误", status_code=400)

# 分页响应
return paginated_response(data=items, total=total, page=1, page_size=20)
```

### 异常处理

```python
from utils.decorators import handle_exceptions, log_request

@handle_exceptions  # 自动捕获并处理异常
@log_request        # 记录请求日志
def some_api_function():
    # 业务逻辑
    pass
```
