# 应用模块 (app/)

应用模块是整个系统的核心，负责处理用户请求、路由分发以及集成各种功能模块。

## 模块结构

- [app/__init__.py](mdc:app/__init__.py) - 创建和初始化Flask应用
- [app/retrieval.py](mdc:app/retrieval.py) - 知识库检索相关功能
- [app/suggest.py](mdc:app/suggest.py) - 智能建议生成相关功能
- [app/sensitive_words.py](mdc:app/sensitive_words.py) - 敏感词过滤相关功能

## Flask应用初始化流程

应用初始化过程中会执行以下操作：
1. 创建Flask应用实例
2. 配置数据库连接
3. 初始化CORS跨域支持
4. 注册各模块的Blueprint
5. 配置路由和中间件

## API端点

主要API端点包括：
- `/ai-answer/suggest` - 智能建议服务
- `/ai-answer/retrieval` - 知识库检索服务
- `/ai-answer/sensitive-words` - 敏感词过滤服务
- `/multiagent/application` - 多智能体应用服务
