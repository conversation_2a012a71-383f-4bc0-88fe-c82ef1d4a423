# 配置模块 (config/)

配置模块负责管理系统所有配置，支持多环境部署和用户数据存储。

## 核心文件

- [config/config.py](mdc:config/config.py) - 配置管理核心类和函数
- [config/config.dev.json](mdc:config/config.dev.json) - 开发环境配置
- [config/config.prod.json](mdc:config/config.prod.json) - 生产环境配置
- [config/sensitive_words_config.json](mdc:config/sensitive_words_config.json) - 敏感词过滤配置
- [config/vocabulary_config.json](mdc:config/vocabulary_config.json) - 词汇表配置

## 配置加载流程

1. 在[run.py](mdc:run.py)中调用`config.load_config(env)`加载环境配置
2. 读取对应环境的配置文件（如`config.dev.json`）
3. 解析JSON配置到`Config`类实例
4. 加载用户数据（如存在）

## 配置使用方式

通过`config.conf()`获取全局配置对象，如：

```python
from config import config

# 获取配置项
db_host = config.conf().get('host')
debug_mode = config.conf().get('debug', False)

# 获取用户数据
user_data = config.conf().get_user_data('user_name')
```

## 安全特性

配置模块实现了敏感信息（API密钥、密码）的脱敏处理，在日志中会自动将这些信息替换为星号。
