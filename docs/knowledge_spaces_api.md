# 知识空间 API 文档

## 概述

知识空间模块提供了完整的层级化知识管理功能，支持创建、查询、更新、移动和删除操作。支持最多5级的嵌套结构。

## 数据模型

### KnowledgeSpace 基础字段

```json
{
  "id": "integer - 知识空间ID",
  "userId": "integer - 所属用户ID",
  "parentId": "integer|null - 父级知识空间ID",
  "name": "string(100) - 知识空间名称",
  "description": "string(300)|null - 知识空间描述",
  "level": "integer - 层级深度 (0=顶级, 1=一级子空间...)",
  "path": "string - 空间路径 (如 '1,5,12')",
  "creator": "integer - 创建人ID",
  "createTime": "datetime - 创建时间",
  "updater": "integer - 更新人ID", 
  "updateTime": "datetime - 更新时间",
  "status": "integer - 状态 (1=启用, 0=禁用)",
  "deleted": "boolean - 是否删除",
  "children": "array - 子空间列表 (仅在特定接口返回)",
  "childrenCount": "integer - 子空间数量"
}
```

## API 接口

### 1. 获取知识空间列表

```http
GET /api/v1/knowledge-spaces
```

**Query Parameters:**
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20, 最大: 100)
- `userId`: 用户ID (必填)
- `parentId`: 父空间ID (0表示顶级空间)
- `name`: 名称模糊搜索
- `level`: 层级深度
- `status`: 状态 (1=启用, 0=禁用)
- `includeChildren`: 是否包含子空间信息 (默认: false)
- `orderBy`: 排序字段 (默认: create_time)
- `orderDesc`: 是否倒序 (默认: true)

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": [...],
  "total": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5
}
```

### 2. 获取知识空间树结构

```http
GET /api/v1/knowledge-spaces/tree
```

**Query Parameters:**
- `userId`: 用户ID (必填)
- `maxLevel`: 最大层级深度

**Response:**
```json
{
  "success": true,
  "message": "获取成功", 
  "data": [
    {
      "id": 1,
      "name": "技术文档",
      "children": [
        {
          "id": 2,
          "name": "Python文档",
          "children": [...]
        }
      ]
    }
  ]
}
```

### 3. 根据ID获取知识空间详情

```http
GET /api/v1/knowledge-spaces/{space_id}
```

**Path Parameters:**
- `space_id`: 空间ID

**Query Parameters:**
- `userId`: 用户ID (用于权限验证)

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "技术文档",
    "description": "存放技术相关文档",
    "childrenCount": 5
  }
}
```

### 4. 创建知识空间

```http
POST /api/v1/knowledge-spaces
```

**Request Body:**
```json
{
  "userId": 1001,
  "parentId": null,
  "name": "技术文档",
  "description": "存放技术相关文档",
  "creator": 1001
}
```

**Response:**
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "技术文档",
    "level": 0,
    "path": "1"
  }
}
```

### 5. 更新知识空间

```http
PUT /api/v1/knowledge-spaces/{space_id}
```

**Path Parameters:**
- `space_id`: 空间ID

**Query Parameters:**
- `userId`: 用户ID (用于权限验证)

**Request Body:**
```json
{
  "name": "技术资料库",
  "description": "更新后的描述",
  "status": 1,
  "updater": 1001
}
```

**Response:**
```json
{
  "success": true,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "技术资料库",
    "description": "更新后的描述"
  }
}
```

### 6. 移动知识空间

```http
PUT /api/v1/knowledge-spaces/{space_id}/move
```

**Path Parameters:**
- `space_id`: 要移动的空间ID

**Query Parameters:**
- `userId`: 用户ID (用于权限验证)

**Request Body:**
```json
{
  "targetParentId": 2,
  "updater": 1001
}
```

**Response:**
```json
{
  "success": true,
  "message": "移动成功"
}
```

### 7. 删除知识空间

```http
DELETE /api/v1/knowledge-spaces/{space_id}
```

**Path Parameters:**
- `space_id`: 空间ID

**Query Parameters:**
- `userId`: 用户ID (用于权限验证)
- `deleter`: 删除人ID (必填)
- `hardDelete`: 是否硬删除 (默认: false)

**Response:**
```json
{
  "success": true,
  "message": "删除成功"
}
```

### 8. 恢复已删除的知识空间

```http
PUT /api/v1/knowledge-spaces/{space_id}/restore
```

**Path Parameters:**
- `space_id`: 空间ID

**Query Parameters:**
- `userId`: 用户ID (用于权限验证)
- `updater`: 更新人ID (必填)

**Response:**
```json
{
  "success": true,
  "message": "恢复成功"
}
```

### 9. 获取知识空间统计信息

```http
GET /api/v1/knowledge-spaces/statistics
```

**Query Parameters:**
- `userId`: 用户ID (必填)

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "total": 100,
    "active": 85,
    "inactive": 15,
    "levelStats": {
      "level_0": 10,
      "level_1": 30,
      "level_2": 40,
      "level_3": 18,
      "level_4": 2
    }
  }
}
```

### 10. 获取指定空间的子空间列表

```http
GET /api/v1/knowledge-spaces/{space_id}/children
```

**Path Parameters:**
- `space_id`: 父空间ID

**Query Parameters:**
- `userId`: 用户ID (用于权限验证)
- `includeGrandchildren`: 是否包含孙子空间信息

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": [
    {
      "id": 2,
      "name": "Python文档",
      "parentId": 1,
      "childrenCount": 3
    }
  ]
}
```

## 错误响应

所有API在出错时返回统一格式：

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE"
}
```

**常见错误码:**
- `400`: 请求参数错误
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 业务冲突 (如重名、层级限制等)
- `500`: 服务器内部错误

## 业务规则

1. **层级限制**: 最多支持5级嵌套 (0-4级)
2. **命名唯一性**: 同一父空间下不能存在重名子空间
3. **权限控制**: 用户只能操作自己创建的知识空间
4. **删除限制**: 有子空间的空间无法删除，需先删除所有子空间
5. **移动限制**: 不能移动到自己或自己的后代空间下
6. **路径自动维护**: 移动空间时会自动更新路径和层级信息

## 使用示例

### 创建多层级知识空间结构

```javascript
// 1. 创建顶级空间
const rootSpace = await createSpace({
  userId: 1001,
  name: "技术文档",
  description: "所有技术文档的根目录",
  creator: 1001
});

// 2. 创建二级分类
const pythonSpace = await createSpace({
  userId: 1001,
  parentId: rootSpace.id,
  name: "Python",
  description: "Python相关文档",
  creator: 1001
});

// 3. 创建三级具体内容
const djangoSpace = await createSpace({
  userId: 1001,
  parentId: pythonSpace.id,
  name: "Django框架",
  description: "Django Web框架文档",
  creator: 1001
});
```

### 获取完整的空间树

```javascript
// 获取用户的完整知识空间树
const tree = await fetch('/api/v1/knowledge-spaces/tree?userId=1001');

// 渲染树形结构
function renderTree(spaces) {
  return spaces.map(space => ({
    label: space.name,
    value: space.id,
    children: space.children ? renderTree(space.children) : []
  }));
}
```

### 搜索和过滤

```javascript
// 搜索包含"Python"的空间
const searchResult = await fetch(
  '/api/v1/knowledge-spaces?userId=1001&name=Python&page=1&pageSize=20'
);

// 获取特定层级的空间
const level1Spaces = await fetch(
  '/api/v1/knowledge-spaces?userId=1001&level=1&page=1&pageSize=50'
);
```