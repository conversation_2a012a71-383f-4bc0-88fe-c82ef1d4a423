# AI标签系统API文档

## 概述

基于数据库表结构实现的AI标签系统，提供标签的增删改查功能以及应用与标签的关联管理。

## 数据库表结构

### ai_tags 标签表
- `id`: 标签ID (主键)
- `tenant_id`: 租户编号 (默认: '8')
- `name`: 标签名称 (必填)
- `slug`: 标签标识 (必填，唯一)
- `description`: 标签描述
- `color`: 标签颜色
- `sort`: 排序 (默认: 0)
- `post_count`: 使用次数 (默认: 0)
- `is_hot`: 是否热门标签 (1热门 2不热门，默认: 2)
- `deleted`: 删除标志 (默认: false)
- `creator`: 创建人
- `create_time`: 创建时间
- `updater`: 更新人
- `update_time`: 更新时间

### ai_application_tags 应用标签关联表
- `application_id`: 应用ID (主键)
- `tag_id`: 标签ID (主键)
- `tenant_id`: 租户编号 (默认: '8')
- `deleted`: 删除标志 (默认: false)
- `creator`: 创建人
- `create_time`: 创建时间
- `updater`: 更新人
- `update_time`: 更新时间

## API接口

### 标签基础CRUD

#### 1. 获取标签列表
```
GET /multiagent/tags
```

**查询参数:**
- `pageNo`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20, 最大: 100)
- `keyword`: 搜索关键词 (搜索名称和描述)
- `is_hot`: 是否热门标签 (1热门 2不热门)
- `tenant_id`: 租户ID

**响应示例:**
```json
{
  "code": 200,
  "message": "获取标签列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "tenant_id": "8",
        "name": "人工智能",
        "slug": "ai",
        "description": "人工智能相关应用",
        "color": "#FF0000",
        "sort": 0,
        "post_count": 5,
        "is_hot": 1,
        "create_time": "2024-01-01T00:00:00",
        "update_time": "2024-01-01T00:00:00"
      }
    ],
    "total": 10,
    "pageNo": 1,
    "pageSize": 20,
    "pages": 1
  }
}
```

#### 2. 创建标签
```
POST /multiagent/tags
```

**请求体:**
```json
{
  "name": "机器学习",
  "slug": "machine-learning",
  "description": "机器学习相关应用",
  "color": "#00FF00",
  "sort": 1,
  "is_hot": 2
}
```

**响应:** 201 Created

#### 3. 根据ID获取标签
```
GET /multiagent/tags/{tag_id}
```

**响应:** 200 OK 或 404 Not Found

#### 4. 更新标签
```
PUT /multiagent/tags/{tag_id}
```

**请求体:** 同创建标签，所有字段可选

**响应:** 200 OK 或 404 Not Found

#### 5. 删除标签
```
DELETE /multiagent/tags/{tag_id}
```

**响应:** 200 OK 或 404 Not Found

#### 6. 根据slug获取标签
```
GET /multiagent/tags/by-slug/{slug}
```

**响应:** 200 OK 或 404 Not Found

### 应用标签关联管理

#### 7. 为应用添加标签
```
POST /multiagent/tags/applications/{application_id}/tags
```

**请求体:**
```json
{
  "tag_id": 1
}
```

**响应:** 201 Created 或 409 Conflict (关联已存在)

#### 8. 移除应用标签
```
DELETE /multiagent/tags/applications/{application_id}/tags/{tag_id}
```

**响应:** 200 OK 或 404 Not Found

#### 9. 获取应用的所有标签
```
GET /multiagent/tags/applications/{application_id}/tags
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取应用标签成功",
  "data": [
    {
      "id": 1,
      "name": "人工智能",
      "slug": "ai",
      "description": "人工智能相关应用",
      "color": "#FF0000"
    }
  ]
}
```

### 根据标签获取应用

#### 10. 根据标签slug获取应用列表
```
GET /multiagent/tags/applications?slug={slug}
GET /multiagent/application/by-tag?slug={slug}
```

**查询参数:**
- `slug`: 标签标识 (可选，不传递时获取所有应用)
- `pageNo`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20, 最大: 100)

**功能说明:**
- 不传递`slug`参数：获取所有应用
- 传递`slug`参数：获取该标签下的所有应用

**响应示例:**
```json
{
  "code": 200,
  "message": "获取应用列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "app_key": "chatbot-ai",
        "name": "智能聊天机器人",
        "description": "基于AI的智能对话系统",
        "app_type": 1,
        "status": 1
      }
    ],
    "total": 5,
    "pageNo": 1,
    "pageSize": 20,
    "pages": 1
  }
}
```

## 使用示例

### 1. 创建标签并关联应用

```bash
# 1. 创建标签
curl -X POST http://localhost:9000/multiagent/tags \
  -H "Content-Type: application/json" \
  -d '{
    "name": "自然语言处理",
    "slug": "nlp",
    "description": "自然语言处理相关应用",
    "color": "#0066CC"
  }'

# 2. 为应用添加标签
curl -X POST http://localhost:9000/multiagent/tags/applications/1/tags \
  -H "Content-Type: application/json" \
  -d '{"tag_id": 1}'
```

### 2. 根据标签获取应用

```bash
# 获取所有应用
curl "http://localhost:9000/multiagent/application/by-tag"

# 获取特定标签的应用
curl "http://localhost:9000/multiagent/application/by-tag?slug=nlp"
```

## 错误处理

所有API都遵循统一的错误响应格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

常见错误码：
- `400`: 请求参数错误
- `404`: 资源不存在
- `409`: 资源冲突 (如slug已存在)
- `500`: 服务器内部错误

## 注意事项

1. 所有删除操作都是软删除，不会物理删除数据
2. 标签的`slug`字段必须唯一
3. 删除标签时会同时删除相关的应用标签关联
4. 添加应用标签关联时会自动增加标签的使用次数
5. 移除应用标签关联时会自动减少标签的使用次数
