# 标签列表API使用说明

## 新增接口

### GET /multiagent/tags/list

获取标签列表（不分页），支持通过 `limit` 参数指定获取数量。

## 接口参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | int | 否 | 20 | 获取数量限制，最大100 |
| keyword | string | 否 | - | 搜索关键词（搜索名称和描述） |
| is_hot | int | 否 | - | 是否热门标签（1热门 2不热门） |
| tenant_id | string | 否 | - | 租户ID |

## 响应格式

```json
{
  "code": 200,
  "message": "获取标签列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "tenant_id": "8",
        "name": "人工智能",
        "slug": "ai",
        "description": "人工智能相关应用",
        "color": "#FF0000",
        "sort": 0,
        "post_count": 5,
        "is_hot": 1,
        "create_time": "2024-01-01T00:00:00",
        "update_time": "2024-01-01T00:00:00"
      }
    ],
    "total": 10,
    "limit": 20
  }
}
```

## 使用示例

### 1. 获取默认数量的标签（20个）

```bash
curl "http://localhost:9000/multiagent/tags/list"
```

### 2. 获取指定数量的标签

```bash
# 获取10个标签
curl "http://localhost:9000/multiagent/tags/list?limit=10"

# 获取最多100个标签
curl "http://localhost:9000/multiagent/tags/list?limit=100"
```

### 3. 搜索热门标签

```bash
# 获取5个热门标签
curl "http://localhost:9000/multiagent/tags/list?limit=5&is_hot=1"

# 获取10个非热门标签
curl "http://localhost:9000/multiagent/tags/list?limit=10&is_hot=2"
```

### 4. 关键词搜索

```bash
# 搜索包含"AI"的标签
curl "http://localhost:9000/multiagent/tags/list?limit=20&keyword=AI"

# 搜索包含"机器学习"的标签
curl "http://localhost:9000/multiagent/tags/list?keyword=机器学习"
```

### 5. 组合查询

```bash
# 搜索热门的AI相关标签，最多5个
curl "http://localhost:9000/multiagent/tags/list?limit=5&keyword=AI&is_hot=1"

# 指定租户的标签
curl "http://localhost:9000/multiagent/tags/list?limit=10&tenant_id=8"
```

## 与分页接口的区别

| 接口 | URL | 特点 | 适用场景 |
|------|-----|------|----------|
| 分页接口 | `/multiagent/tags` | 返回分页信息（pageNo, pageSize, pages） | 需要分页展示的场景 |
| 列表接口 | `/multiagent/tags/list` | 简单列表，只返回指定数量的数据 | 下拉框、选择器等场景 |

### 分页接口响应示例

```json
{
  "code": 200,
  "message": "获取标签列表成功",
  "data": {
    "list": [...],
    "total": 100,
    "pageNo": 1,
    "pageSize": 20,
    "pages": 5
  }
}
```

### 列表接口响应示例

```json
{
  "code": 200,
  "message": "获取标签列表成功",
  "data": {
    "list": [...],
    "total": 100,
    "limit": 20
  }
}
```

## JavaScript 使用示例

### 使用 fetch API

```javascript
// 获取标签列表
async function getTagsList(limit = 20, keyword = '', isHot = null) {
  const params = new URLSearchParams();
  params.append('limit', limit);
  if (keyword) params.append('keyword', keyword);
  if (isHot !== null) params.append('is_hot', isHot);
  
  try {
    const response = await fetch(`/multiagent/tags/list?${params}`);
    const data = await response.json();
    
    if (data.code === 200) {
      return data.data.list;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('获取标签列表失败:', error);
    return [];
  }
}

// 使用示例
getTagsList(10, 'AI', 1).then(tags => {
  console.log('热门AI标签:', tags);
});
```

### 使用 axios

```javascript
import axios from 'axios';

// 获取标签列表
const getTagsList = async (params = {}) => {
  try {
    const response = await axios.get('/multiagent/tags/list', { params });
    return response.data.data.list;
  } catch (error) {
    console.error('获取标签列表失败:', error);
    return [];
  }
};

// 使用示例
const tags = await getTagsList({ limit: 10, keyword: 'AI' });
```

## 错误处理

### 常见错误

1. **参数错误**
   ```json
   {
     "code": 400,
     "message": "参数错误",
     "data": null
   }
   ```

2. **服务器错误**
   ```json
   {
     "code": 500,
     "message": "获取标签列表失败",
     "data": null
   }
   ```

### 参数验证规则

- `limit`: 必须为正整数，最大值100，小于1时默认为20
- `is_hot`: 只能为1（热门）或2（不热门）
- `keyword`: 字符串类型，支持中英文搜索
- `tenant_id`: 字符串类型

## 性能说明

- 接口支持数据库索引优化
- 建议 `limit` 参数不要设置过大（推荐≤50）
- 关键词搜索使用 LIKE 查询，大数据量时可能较慢
- 结果按 `sort` 字段升序，`id` 字段降序排列
