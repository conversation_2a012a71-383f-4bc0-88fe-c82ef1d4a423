# Dify数据库查询API文档

## 概述

本API提供了对Dify数据库中用户、对话和消息数据的查询功能，支持PostgreSQL数据库连接。

## 基础信息

- **基础URL**: `http://localhost:9000/api/dify`
- **数据库**: PostgreSQL
- **认证**: 暂无（根据需要可添加）
- **响应格式**: JSON

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": {...},
    "code": 200
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误信息",
    "data": null,
    "code": 400
}
```

## API端点

### 1. 健康检查

**GET** `/health`

检查服务状态。

**响应示例**:
```json
{
    "success": true,
    "message": "服务正常",
    "data": {
        "status": "healthy",
        "service": "dify-api"
    },
    "code": 200
}
```

### 2. 根据用户信息查询对话会话和消息记录

**GET** `/users/{user_id}/conversations`

根据用户ID查询该用户的对话会话列表，每个对话包含最新的消息记录。

**路径参数**:
- `user_id` (string): 用户ID（UUID格式）

**查询参数**:
- `app_id` (string, 可选): 应用ID，用于过滤特定应用的对话
- `limit` (integer, 可选): 返回数量限制，默认20，最大100
- `offset` (integer, 可选): 偏移量，默认0

**响应示例**:
```json
{
    "success": true,
    "message": "成功获取用户对话和消息记录",
    "data": {
        "user_info": {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "测试用户",
            "type": "web",
            "is_anonymous": false,
            "session_id": "test_session_001",
            "created_at": "2024-01-01T10:00:00"
        },
        "conversations": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "name": "测试对话",
                "mode": "chat",
                "status": "normal",
                "created_at": "2024-01-01T10:00:00",
                "dialogue_count": 5,
                "messages": [
                    {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "query": "这是一个测试问题",
                        "answer": "这是一个测试回答",
                        "created_at": "2024-01-01T10:01:00"
                    }
                ],
                "message_count": 10,
                "latest_message_time": "2024-01-01T10:01:00"
            }
        ],
        "totalConversations": 1,
        "totalMessages": 10
    },
    "code": 200
}
```

### 3. 根据对话会话查询消息记录

**GET** `/conversations/{conversation_id}/messages`

根据对话ID查询该对话的所有消息记录。

**路径参数**:
- `conversation_id` (string): 对话ID（UUID格式）

**查询参数**:
- `limit` (integer, 可选): 返回数量限制，默认50，最大200
- `offset` (integer, 可选): 偏移量，默认0
- `include_context` (boolean, 可选): 是否包含上下文统计信息，默认true

**响应示例**:
```json
{
    "success": true,
    "message": "成功获取对话消息记录",
    "data": {
        "conversation_info": {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "测试对话",
            "mode": "chat",
            "status": "normal",
            "created_at": "2024-01-01T10:00:00",
            "dialogue_count": 5
        },
        "user_info": {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "测试用户"
        },
        "messages": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "query": "这是一个测试问题",
                "answer": "这是一个测试回答",
                "message_tokens": 10,
                "answer_tokens": 15,
                "total_price": 0.025,
                "currency": "USD",
                "provider_response_latency": 1.5,
                "created_at": "2024-01-01T10:01:00"
            }
        ],
        "total_messages": 1,
        "conversation_stats": {
            "total_tokens": 25,
            "total_cost": 0.025,
            "avg_response_time": 1.5,
            "message_count": 1
        }
    },
    "code": 200
}
```

### 4. 搜索对话会话

**POST** `/conversations/search`

根据多种条件搜索对话会话。

**请求体**:
```json
{
    "app_id": "550e8400-e29b-41d4-a716-************",
    "from_source": "api",
    "from_end_user_id": "550e8400-e29b-41d4-a716-************",
    "from_account_id": "550e8400-e29b-41d4-a716-************",
    "status": "normal",
    "mode": "chat",
    "name_keyword": "测试",
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-01-31T23:59:59Z",
    "limit": 20,
    "offset": 0
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "成功搜索对话会话",
    "data": {
        "conversations": [
            {
                "id": "550e8400-e29b-41d4-a716-************",
                "name": "测试对话",
                "mode": "chat",
                "status": "normal",
                "created_at": "2024-01-01T10:00:00",
                "message_count": 10,
                "latest_message": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "query": "这是一个测试问题...",
                    "answer": "这是一个测试回答...",
                    "created_at": "2024-01-01T10:01:00"
                }
            }
        ],
        "total_count": 1,
        "search_params": {...}
    },
    "code": 200
}
```

### 5. 获取用户统计信息

**GET** `/users/{user_id}/statistics`

获取用户的详细统计信息，包括对话数量、消息数量、token使用量等。

**路径参数**:
- `user_id` (string): 用户ID（UUID格式）

**查询参数**:
- `app_id` (string, 可选): 应用ID，用于过滤特定应用的统计

**响应示例**:
```json
{
    "success": true,
    "message": "成功获取用户统计信息",
    "data": {
        "user_info": {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "测试用户"
        },
        "statistics": {
            "totalConversations": 5,
            "total_messages": 50,
            "total_tokens": 1250,
            "total_cost": 1.25,
            "avg_response_time": 2.3,
            "active_days": 10
        },
        "daily_stats": {
            "2024-01-01": {
                "message_count": 10,
                "total_tokens": 250,
                "total_cost": 0.25
            }
        }
    },
    "code": 200
}
```

### 6. 创建对话会话

**POST** `/conversations`

创建新的对话会话。

**请求体**:
```json
{
    "app_id": "550e8400-e29b-41d4-a716-************",
    "mode": "chat",
    "name": "新对话",
    "from_source": "api",
    "from_end_user_id": "550e8400-e29b-41d4-a716-************",
    "inputs": {},
    "status": "normal"
}
```

### 7. 创建消息

**POST** `/messages`

创建新的消息记录。

**请求体**:
```json
{
    "app_id": "550e8400-e29b-41d4-a716-************",
    "conversation_id": "550e8400-e29b-41d4-a716-************",
    "query": "用户问题",
    "answer": "AI回答",
    "message": {"role": "user", "content": "用户问题"},
    "inputs": {},
    "from_source": "api",
    "from_end_user_id": "550e8400-e29b-41d4-a716-************",
    "currency": "USD",
    "message_unit_price": 0.001,
    "answer_unit_price": 0.001
}
```

## 错误代码

- `400`: 请求参数错误
- `404`: 资源不存在
- `405`: 方法不允许
- `500`: 服务器内部错误

## 数据库配置

使用`config/config.dify.json`配置PostgreSQL连接：

```json
{
  "env": "dify",
  "debug": true,
  "host": "**************",
  "port": 5444,
  "user": "postgres",
  "password": "difyai123456",
  "database": "dify",
  "database_type": "postgresql"
}
```

## 启动服务

```bash
# 安装PostgreSQL依赖
pip install psycopg2-binary

# 启动服务
python run.py dify
```

## 测试

运行测试脚本：

```bash
python tests/test_dify_api.py
```

---

# Knowledge Spaces API 文档

## 概述

Knowledge Spaces API提供了知识空间管理的完整功能，包括知识空间、源文件和处理任务的增删改查操作。

## 基础信息

- **基础URL**: `http://127.0.0.1:9000/ai-answer/knowledge-spaces`
- **认证方式**: Bearer Token
- **必要请求头**:
  - `Authorization: bearer xinli-stock-2025072301-cc`
  - `Device-Id: 20250514180501`
  - `company-id: 0`
  - `Tenant-ID: 8`
  - `Content-Type: application/json`

---

## 1. 知识空间管理 API

### 1.1 获取知识空间列表

**GET** `/`

获取知识空间列表，支持分页和多条件查询。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/?userId=1&page=1&pageSize=20&parentId=0&includeChildren=false' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `userId` (int, 必填): 用户ID
- `page` (int, 可选): 页码，默认1
- `pageSize` (int, 可选): 每页大小，默认20，最大100
- `parentId` (int, 可选): 父空间ID，0表示顶级空间
- `name` (string, 可选): 名称搜索
- `level` (int, 可选): 层级深度
- `status` (int, 可选): 状态，1=启用，0=禁用
- `includeChildren` (boolean, 可选): 是否包含子空间信息
- `orderBy` (string, 可选): 排序字段，默认create_time
- `orderDesc` (boolean, 可选): 是否倒序，默认true

### 1.2 获取知识空间树结构

**GET** `/tree`

获取知识空间的树形结构。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/tree?userId=1&maxLevel=3' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `userId` (int, 必填): 用户ID
- `maxLevel` (int, 可选): 最大层级深度

### 1.3 根据ID获取知识空间详情

**GET** `/{space_id}`

根据空间ID获取详细信息。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/1?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `space_id` (int): 空间ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

### 1.4 创建知识空间

**POST** `/`

创建新的知识空间。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "userId": 1,
    "parentId": 0,
    "name": "测试知识空间",
    "description": "这是一个测试知识空间",
    "creator": 1
}'
```

**请求体参数**:
- `userId` (int, 必填): 用户ID
- `parentId` (int, 可选): 父空间ID
- `name` (string, 必填): 空间名称
- `description` (string, 可选): 空间描述
- `creator` (int, 必填): 创建人ID

### 1.5 更新知识空间

**PUT** `/{space_id}`

更新指定的知识空间信息。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/1?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "name": "更新后的知识空间名称",
    "description": "更新后的描述",
    "status": 1,
    "updater": 1
}'
```

**路径参数**:
- `space_id` (int): 空间ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

**请求体参数**:
- `name` (string, 可选): 空间名称
- `description` (string, 可选): 空间描述
- `status` (int, 可选): 状态
- `updater` (int, 必填): 更新人ID

### 1.6 移动知识空间

**PUT** `/{space_id}/move`

移动知识空间到指定的父空间下。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/1/move?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "targetParentId": 2,
    "updater": 1
}'
```

**路径参数**:
- `space_id` (int): 要移动的空间ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

**请求体参数**:
- `targetParentId` (int, 可选): 目标父空间ID，null表示移到顶级
- `updater` (int, 必填): 更新人ID

### 1.7 删除知识空间

**DELETE** `/{space_id}`

删除指定的知识空间。

```bash
curl --location --request DELETE 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/1?userId=1&deleter=1&hardDelete=false' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `space_id` (int): 空间ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证
- `deleter` (int, 必填): 删除人ID
- `hardDelete` (boolean, 可选): 是否硬删除，默认false

### 1.8 恢复已删除的知识空间

**PUT** `/{space_id}/restore`

恢复已删除的知识空间。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/1/restore?userId=1&updater=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `space_id` (int): 空间ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证
- `updater` (int, 必填): 更新人ID

### 1.9 获取知识空间统计信息

**GET** `/statistics`

获取用户的知识空间统计信息。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/statistics?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `userId` (int, 必填): 用户ID

### 1.10 获取指定空间的子空间列表

**GET** `/{space_id}/children`

获取指定空间下的子空间列表。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/1/children?userId=1&includeGrandchildren=false' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `space_id` (int): 父空间ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证
- `includeGrandchildren` (boolean, 可选): 是否包含孙子空间信息

---

## 2. 源文件管理 API

### 2.1 创建源文件

**POST** `/api/source-files/`

创建新的源文件记录。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/source-files' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "knowledge_spaces_id": 1,
    "file_name": "测试文档.pdf",
    "file_path": "/uploads/documents/test.pdf",
    "file_size": 1024000,
    "file_format": "pdf",
    "creator": 1
}'
```

**请求体参数**:
- `knowledge_spaces_id` (int, 必填): 知识空间ID
- `file_name` (string, 必填): 文件名
- `file_path` (string, 必填): 文件路径
- `file_size` (int, 必填): 文件大小（字节）
- `file_format` (string, 必填): 文件格式
- `creator` (int, 必填): 创建人ID

### 2.2 获取源文件详情

**GET** `/api/source-files/{file_id}`

根据文件ID获取文件详细信息。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/api/source-files/1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `file_id` (int): 文件ID

### 2.3 获取知识空间的文件列表

**GET** `/api/source-files/knowledge-space/{knowledge_spaces_id}`

获取指定知识空间下的所有文件。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/api/source-files/knowledge-space/1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `knowledge_spaces_id` (int): 知识空间ID

### 2.4 搜索源文件

**POST** `/api/source-files/search`

根据多种条件搜索源文件。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/search' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "knowledge_spaces_id": 1,
    "file_name": "测试",
    "file_format": "pdf",
    "upload_status": 3,
    "page": 1,
    "pageSize": 20
}'
```

**请求体参数**:
- `knowledge_spaces_id` (int, 可选): 知识空间ID
- `file_name` (string, 可选): 文件名搜索
- `file_format` (string, 可选): 文件格式过滤
- `upload_status` (int, 可选): 上传状态，1=上传中，2=上传失败，3=上传完成
- `page` (int, 可选): 页码，默认1
- `pageSize` (int, 可选): 每页大小，默认20

### 2.5 更新源文件

**PUT** `/api/source-files/{file_id}`

更新指定的源文件信息。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/api/source-files/1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "file_name": "更新后的文件名.pdf",
    "upload_status": 3,
    "updater": 1
}'
```

**路径参数**:
- `file_id` (int): 文件ID

**请求体参数**:
- `file_name` (string, 可选): 文件名
- `upload_status` (int, 可选): 上传状态
- `updater` (int, 必填): 更新人ID

### 2.6 批量更新文件状态

**POST** `/api/source-files/batch/update-status`

批量更新多个文件的状态。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/batch/update-status' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "file_ids": [1, 2, 3],
    "upload_status": 3,
    "updater": 1
}'
```

**请求体参数**:
- `file_ids` (array, 必填): 文件ID列表
- `upload_status` (int, 必填): 目标状态
- `updater` (int, 必填): 更新人ID

### 2.7 删除源文件

**DELETE** `/api/source-files/{file_id}`

删除指定的源文件。

```bash
curl --location --request DELETE 'http://127.0.0.1:9000/ai-answer/api/source-files/1?deleter=1&hard_delete=false' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `file_id` (int): 文件ID

**查询参数**:
- `deleter` (int, 必填): 删除人ID
- `hard_delete` (boolean, 可选): 是否硬删除，默认false

### 2.8 批量删除源文件

**POST** `/api/source-files/batch/delete`

批量删除多个源文件。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/batch/delete?hard_delete=false' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "file_ids": [1, 2, 3],
    "deleter": 1
}'
```

**查询参数**:
- `hard_delete` (boolean, 可选): 是否硬删除，默认false

**请求体参数**:
- `file_ids` (array, 必填): 文件ID列表
- `deleter` (int, 必填): 删除人ID

### 2.9 获取文件统计信息

**GET** `/api/source-files/statistics`

获取文件的统计信息。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/api/source-files/statistics?knowledge_spaces_id=1&creator_id=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `knowledge_spaces_id` (int, 可选): 知识空间ID
- `creator_id` (int, 可选): 创建人ID

### 2.10 验证文件上传

**POST** `/api/source-files/upload/validate`

验证文件上传的合法性。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/upload/validate' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "file_format": "pdf",
    "file_size": 1024000,
    "max_size_mb": 100
}'
```

**请求体参数**:
- `file_format` (string, 必填): 文件格式
- `file_size` (int, 必填): 文件大小（字节）
- `max_size_mb` (int, 可选): 最大允许大小（MB），默认100

### 2.11 根据上传状态获取文件列表

**GET** `/api/source-files/status/{upload_status}`

根据上传状态筛选文件。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/api/source-files/status/3?limit=50' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `upload_status` (int): 上传状态

**查询参数**:
- `limit` (int, 可选): 限制数量

### 2.12 根据文件格式获取文件列表

**GET** `/api/source-files/format/{file_format}`

根据文件格式筛选文件。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/api/source-files/format/pdf?knowledge_spaces_id=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `file_format` (string): 文件格式

**查询参数**:
- `knowledge_spaces_id` (int, 可选): 知识空间ID

### 2.13 处理文件上传完成

**POST** `/api/source-files/upload/complete/{file_id}`

标记文件上传完成。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/upload/complete/1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "updater": 1
}'
```

**路径参数**:
- `file_id` (int): 文件ID

**请求体参数**:
- `updater` (int, 必填): 更新人ID

### 2.14 处理文件上传失败

**POST** `/api/source-files/upload/fail/{file_id}`

标记文件上传失败。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/upload/fail/1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "error_message": "上传过程中发生错误",
    "updater": 1
}'
```

**路径参数**:
- `file_id` (int): 文件ID

**请求体参数**:
- `error_message` (string, 必填): 错误信息
- `updater` (int, 必填): 更新人ID

### 2.15 清理过期失败上传文件

**POST** `/api/source-files/cleanup/failed`

清理指定天数前的失败上传文件。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/api/source-files/cleanup/failed' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "days_old": 7
}'
```

**请求体参数**:
- `days_old` (int, 可选): 天数，默认7天

---

## 3. 处理任务管理 API

### 3.1 获取处理任务列表

**GET** `/processing-tasks`

获取处理任务列表，支持分页和多条件查询。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks?page=1&pageSize=20&userId=1&taskType=1&status=3' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `page` (int, 可选): 页码，默认1
- `pageSize` (int, 可选): 每页大小，默认20，最大100
- `taskId` (int, 可选): 任务ID
- `sourceFileId` (int, 可选): 源文件ID
- `userId` (int, 可选): 用户ID
- `taskType` (int, 可选): 任务类型，1=文档解析，2=向量化分块，3=生成摘要
- `status` (int, 可选): 任务状态，1=排队中，2=处理中，3=已完成，4=失败
- `startTimeFrom` (string, 可选): 开始时间范围-起始
- `startTimeTo` (string, 可选): 开始时间范围-结束
- `completedTimeFrom` (string, 可选): 完成时间范围-起始
- `completedTimeTo` (string, 可选): 完成时间范围-结束
- `orderBy` (string, 可选): 排序字段，默认create_time
- `orderDesc` (boolean, 可选): 是否倒序，默认true

### 3.2 根据ID获取处理任务详情

**GET** `/processing-tasks/{task_id}`

根据任务ID获取详细信息。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

### 3.3 根据源文件ID获取处理任务列表

**GET** `/processing-tasks/file/{source_file_id}`

获取指定源文件的所有处理任务。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/file/1?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `source_file_id` (int): 源文件ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

### 3.4 根据用户ID获取处理任务列表

**GET** `/processing-tasks/user/{user_id}`

获取指定用户的所有处理任务。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/user/1?status=3' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `user_id` (int): 用户ID

**查询参数**:
- `status` (int, 可选): 任务状态过滤

### 3.5 创建处理任务

**POST** `/processing-tasks`

创建新的处理任务。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "sourceFileId": 1,
    "userId": 1,
    "taskType": 1,
    "payload": {"priority": "high"},
    "creator": 1
}'
```

**请求体参数**:
- `sourceFileId` (int, 必填): 源文件ID
- `userId` (int, 必填): 用户ID
- `taskType` (int, 必填): 任务类型，1=文档解析，2=向量化分块，3=生成摘要
- `payload` (object, 可选): 任务载荷
- `creator` (int, 必填): 创建人ID

### 3.6 更新处理任务

**PUT** `/processing-tasks/{task_id}`

更新指定的处理任务信息。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "status": 2,
    "payload": {"progress": 50},
    "updater": 1
}'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

**请求体参数**:
- `status` (int, 可选): 任务状态
- `payload` (object, 可选): 任务载荷
- `errorMessage` (string, 可选): 错误信息
- `startedAt` (string, 可选): 开始执行时间
- `completedAt` (string, 可选): 完成时间
- `updater` (int, 必填): 更新人ID

### 3.7 开始执行任务

**PUT** `/processing-tasks/{task_id}/start`

标记任务开始执行。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1/start?userId=1&updater=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证
- `updater` (int, 必填): 更新人ID

### 3.8 完成任务

**PUT** `/processing-tasks/{task_id}/complete`

标记任务完成。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1/complete?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "payload": {"result": "处理完成"},
    "updater": 1
}'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

**请求体参数**:
- `payload` (object, 可选): 任务结果载荷
- `updater` (int, 必填): 更新人ID

### 3.9 设置任务失败

**PUT** `/processing-tasks/{task_id}/fail`

标记任务执行失败。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1/fail?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "errorMessage": "处理过程中发生错误",
    "updater": 1
}'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

**请求体参数**:
- `errorMessage` (string, 必填): 错误信息
- `updater` (int, 必填): 更新人ID

### 3.10 取消任务

**PUT** `/processing-tasks/{task_id}/cancel`

取消指定的处理任务。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1/cancel?userId=1&updater=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证
- `updater` (int, 必填): 更新人ID

### 3.11 重试任务

**PUT** `/processing-tasks/{task_id}/retry`

重新执行失败的任务。

```bash
curl --location --request PUT 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1/retry?userId=1&updater=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证
- `updater` (int, 必填): 更新人ID

### 3.12 批量操作任务

**POST** `/processing-tasks/batch`

批量执行任务操作（取消/重试/删除）。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/batch?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "taskIds": [1, 2, 3],
    "action": "cancel",
    "updater": 1
}'
```

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

**请求体参数**:
- `taskIds` (array, 必填): 任务ID列表
- `action` (string, 必填): 操作类型，cancel/retry/delete
- `updater` (int, 必填): 操作人ID

### 3.13 删除任务

**DELETE** `/processing-tasks/{task_id}`

删除指定的处理任务。

```bash
curl --location --request DELETE 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/1?userId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**路径参数**:
- `task_id` (int): 任务ID

**查询参数**:
- `userId` (int, 可选): 用户ID，用于权限验证

### 3.14 获取排队中的任务列表

**GET** `/processing-tasks/queue`

获取等待执行的任务列表，用于任务调度。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/queue?taskType=1&limit=10' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `taskType` (int, 可选): 任务类型过滤
- `limit` (int, 可选): 限制数量，默认10，最大100

### 3.15 获取超时的处理中任务

**GET** `/processing-tasks/timeout`

获取执行超时的任务列表。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/timeout?timeoutMinutes=30' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `timeoutMinutes` (int, 可选): 超时时间（分钟），默认30

### 3.16 获取任务统计信息

**GET** `/processing-tasks/statistics`

获取任务的统计信息。

```bash
curl --location --request GET 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/statistics?userId=1&sourceFileId=1' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8'
```

**查询参数**:
- `userId` (int, 可选): 用户ID
- `sourceFileId` (int, 可选): 源文件ID

### 3.17 清理旧任务记录

**POST** `/processing-tasks/cleanup`

清理指定时间前的旧任务记录。

```bash
curl --location --request POST 'http://127.0.0.1:9000/ai-answer/knowledge-spaces/processing-tasks/cleanup' \
--header 'Authorization: bearer xinli-stock-2025072301-cc' \
--header 'Device-Id: 20250514180501' \
--header 'company-id: 0' \
--header 'Tenant-ID: 8' \
--header 'Content-Type: application/json' \
--data-raw '{
    "days": 30,
    "status": 3
}'
```

**请求体参数**:
- `days` (int, 必填): 天数，超过此时间的任务将被删除
- `status` (int, 可选): 任务状态过滤，为空时清理所有已完成和失败的任务

---

## 错误代码说明

### HTTP状态码
- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `405`: 请求方法不被允许
- `409`: 冲突（如重名、状态不允许等）
- `500`: 服务器内部错误

### 业务错误码
Knowledge Spaces API返回的错误信息会在响应体的`message`字段中详细说明具体的错误原因。

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": {...},
    "timestamp": "2024-01-01T10:00:00.000Z"
}
```

### 分页响应
```json
{
    "success": true,
    "message": "获取成功",
    "data": [...],
    "pagination": {
        "total": 100,
        "page": 1,
        "pageSize": 20,
        "totalPages": 5
    },
    "timestamp": "2024-01-01T10:00:00.000Z"
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误信息描述",
    "data": null,
    "timestamp": "2024-01-01T10:00:00.000Z",
    "code": 400
}
```
