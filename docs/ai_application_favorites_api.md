# AI应用收藏功能API文档

## 概述

AI应用收藏功能提供了用户收藏和管理AI应用的完整API接口，包括添加收藏、取消收藏、检查收藏状态、获取收藏列表等功能。

## 数据库表结构

```sql
CREATE TABLE `ai_application_favorites` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '8' COMMENT '租户编号',
  `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户设备ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `application_id` bigint NOT NULL COMMENT '应用ID',
  `creator` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_user_app` (`device_id`,`user_id`,`application_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI应用收藏表';
```

## API接口

### 1. 添加收藏

**接口地址：** `POST /ai-answer/favorites`

**请求参数：**
```json
{
  "device_id": "设备ID",
  "user_id": 用户ID,
  "application_id": 应用ID,
  "tenant_id": "租户ID" // 可选，默认为"8"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "收藏成功",
  "data": {
    "id": 1,
    "tenant_id": "8",
    "device_id": "device123",
    "user_id": 1,
    "application_id": 1,
    "creator": "system",
    "create_time": "2024-01-01T10:00:00",
    "updater": null,
    "update_time": "2024-01-01T10:00:00"
  }
}
```

**错误响应：**
- `409 Conflict`: 应用已收藏或应用不存在
- `400 Bad Request`: 参数验证失败

### 2. 取消收藏

**接口地址：** `DELETE /ai-answer/favorites`

**查询参数：**
- `device_id`: 设备ID (必填)
- `user_id`: 用户ID (必填)
- `application_id`: 应用ID (必填)

**响应示例：**
```json
{
  "code": 200,
  "message": "取消收藏成功",
  "data": null
}
```

**错误响应：**
- `404 Not Found`: 收藏记录不存在
- `400 Bad Request`: 参数错误

### 3. 检查收藏状态

**接口地址：** `GET /ai-answer/favorites/check`

**查询参数：**
- `device_id`: 设备ID (必填)
- `user_id`: 用户ID (必填)
- `application_id`: 应用ID (必填)

**响应示例：**
```json
{
  "code": 200,
  "message": "检查收藏状态成功",
  "data": {
    "is_favorite": true
  }
}
```

### 4. 获取用户收藏列表

**接口地址：** `GET /ai-answer/favorites/list`

**查询参数：**
- `device_id`: 设备ID (必填)
- `user_id`: 用户ID (必填)
- `pageNo`: 页码 (可选，默认1)
- `pageSize`: 每页大小 (可选，默认20，最大100)

**响应示例：**
```json
{
  "code": 200,
  "message": "获取收藏列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "tenant_id": "8",
        "device_id": "device123",
        "user_id": 1,
        "application_id": 1,
        "create_time": "2024-01-01T10:00:00",
        "app_key": "chat_assistant",
        "app_name": "智能聊天助手",
        "app_logo_url": "https://example.com/logo.png",
        "app_description": "智能聊天助手应用",
        "app_type": 1,
        "app_status": 1
      }
    ],
    "total": 1,
    "pageNo": 1,
    "pageSize": 20,
    "pages": 1
  }
}
```

### 5. 获取收藏数量统计

**接口地址：** `GET /ai-answer/favorites/count`

**查询参数：**

**获取用户收藏总数：**
- `type`: "user" (必填)
- `device_id`: 设备ID (必填)
- `user_id`: 用户ID (必填)

**获取应用收藏数量：**
- `type`: "app" (必填)
- `application_id`: 应用ID (必填)

**响应示例：**
```json
{
  "code": 200,
  "message": "获取收藏数量成功",
  "data": {
    "count": 5
  }
}
```

## 使用示例

### JavaScript/Ajax示例

```javascript
// 添加收藏
function addFavorite(deviceId, userId, applicationId) {
    fetch('/ai-answer/favorites', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            user_id: userId,
            application_id: applicationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            console.log('收藏成功');
        } else {
            console.error('收藏失败:', data.message);
        }
    });
}

// 取消收藏
function removeFavorite(deviceId, userId, applicationId) {
    fetch(`/ai-answer/favorites?device_id=${deviceId}&user_id=${userId}&application_id=${applicationId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            console.log('取消收藏成功');
        } else {
            console.error('取消收藏失败:', data.message);
        }
    });
}

// 检查收藏状态
function checkFavorite(deviceId, userId, applicationId) {
    fetch(`/ai-answer/favorites/check?device_id=${deviceId}&user_id=${userId}&application_id=${applicationId}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            console.log('是否已收藏:', data.data.is_favorite);
        }
    });
}

// 获取收藏列表
function getFavoritesList(deviceId, userId, pageNo = 1, pageSize = 20) {
    fetch(`/ai-answer/favorites/list?device_id=${deviceId}&user_id=${userId}&pageNo=${pageNo}&pageSize=${pageSize}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            console.log('收藏列表:', data.data.list);
            console.log('总数:', data.data.total);
        }
    });
}
```

### Python示例

```python
import requests

# 添加收藏
def add_favorite(device_id, user_id, application_id):
    url = "http://localhost:5000/ai-answer/favorites"
    data = {
        "device_id": device_id,
        "user_id": user_id,
        "application_id": application_id
    }
    response = requests.post(url, json=data)
    return response.json()

# 取消收藏
def remove_favorite(device_id, user_id, application_id):
    url = f"http://localhost:5000/ai-answer/favorites"
    params = {
        "device_id": device_id,
        "user_id": user_id,
        "application_id": application_id
    }
    response = requests.delete(url, params=params)
    return response.json()

# 检查收藏状态
def check_favorite(device_id, user_id, application_id):
    url = f"http://localhost:5000/ai-answer/favorites/check"
    params = {
        "device_id": device_id,
        "user_id": user_id,
        "application_id": application_id
    }
    response = requests.get(url, params=params)
    return response.json()
```

## 注意事项

1. **唯一性约束**: 同一用户（device_id + user_id）对同一应用只能收藏一次
2. **应用存在性检查**: 添加收藏时会检查应用是否存在且未删除
3. **分页限制**: 每页最大100条记录
4. **错误处理**: 所有接口都有完整的错误处理和标准化响应格式
5. **审计字段**: 自动记录创建时间、更新时间和操作人信息
