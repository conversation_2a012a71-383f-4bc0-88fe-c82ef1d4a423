# 技能中心API接口文档

## 概述

新增了专门查询技能中心应用的API接口，用于获取标记为技能中心的AI应用列表。

## 接口详情

### 获取技能中心应用列表

**接口地址：** `GET /multiagent/application/skill-center`

**功能描述：** 获取所有标记为技能中心的AI应用列表，支持分页、关键词搜索和状态过滤。

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNo | int | 否 | 1 | 页码，从1开始 |
| pageSize | int | 否 | 20 | 每页大小，最大100 |
| keyword | string | 否 | - | 搜索关键词，支持应用名称和描述搜索 |
| status | int | 否 | - | 应用状态过滤 (1:启用, 0:禁用) |
| app_type | int | 否 | - | 应用类型过滤 |

#### 响应格式

**成功响应 (200):**

```json
{
  "code": 200,
  "message": "获取技能中心应用列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "app_key": "skill_app_001",
        "name": "智能写作助手",
        "description": "基于AI的智能写作工具",
        "logo_url": "https://example.com/logo.png",
        "app_type": 1,
        "status": 1,
        "is_skill_center": 1,
        "visibility": 2,
        "is_public": 1,
        "is_featured": 0,
        "api_base_url": "https://api.example.com",
        "tenant_id": 8,
        "company_id": 0,
        "version": "1.0.0",
        "views_count": 100,
        "likes_count": 25,
        "shares_count": 5,
        "create_time": "2024-01-01T10:00:00",
        "update_time": "2024-01-15T15:30:00",
        "creator": "admin",
        "updater": "admin"
      }
    ],
    "total": 50,
    "pageNo": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```

**错误响应 (500):**

```json
{
  "code": 500,
  "message": "获取技能中心应用列表失败"
}
```

#### 请求示例

```bash
# 获取第一页技能中心应用
curl -X GET "http://localhost:5000/multiagent/application/skill-center?pageNo=1&pageSize=10"

# 搜索包含"AI"关键词的技能中心应用
curl -X GET "http://localhost:5000/multiagent/application/skill-center?keyword=AI&pageNo=1&pageSize=20"

# 获取状态为启用的技能中心应用
curl -X GET "http://localhost:5000/multiagent/application/skill-center?status=1&pageNo=1&pageSize=20"

# 组合查询：搜索关键词并过滤状态
curl -X GET "http://localhost:5000/multiagent/application/skill-center?keyword=写作&status=1&pageNo=1&pageSize=10"
```

## 实现细节

### 服务层方法

在 `AIApplicationService` 类中新增了 `get_skill_center_applications` 方法：

```python
def get_skill_center_applications(self,
                                keyword: Optional[str] = None,
                                status: Optional[int] = None,
                                app_type: Optional[int] = None,
                                limit: int = 20,
                                offset: int = 0) -> Tuple[List[AIApplicationVO], int]:
    """
    获取技能中心应用列表（带分页和总数）
    
    Args:
        keyword: 搜索关键词
        status: 应用状态过滤
        app_type: 应用类型过滤
        limit: 每页大小
        offset: 偏移量
        
    Returns:
        Tuple[List[AIApplicationVO], int]: 应用列表和总数
    """
```

### 控制器路由

在 `ai_application_controller.py` 中新增了 `/skill-center` 路由：

```python
@ai_application_bp.route('/skill-center', methods=['GET'])
@handle_exceptions
@log_request
def get_skill_center_applications():
    """获取技能中心应用列表"""
```

### 核心特性

1. **自动过滤技能中心应用：** 强制设置 `is_skill_center=1` 过滤条件
2. **复用现有查询逻辑：** 基于现有的 `search_applications_with_count` 方法
3. **支持分页：** 标准的分页参数和响应格式
4. **支持搜索：** 关键词搜索应用名称和描述
5. **支持过滤：** 按状态、应用类型等条件过滤
6. **异常处理：** 完整的异常处理和错误响应
7. **日志记录：** 请求日志和错误日志

## 测试

可以使用提供的测试脚本 `test_skill_center_api.py` 进行接口测试：

```bash
python test_skill_center_api.py
```

## 注意事项

1. 该接口只返回 `is_skill_center=1` 的应用
2. 支持的查询参数与普通应用列表接口基本一致
3. 返回的数据格式与普通应用列表接口完全一致
4. 接口路径为 `/multiagent/application/skill-center`
