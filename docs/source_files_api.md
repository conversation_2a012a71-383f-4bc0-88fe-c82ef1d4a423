# 源文件 API 文档

## 概述

源文件模块提供了完整的文件管理功能，支持文件的上传记录、状态追踪、批量操作和统计分析等功能。

## 数据模型

### SourceFile 基础字段

```json
{
  "id": "integer - 文件ID",
  "knowledgeSpacesId": "integer - 所属知识空间ID",
  "fileName": "string(255) - 系统生成的唯一文件名",
  "originalFileName": "string(255) - 用户上传的原始文件名",
  "url": "string(512) - 文件存储地址",
  "fileFormat": "string(50) - 文件格式(MIME类型)",
  "fileSuffix": "string(50) - 文件后缀名",
  "fileSize": "integer - 文件大小(字节)",
  "uploadStatus": "integer - 上传状态(1=上传中, 2=已完成, 3=失败)",
  "creator": "integer - 创建人ID(上传者)",
  "createTime": "datetime - 创建时间",
  "updater": "integer - 更新人ID",
  "updateTime": "datetime - 更新时间",
  "deleted": "boolean - 是否删除",
  "uploadStatusText": "string - 上传状态文本",
  "fileSizeText": "string - 文件大小文本"
}
```

### 上传状态枚举

- `1`: 上传中
- `2`: 已完成  
- `3`: 失败

## API 接口

### 1. 获取源文件列表

```http
GET /api/v1/source-files
```

**Query Parameters:**
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20, 最大: 100)
- `knowledgeSpacesId`: 知识空间ID
- `fileName`: 文件名搜索
- `originalFileName`: 原始文件名搜索
- `fileFormat`: 文件格式过滤
- `fileSuffix`: 文件后缀过滤
- `uploadStatus`: 上传状态过滤 (1=上传中, 2=已完成, 3=失败)
- `creator`: 创建人ID过滤
- `minFileSize`: 最小文件大小(字节)
- `maxFileSize`: 最大文件大小(字节)
- `orderBy`: 排序字段 (默认: create_time)
- `orderDesc`: 是否倒序 (默认: true)

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": [...],
  "total": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5
}
```

### 2. 根据知识空间获取文件列表

```http
GET /api/v1/source-files/knowledge-space/{knowledge_spaces_id}
```

**Path Parameters:**
- `knowledge_spaces_id`: 知识空间ID

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "originalFileName": "技术文档.pdf",
      "fileSize": 1024000,
      "fileSizeText": "1.0MB",
      "uploadStatus": 2,
      "uploadStatusText": "已完成"
    }
  ]
}
```

### 3. 根据创建人获取文件列表

```http
GET /api/v1/source-files/creator/{creator_id}
```

**Path Parameters:**
- `creator_id`: 创建人ID

### 4. 根据ID获取文件详情

```http
GET /api/v1/source-files/{file_id}
```

**Path Parameters:**
- `file_id`: 文件ID

### 5. 创建文件记录

```http
POST /api/v1/source-files
```

**Request Body:**
```json
{
  "knowledgeSpacesId": 1001,
  "fileName": "file_20231201_001.pdf",
  "originalFileName": "技术文档.pdf", 
  "url": "https://example.com/files/file_20231201_001.pdf",
  "fileFormat": "application/pdf",
  "fileSuffix": ".pdf",
  "fileSize": 1024000,
  "uploadStatus": 2,
  "creator": 2001
}
```

**Response:**
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 1,
    "fileName": "file_20231201_001.pdf",
    "originalFileName": "技术文档.pdf",
    "uploadStatusText": "已完成"
  }
}
```

### 6. 更新文件信息

```http
PUT /api/v1/source-files/{file_id}
```

**Request Body:**
```json
{
  "originalFileName": "更新后的文档.pdf",
  "fileSize": 2048000,
  "uploadStatus": 2,
  "updater": 2001
}
```

### 7. 更新文件上传状态

```http
PUT /api/v1/source-files/{file_id}/status
```

**Query Parameters:**
- `uploadStatus`: 上传状态 (必填: 1=上传中, 2=已完成, 3=失败)
- `updater`: 更新人ID (必填)

### 8. 批量更新文件状态

```http
PUT /api/v1/source-files/batch/status
```

**Request Body:**
```json
{
  "fileIds": [1, 2, 3],
  "uploadStatus": 2,
  "updater": 2001
}
```

### 9. 删除文件

```http
DELETE /api/v1/source-files/{file_id}
```

**Query Parameters:**
- `deleter`: 删除人ID (必填)
- `hardDelete`: 是否硬删除 (默认: false)

### 10. 批量删除文件

```http
DELETE /api/v1/source-files/batch
```

**Request Body:**
```json
{
  "fileIds": [1, 2, 3],
  "deleter": 2001
}
```

**Query Parameters:**
- `hardDelete`: 是否硬删除 (默认: false)

### 11. 获取文件统计信息

```http
GET /api/v1/source-files/statistics
```

**Query Parameters:**
- `knowledgeSpacesId`: 知识空间ID (可选)
- `creator`: 创建人ID (可选)

**Response:**
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "totalCount": 100,
    "totalSize": 104857600,
    "totalSizeText": "100.0MB",
    "uploadingCount": 5,
    "completedCount": 90,
    "failedCount": 5,
    "formatStats": {
      "application/pdf": 50,
      "image/png": 30,
      "application/msword": 20
    }
  }
}
```

### 12. 验证文件上传

```http
POST /api/v1/source-files/validate
```

**Request Body:**
```json
{
  "fileFormat": "application/pdf",
  "fileSize": 1024000
}
```

**Query Parameters:**
- `maxSizeMb`: 最大文件大小MB (默认: 100)

### 13. 根据文件格式获取文件

```http
GET /api/v1/source-files/format/{file_format}
```

**Path Parameters:**
- `file_format`: 文件格式（需要URL编码）

**Query Parameters:**
- `limit`: 限制数量 (默认: 100)

### 14. 获取大文件列表

```http
GET /api/v1/source-files/large-files
```

**Query Parameters:**
- `minSizeMb`: 最小文件大小MB (默认: 10)
- `limit`: 限制数量 (默认: 100)

### 15. 清理失败上传记录

```http
POST /api/v1/source-files/cleanup
```

**Query Parameters:**
- `hours`: 清理超过指定小时数的失败记录 (默认: 24)

## 支持的文件格式

- **文档类**: `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- **文本类**: `text/plain`, `text/markdown`, `text/html`
- **数据类**: `application/json`, `application/xml`
- **图片类**: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- **压缩类**: `application/zip`, `application/x-rar-compressed`

## 业务规则

1. **文件名唯一性**: 同一知识空间下系统生成的文件名必须唯一
2. **文件大小限制**: 默认最大支持100MB，可配置
3. **格式限制**: 只支持预定义的安全文件格式
4. **状态管理**: 支持上传状态的完整生命周期管理
5. **软删除**: 默认使用软删除，支持恢复
6. **自动清理**: 支持自动清理长时间失败的上传记录

## 使用示例

### 文件上传流程

```javascript
// 1. 验证文件
const validation = await fetch('/api/v1/source-files/validate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    fileFormat: 'application/pdf',
    fileSize: 1024000
  })
});

// 2. 创建上传中记录
const createResult = await fetch('/api/v1/source-files', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    knowledgeSpacesId: 1001,
    fileName: 'unique_file_name.pdf',
    originalFileName: '用户文档.pdf',
    url: 'temp_url',
    fileFormat: 'application/pdf',
    fileSuffix: '.pdf',
    fileSize: 1024000,
    uploadStatus: 1, // 上传中
    creator: 2001
  })
});

// 3. 执行实际文件上传...

// 4. 更新状态为已完成
await fetch(`/api/v1/source-files/${fileId}/status?uploadStatus=2&updater=2001`, {
  method: 'PUT'
});
```

### 文件管理操作

```javascript
// 获取知识空间的所有文件
const files = await fetch('/api/v1/source-files/knowledge-space/1001');

// 搜索PDF文件
const pdfFiles = await fetch('/api/v1/source-files?fileFormat=application/pdf&page=1&pageSize=20');

// 批量更新状态
await fetch('/api/v1/source-files/batch/status', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    fileIds: [1, 2, 3],
    uploadStatus: 2,
    updater: 2001
  })
});

// 获取统计信息
const stats = await fetch('/api/v1/source-files/statistics?knowledgeSpacesId=1001');
```

### 定时清理任务

```javascript
// 清理24小时前失败的上传记录
await fetch('/api/v1/source-files/cleanup?hours=24', {
  method: 'POST'
});
```

## 错误处理

所有API都返回统一格式的错误响应：

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE"
}
```

**常见错误码:**
- `400`: 请求参数错误或数据验证失败
- `404`: 文件不存在
- `409`: 文件名冲突或业务冲突
- `500`: 服务器内部错误