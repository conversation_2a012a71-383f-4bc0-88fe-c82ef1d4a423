# 股票分析模块

本模块提供股票数据分析和推荐功能，基于多种技术指标计算股票的评分和投资建议。

## 功能特性

- 支持A股、港股、美股、ETF和LOF基金的数据获取和分析
- 计算多种技术指标：MA、RSI、MACD、布林带等
- 综合评分系统和投资建议
- 简单的API接口

## 使用方法

### 1. 将蓝图注册到Flask应用中

```python
from flask import Flask
from stock.stock_analysis_api import stock_bp

app = Flask(__name__)
# 注册股票分析蓝图
app.register_blueprint(stock_bp, url_prefix='/stock')

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080, debug=True)
```

### 2. API接口

#### 分析股票
- 端点：`/stock/analyze-stock`
- 方法：POST
- 认证：Bearer Token
- 请求体示例：
```json
{
  "stock_code": "000001",
  "market_type": "A",
  "start_date": "20240101",
  "end_date": "20240701"
}
```

## 依赖库

- Flask
- pandas
- akshare
- numpy

## 说明

该模块使用akshare获取股票数据，支持多种市场类型的股票和基金数据获取与分析。