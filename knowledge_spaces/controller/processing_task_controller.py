"""
处理任务控制器
提供处理任务的增删改查API接口
"""
import logging
from flask import Blueprint, jsonify, request, g
from http import HTTPStatus
from pydantic import ValidationError

from knowledge_spaces.service.processing_task_service import ProcessingTaskService
from knowledge_spaces.model.processing_task_model import (
    ProcessingTaskCreateRequest, ProcessingTaskUpdateRequest,
    ProcessingTaskQueryRequest, ProcessingTaskBatchRequest
)
from utils.response_utils import success_response, error_response, paginated_response
from utils.decorators import handle_exceptions, log_request

# 创建Blueprint
processing_tasks_bp = Blueprint('processing_tasks', __name__)
service = ProcessingTaskService()


@processing_tasks_bp.route('', methods=['GET'])
@handle_exceptions
@log_request
def get_tasks():
    """
    获取处理任务列表
    
    Query Parameters:
        page: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)
        taskId: 任务ID
        sourceFileId: 源文件ID
        userId: 用户ID
        taskType: 任务类型 (1=文档解析, 2=向量化分块, 3=生成摘要)
        status: 任务状态 (1=排队中, 2=处理中, 3=已完成, 4=失败)
        startTimeFrom: 开始时间范围-起始
        startTimeTo: 开始时间范围-结束
        completedTimeFrom: 完成时间范围-起始
        completedTimeTo: 完成时间范围-结束
        orderBy: 排序字段 (默认: create_time)
        orderDesc: 是否倒序 (默认: true)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = min(request.args.get('pageSize', 20, type=int), 100)
        task_id = request.args.get('taskId', type=int)
        source_file_id = request.args.get('sourceFileId', type=int)
        user_id = request.args.get('userId', type=int)
        task_type = request.args.get('taskType', type=int)
        status = request.args.get('status', type=int)
        start_time_from = request.args.get('startTimeFrom')
        start_time_to = request.args.get('startTimeTo')
        completed_time_from = request.args.get('completedTimeFrom')
        completed_time_to = request.args.get('completedTimeTo')
        order_by = request.args.get('orderBy', 'create_time')
        order_desc = request.args.get('orderDesc', 'true').lower() == 'true'
        
        # 构建查询请求
        query_request = ProcessingTaskQueryRequest(
            taskId=task_id,
            sourceFileId=source_file_id,
            userId=user_id,
            taskType=task_type,
            status=status,
            startTimeFrom=start_time_from,
            startTimeTo=start_time_to,
            completedTimeFrom=completed_time_from,
            completedTimeTo=completed_time_to,
            page=page,
            pageSize=page_size,
            orderBy=order_by,
            orderDesc=order_desc
        )
        
        # 调用服务层
        success, message, result = service.search_tasks(query_request)
        
        if success:
            return paginated_response(
                data=[task.dict() for task in result['list']],
                total=result['total'],
                page=result['page'],
                page_size=result['pageSize'],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except ValidationError as e:
        return error_response(f"参数验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"获取处理任务列表失败: {e}")
        return error_response("获取列表失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>', methods=['GET'])
@handle_exceptions
@log_request
def get_task_by_id(task_id):
    """
    根据ID获取处理任务详情
    
    Path Parameters:
        task_id: 任务ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 获取成功
        404: 任务不存在
        403: 权限不足
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 调用服务层
        success, message, task_vo = service.get_task_by_id(task_id, user_id)
        
        if success:
            return success_response(data=task_vo.dict(), message=message)
        else:
            status_code = HTTPStatus.NOT_FOUND if "不存在" in message else HTTPStatus.FORBIDDEN
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"获取处理任务详情失败: {e}")
        return error_response("获取详情失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/file/<int:source_file_id>', methods=['GET'])
@handle_exceptions
@log_request
def get_tasks_by_file(source_file_id):
    """
    根据源文件ID获取处理任务列表
    
    Path Parameters:
        source_file_id: 源文件ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 调用服务层
        success, message, task_list = service.get_tasks_by_file(source_file_id, user_id)
        
        if success:
            return success_response(
                data=[task.dict() for task in task_list],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"根据文件获取任务列表失败: {e}")
        return error_response("获取任务列表失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/user/<int:user_id>', methods=['GET'])
@handle_exceptions
@log_request
def get_tasks_by_user(user_id):
    """
    根据用户ID获取处理任务列表
    
    Path Parameters:
        user_id: 用户ID
        
    Query Parameters:
        status: 任务状态过滤 (可选)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        status = request.args.get('status', type=int)
        
        # 调用服务层
        success, message, task_list = service.get_tasks_by_user(user_id, status)
        
        if success:
            return success_response(
                data=[task.dict() for task in task_list],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"根据用户获取任务列表失败: {e}")
        return error_response("获取任务列表失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('', methods=['POST'])
@handle_exceptions
@log_request
def create_task():
    """
    创建处理任务
    
    Request Body:
        sourceFileId: 源文件ID (必填)
        userId: 用户ID (必填)
        taskType: 任务类型 (必填, 1=文档解析, 2=向量化分块, 3=生成摘要)
        payload: 任务载荷 (可选)
        creator: 创建人ID (必填)
        
    Returns:
        201: 创建成功
        400: 数据验证失败
        409: 任务已存在
    """
    try:
        # 验证请求数据
        create_request = ProcessingTaskCreateRequest(**request.json)
        
        # 调用服务层
        success, message, task_vo = service.create_task(create_request)
        
        if success:
            return success_response(
                data=task_vo.dict(),
                message=message,
                status_code=HTTPStatus.CREATED
            )
        else:
            status_code = HTTPStatus.CONFLICT if "正在处理中" in message else HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except ValidationError as e:
        return error_response(f"数据验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"创建处理任务失败: {e}")
        return error_response("创建失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>', methods=['PUT'])
@handle_exceptions
@log_request
def update_task(task_id):
    """
    更新处理任务
    
    Path Parameters:
        task_id: 任务ID
        
    Request Body:
        status: 任务状态 (可选)
        payload: 任务载荷 (可选)
        errorMessage: 错误信息 (可选)
        startedAt: 开始执行时间 (可选)
        completedAt: 完成时间 (可选)
        updater: 更新人ID (必填)
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 更新成功
        404: 任务不存在
        403: 权限不足
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 验证请求数据
        update_request = ProcessingTaskUpdateRequest(**request.json)
        
        # 调用服务层
        success, message, task_vo = service.update_task(task_id, update_request, user_id)
        
        if success:
            return success_response(data=task_vo.dict(), message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except ValidationError as e:
        return error_response(f"数据验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"更新处理任务失败: {e}")
        return error_response("更新失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>/start', methods=['PUT'])
@handle_exceptions
@log_request
def start_task(task_id):
    """
    开始执行任务
    
    Path Parameters:
        task_id: 任务ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        updater: 更新人ID (必填)
        
    Returns:
        200: 开始执行成功
        404: 任务不存在
        403: 权限不足
        409: 任务状态不允许
    """
    try:
        user_id = request.args.get('userId', type=int)
        updater = request.args.get('updater', type=int)
        
        if not updater:
            return error_response("更新人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.start_task(task_id, updater, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif "状态" in message or "不允许" in message:
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"开始执行任务失败: {e}")
        return error_response("开始执行失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>/complete', methods=['PUT'])
@handle_exceptions
@log_request
def complete_task(task_id):
    """
    完成任务
    
    Path Parameters:
        task_id: 任务ID
        
    Request Body:
        payload: 任务结果载荷 (可选)
        updater: 更新人ID (必填)
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 完成成功
        404: 任务不存在
        403: 权限不足
        409: 任务状态不允许
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 获取请求数据
        data = request.json or {}
        updater = data.get('updater')
        payload = data.get('payload')
        
        if not updater:
            return error_response("更新人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.complete_task(task_id, updater, payload, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif "状态" in message or "不允许" in message:
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"完成任务失败: {e}")
        return error_response("完成任务失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>/fail', methods=['PUT'])
@handle_exceptions
@log_request
def fail_task(task_id):
    """
    设置任务失败
    
    Path Parameters:
        task_id: 任务ID
        
    Request Body:
        errorMessage: 错误信息 (必填)
        updater: 更新人ID (必填)
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 设置失败成功
        404: 任务不存在
        403: 权限不足
        409: 任务状态不允许
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 获取请求数据
        data = request.json or {}
        error_message = data.get('errorMessage')
        updater = data.get('updater')
        
        if not error_message:
            return error_response("错误信息不能为空", HTTPStatus.BAD_REQUEST)
        if not updater:
            return error_response("更新人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.fail_task(task_id, error_message, updater, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif "状态" in message or "不允许" in message:
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"设置任务失败失败: {e}")
        return error_response("设置任务失败失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>/cancel', methods=['PUT'])
@handle_exceptions
@log_request
def cancel_task(task_id):
    """
    取消任务
    
    Path Parameters:
        task_id: 任务ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        updater: 更新人ID (必填)
        
    Returns:
        200: 取消成功
        404: 任务不存在
        403: 权限不足
        409: 任务状态不允许
    """
    try:
        user_id = request.args.get('userId', type=int)
        updater = request.args.get('updater', type=int)
        
        if not updater:
            return error_response("更新人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.cancel_task(task_id, updater, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif "状态" in message or "不允许" in message:
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"取消任务失败: {e}")
        return error_response("取消任务失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>/retry', methods=['PUT'])
@handle_exceptions
@log_request
def retry_task(task_id):
    """
    重试任务
    
    Path Parameters:
        task_id: 任务ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        updater: 更新人ID (必填)
        
    Returns:
        200: 重试成功
        404: 任务不存在
        403: 权限不足
        409: 任务状态不允许
    """
    try:
        user_id = request.args.get('userId', type=int)
        updater = request.args.get('updater', type=int)
        
        if not updater:
            return error_response("更新人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.retry_task(task_id, updater, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif any(x in message for x in ["状态", "不允许", "失败"]):
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"重试任务失败: {e}")
        return error_response("重试任务失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/batch', methods=['POST'])
@handle_exceptions
@log_request
def batch_operation():
    """
    批量操作任务
    
    Request Body:
        taskIds: 任务ID列表 (必填)
        action: 操作类型 (必填, cancel/retry/delete)
        updater: 操作人ID (必填)
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 操作成功
        400: 数据验证失败
        403: 权限不足
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 验证请求数据
        batch_request = ProcessingTaskBatchRequest(**request.json)
        
        # 调用服务层
        success, message, success_count = service.batch_operation(batch_request, user_id)
        
        if success:
            return success_response(
                data={'successCount': success_count},
                message=message
            )
        else:
            status_code = HTTPStatus.FORBIDDEN if "权限" in message else HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except ValidationError as e:
        return error_response(f"数据验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"批量操作任务失败: {e}")
        return error_response("批量操作失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/<int:task_id>', methods=['DELETE'])
@handle_exceptions
@log_request
def delete_task(task_id):
    """
    删除任务
    
    Path Parameters:
        task_id: 任务ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 删除成功
        404: 任务不存在
        403: 权限不足
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 调用服务层
        success, message = service.delete_task(task_id, user_id)
        
        if success:
            return success_response(message=message)
        else:
            status_code = HTTPStatus.NOT_FOUND if "不存在" in message else HTTPStatus.FORBIDDEN
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"删除任务失败: {e}")
        return error_response("删除失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/queue', methods=['GET'])
@handle_exceptions
@log_request
def get_queued_tasks():
    """
    获取排队中的任务列表（用于任务调度）
    
    Query Parameters:
        taskType: 任务类型过滤 (可选)
        limit: 限制数量 (默认: 10, 最大: 100)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        task_type = request.args.get('taskType', type=int)
        limit = min(request.args.get('limit', 10, type=int), 100)
        
        # 调用服务层
        success, message, task_list = service.get_queued_tasks(task_type, limit)
        
        if success:
            return success_response(
                data=[task.dict() for task in task_list],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"获取排队任务失败: {e}")
        return error_response("获取排队任务失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/timeout', methods=['GET'])
@handle_exceptions
@log_request
def get_timeout_tasks():
    """
    获取超时的处理中任务
    
    Query Parameters:
        timeoutMinutes: 超时时间（分钟，默认: 30）
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        timeout_minutes = request.args.get('timeoutMinutes', 30, type=int)
        
        # 调用服务层
        success, message, task_list = service.get_timeout_tasks(timeout_minutes)
        
        if success:
            return success_response(
                data=[task.dict() for task in task_list],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"获取超时任务失败: {e}")
        return error_response("获取超时任务失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/statistics', methods=['GET'])
@handle_exceptions
@log_request
def get_task_statistics():
    """
    获取任务统计信息
    
    Query Parameters:
        userId: 用户ID (可选)
        sourceFileId: 源文件ID (可选)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        user_id = request.args.get('userId', type=int)
        source_file_id = request.args.get('sourceFileId', type=int)
        
        # 调用服务层
        success, message, stats = service.get_task_statistics(user_id, source_file_id)
        
        if success:
            return success_response(data=stats, message=message)
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"获取任务统计失败: {e}")
        return error_response("获取统计失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@processing_tasks_bp.route('/cleanup', methods=['POST'])
@handle_exceptions
@log_request
def cleanup_old_tasks():
    """
    清理旧任务记录
    
    Request Body:
        days: 天数 (必填, 超过此时间的任务将被删除)
        status: 任务状态过滤 (可选, 为空时清理所有已完成和失败的任务)
        
    Returns:
        200: 清理成功
        400: 参数错误
    """
    try:
        data = request.json or {}
        days = data.get('days')
        status = data.get('status')
        
        if not days or days <= 0:
            return error_response("天数必须大于0", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message, cleanup_count = service.cleanup_old_tasks(days, status)
        
        if success:
            return success_response(
                data={'cleanupCount': cleanup_count},
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"清理旧任务记录失败: {e}")
        return error_response("清理失败", HTTPStatus.INTERNAL_SERVER_ERROR)