"""
源文件控制器
提供源文件相关的REST API接口
"""
import logging
from datetime import datetime
from typing import Dict, Any
from flask import Blueprint, request, jsonify
from pydantic import ValidationError

from knowledge_spaces.service.source_files_service import SourceFileService
from knowledge_spaces.model.source_file_model import (
    SourceFileCreateRequest, SourceFileUpdateRequest,
    SourceFileQueryRequest, SourceFileBatchDeleteRequest,
    SourceFileBatchUpdateStatusRequest
)

# 创建蓝图
source_files_bp = Blueprint('source_files', __name__)

# 初始化服务
service = SourceFileService()


def make_response(success: bool, message: str, data: Any = None, code: int = None) -> Dict[str, Any]:
    """
    构建统一响应格式
    
    Args:
        success: 成功标志
        message: 响应消息
        data: 响应数据
        code: HTTP状态码
        
    Returns:
        响应字典
    """
    response = {
        'success': success,
        'message': message,
        'data': data,
        'timestamp': datetime.now().isoformat()
    }
    
    if code:
        response['code'] = code
    
    return response


@source_files_bp.route('/', methods=['POST'])
def create_file():
    """创建源文件"""
    try:
        # 解析请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify(make_response(False, "请求数据不能为空", code=400)), 400
        
        # 验证请求数据
        try:
            file_request = SourceFileCreateRequest(**request_data)
        except ValidationError as e:
            return jsonify(make_response(False, f"请求参数错误: {str(e)}", code=400)), 400
        
        # 执行业务逻辑
        success, message, file_vo = service.create_file(file_request)
        
        if success:
            return jsonify(make_response(True, message, file_vo.dict() if file_vo else None, 201)), 201
        else:
            return jsonify(make_response(False, message, code=400)), 400
            
    except Exception as e:
        logging.error(f"创建源文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/<int:file_id>', methods=['GET'])
def get_file(file_id: int):
    """获取源文件详情"""
    try:
        success, message, file_vo = service.get_file_by_id(file_id)
        
        if success:
            return jsonify(make_response(True, message, file_vo.dict() if file_vo else None))
        else:
            return jsonify(make_response(False, message, code=404)), 404
            
    except Exception as e:
        logging.error(f"获取源文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/knowledge-space/<int:knowledge_spaces_id>', methods=['GET'])
def get_files_by_knowledge_space(knowledge_spaces_id: int):
    """获取知识空间的文件列表"""
    try:
        success, message, file_vos = service.get_files_by_knowledge_space(knowledge_spaces_id)
        
        files_data = [file_vo.dict() for file_vo in file_vos] if file_vos else []
        return jsonify(make_response(success, message, files_data))
        
    except Exception as e:
        logging.error(f"获取知识空间文件列表接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/search', methods=['POST'])
def search_files():
    """搜索源文件"""
    try:
        # 解析请求数据
        request_data = request.get_json() or {}
        
        # 验证请求数据
        try:
            query_request = SourceFileQueryRequest(**request_data)
        except ValidationError as e:
            return jsonify(make_response(False, f"请求参数错误: {str(e)}", code=400)), 400
        
        # 执行业务逻辑
        success, message, result_data = service.search_files(query_request)
        
        if success:
            # 转换VO对象为字典
            if 'list' in result_data and result_data['list']:
                result_data['list'] = [file_vo.dict() for file_vo in result_data['list']]
            
            return jsonify(make_response(True, message, result_data))
        else:
            return jsonify(make_response(False, message, code=400)), 400
            
    except Exception as e:
        logging.error(f"搜索源文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/<int:file_id>', methods=['PUT'])
def update_file(file_id: int):
    """更新源文件"""
    try:
        # 解析请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify(make_response(False, "请求数据不能为空", code=400)), 400
        
        # 验证请求数据
        try:
            update_request = SourceFileUpdateRequest(**request_data)
        except ValidationError as e:
            return jsonify(make_response(False, f"请求参数错误: {str(e)}", code=400)), 400
        
        # 执行业务逻辑
        success, message, file_vo = service.update_file(file_id, update_request)
        
        if success:
            return jsonify(make_response(True, message, file_vo.dict() if file_vo else None))
        else:
            return jsonify(make_response(False, message, code=400)), 400
            
    except Exception as e:
        logging.error(f"更新源文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/batch/update-status', methods=['POST'])
def batch_update_status():
    """批量更新文件状态"""
    try:
        # 解析请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify(make_response(False, "请求数据不能为空", code=400)), 400
        
        # 验证请求数据
        try:
            batch_request = SourceFileBatchUpdateStatusRequest(**request_data)
        except ValidationError as e:
            return jsonify(make_response(False, f"请求参数错误: {str(e)}", code=400)), 400
        
        # 执行业务逻辑
        success, message = service.batch_update_status(batch_request)
        
        return jsonify(make_response(success, message))
        
    except Exception as e:
        logging.error(f"批量更新文件状态接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/<int:file_id>', methods=['DELETE'])
def delete_file(file_id: int):
    """删除源文件"""
    try:
        # 获取删除人ID
        deleter = request.args.get('deleter', type=int)
        if not deleter:
            return jsonify(make_response(False, "缺少删除人ID参数", code=400)), 400
        
        # 获取是否硬删除参数
        hard_delete = request.args.get('hard_delete', 'false').lower() == 'true'
        
        # 执行业务逻辑
        success, message = service.delete_file(file_id, deleter, hard_delete)
        
        return jsonify(make_response(success, message))
        
    except Exception as e:
        logging.error(f"删除源文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/batch/delete', methods=['POST'])
def batch_delete_files():
    """批量删除源文件"""
    try:
        # 解析请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify(make_response(False, "请求数据不能为空", code=400)), 400
        
        # 验证请求数据
        try:
            delete_request = SourceFileBatchDeleteRequest(**request_data)
        except ValidationError as e:
            return jsonify(make_response(False, f"请求参数错误: {str(e)}", code=400)), 400
        
        # 获取是否硬删除参数
        hard_delete = request.args.get('hard_delete', 'false').lower() == 'true'
        
        # 执行业务逻辑
        success, message = service.batch_delete_files(delete_request, hard_delete)
        
        return jsonify(make_response(success, message))
        
    except Exception as e:
        logging.error(f"批量删除源文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """获取文件统计信息"""
    try:
        # 获取查询参数
        knowledge_spaces_id = request.args.get('knowledge_spaces_id', type=int)
        creator_id = request.args.get('creator_id', type=int)
        
        # 执行业务逻辑
        success, message, statistics = service.get_file_statistics(knowledge_spaces_id, creator_id)
        
        if success:
            return jsonify(make_response(True, message, statistics.dict()))
        else:
            return jsonify(make_response(False, message, code=400)), 400
            
    except Exception as e:
        logging.error(f"获取文件统计接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/upload/validate', methods=['POST'])
def validate_upload():
    """验证文件上传"""
    try:
        # 解析请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify(make_response(False, "请求数据不能为空", code=400)), 400
        
        file_format = request_data.get('file_format')
        file_size = request_data.get('file_size')
        max_size_mb = request_data.get('max_size_mb', 100)
        
        if not file_format or file_size is None:
            return jsonify(make_response(False, "缺少必要参数", code=400)), 400
        
        # 执行验证
        success, message = service.validate_file_upload(file_format, file_size, max_size_mb)
        
        return jsonify(make_response(success, message))
        
    except Exception as e:
        logging.error(f"验证文件上传接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/status/<int:upload_status>', methods=['GET'])
def get_files_by_status(upload_status: int):
    """根据上传状态获取文件列表"""
    try:
        # 获取限制数量参数
        limit = request.args.get('limit', type=int)
        
        # 执行业务逻辑
        success, message, file_vos = service.get_files_by_upload_status(upload_status, limit)
        
        if success:
            files_data = [file_vo.dict() for file_vo in file_vos] if file_vos else []
            return jsonify(make_response(True, message, files_data))
        else:
            return jsonify(make_response(False, message, code=400)), 400
            
    except Exception as e:
        logging.error(f"根据状态获取文件列表接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/format/<path:file_format>', methods=['GET'])
def get_files_by_format(file_format: str):
    """根据文件格式获取文件列表"""
    try:
        # 获取知识空间ID参数
        knowledge_spaces_id = request.args.get('knowledge_spaces_id', type=int)
        
        # 执行业务逻辑
        success, message, file_vos = service.get_files_by_format(file_format, knowledge_spaces_id)
        
        if success:
            files_data = [file_vo.dict() for file_vo in file_vos] if file_vos else []
            return jsonify(make_response(True, message, files_data))
        else:
            return jsonify(make_response(False, message, code=400)), 400
            
    except Exception as e:
        logging.error(f"根据格式获取文件列表接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/upload/complete/<int:file_id>', methods=['POST'])
def complete_upload(file_id: int):
    """处理文件上传完成"""
    try:
        # 获取更新人ID
        updater = request.json.get('updater') if request.json else None
        if not updater:
            return jsonify(make_response(False, "缺少更新人ID参数", code=400)), 400
        
        # 执行业务逻辑
        success, message = service.process_upload_completion(file_id, updater)
        
        return jsonify(make_response(success, message))
        
    except Exception as e:
        logging.error(f"处理文件上传完成接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/upload/fail/<int:file_id>', methods=['POST'])
def fail_upload(file_id: int):
    """处理文件上传失败"""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify(make_response(False, "请求数据不能为空", code=400)), 400
        
        error_message = request_data.get('error_message', '')
        updater = request_data.get('updater')
        
        if not updater:
            return jsonify(make_response(False, "缺少更新人ID参数", code=400)), 400
        
        # 执行业务逻辑
        success, message = service.process_upload_failure(file_id, error_message, updater)
        
        return jsonify(make_response(success, message))
        
    except Exception as e:
        logging.error(f"处理文件上传失败接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


@source_files_bp.route('/cleanup/failed', methods=['POST'])
def cleanup_failed():
    """清理过期失败上传文件"""
    try:
        # 获取天数参数
        days_old = request.json.get('days_old', 7) if request.json else 7
        
        # 执行业务逻辑
        success, message, cleaned_count = service.cleanup_failed_uploads(days_old)
        
        data = {'cleaned_count': cleaned_count} if success else None
        return jsonify(make_response(success, message, data))
        
    except Exception as e:
        logging.error(f"清理失败上传文件接口异常: {e}")
        return jsonify(make_response(False, "服务器内部错误", code=500)), 500


# 错误处理
@source_files_bp.errorhandler(404)
def not_found(error):
    return jsonify(make_response(False, "接口不存在", code=404)), 404


@source_files_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify(make_response(False, "请求方法不被允许", code=405)), 405


@source_files_bp.errorhandler(500)
def internal_error(error):
    return jsonify(make_response(False, "服务器内部错误", code=500)), 500