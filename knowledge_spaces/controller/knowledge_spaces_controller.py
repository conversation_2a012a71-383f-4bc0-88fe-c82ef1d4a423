"""
知识空间控制器
提供知识空间的增删改查API接口
"""
import logging
from flask import Blueprint,  request
from http import HTTPStatus
from pydantic import ValidationError

from knowledge_spaces.service.knowledge_spaces_service import KnowledgeSpaceService
from knowledge_spaces.model.knowledge_spaces_model import (
    KnowledgeSpaceCreateRequest, KnowledgeSpaceUpdateRequest,
    KnowledgeSpaceQueryRequest, KnowledgeSpaceMoveRequest
)
from utils.response_utils import success_response, error_response, paginated_response
from utils.decorators import handle_exceptions, log_request

# 创建Blueprint
knowledge_spaces_bp = Blueprint('knowledge_spaces', __name__)
service = KnowledgeSpaceService()


@knowledge_spaces_bp.route('', methods=['GET'])
@handle_exceptions
@log_request
def get_spaces():
    """
    获取知识空间列表
    
    Query Parameters:
        page: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)
        userId: 用户ID (必填)
        parentId: 父空间ID (0表示顶级空间)
        name: 名称搜索
        level: 层级深度
        status: 状态 (1=启用, 0=禁用)
        includeChildren: 是否包含子空间信息
        orderBy: 排序字段 (默认: create_time)
        orderDesc: 是否倒序 (默认: true)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = min(request.args.get('pageSize', 20, type=int), 100)
        user_id = request.args.get('userId', type=int)
        parent_id = request.args.get('parentId', type=int)
        name = request.args.get('name')
        level = request.args.get('level', type=int)
        status = request.args.get('status', type=int)
        include_children = request.args.get('includeChildren', 'false').lower() == 'true'
        order_by = request.args.get('orderBy', 'create_time')
        order_desc = request.args.get('orderDesc', 'true').lower() == 'true'
        
        # 验证必填参数
        if not user_id:
            return error_response("用户ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 构建查询请求
        query_request = KnowledgeSpaceQueryRequest(
            userId=user_id,
            parentId=parent_id,
            name=name,
            level=level,
            status=status,
            includeChildren=include_children,
            page=page,
            pageSize=page_size,
            orderBy=order_by,
            orderDesc=order_desc
        )
        
        # 调用服务层
        success, message, result = service.search_spaces(query_request)
        
        if success:
            return paginated_response(
                data=[space.dict() for space in result['list']],
                total=result['total'],
                page=result['page'],
                page_size=result['pageSize'],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except ValidationError as e:
        return error_response(f"参数验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"获取知识空间列表失败: {e}")
        return error_response("获取列表失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/tree', methods=['GET'])
@handle_exceptions
@log_request
def get_space_tree():
    """
    获取知识空间树结构
    
    Query Parameters:
        userId: 用户ID (必填)
        maxLevel: 最大层级深度
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        user_id = request.args.get('userId', type=int)
        max_level = request.args.get('maxLevel', type=int)
        
        if not user_id:
            return error_response("用户ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message, tree_data = service.get_space_tree(user_id, max_level)
        
        if success:
            return success_response(
                data=[space.dict() for space in tree_data],
                message=message
            )
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"获取空间树结构失败: {e}")
        return error_response("获取树结构失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/<int:space_id>', methods=['GET'])
@handle_exceptions
@log_request
def get_space_by_id(space_id):
    """
    根据ID获取知识空间详情
    
    Path Parameters:
        space_id: 空间ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 获取成功
        404: 空间不存在
        403: 权限不足
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 调用服务层
        success, message, space_vo = service.get_space_by_id(space_id, user_id)
        
        if success:
            return success_response(data=space_vo.dict(), message=message)
        else:
            status_code = HTTPStatus.NOT_FOUND if "不存在" in message else HTTPStatus.FORBIDDEN
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"获取知识空间详情失败: {e}")
        return error_response("获取详情失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('', methods=['POST'])
@handle_exceptions
@log_request
def create_space():
    """
    创建知识空间
    
    Request Body:
        userId: 用户ID (必填)
        parentId: 父空间ID (可选)
        name: 空间名称 (必填)
        description: 空间描述 (可选)
        creator: 创建人ID (必填)
        
    Returns:
        201: 创建成功
        400: 数据验证失败
        409: 重名或层级限制
    """
    try:
        # 验证请求数据
        create_request = KnowledgeSpaceCreateRequest(**request.json)
        
        # 调用服务层
        success, message, space_vo = service.create_space(create_request)
        
        if success:
            return success_response(
                data=space_vo.dict(),
                message=message,
                status_code=HTTPStatus.CREATED
            )
        else:
            status_code = HTTPStatus.CONFLICT if any(x in message for x in ["重名", "层级", "深度"]) else HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except ValidationError as e:
        return error_response(f"数据验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"创建知识空间失败: {e}")
        return error_response("创建失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/<int:space_id>', methods=['PUT'])
@handle_exceptions
@log_request
def update_space(space_id):
    """
    更新知识空间
    
    Path Parameters:
        space_id: 空间ID
        
    Request Body:
        name: 空间名称 (可选)
        description: 空间描述 (可选)
        status: 状态 (可选)
        updater: 更新人ID (必填)
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 更新成功
        404: 空间不存在
        403: 权限不足
        409: 重名
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 验证请求数据
        update_request = KnowledgeSpaceUpdateRequest(**request.json)
        
        # 调用服务层
        success, message, space_vo = service.update_space(space_id, update_request, user_id)
        
        if success:
            return success_response(data=space_vo.dict(), message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif "重名" in message:
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except ValidationError as e:
        return error_response(f"数据验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"更新知识空间失败: {e}")
        return error_response("更新失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/<int:space_id>/move', methods=['PUT'])
@handle_exceptions
@log_request
def move_space(space_id):
    """
    移动知识空间
    
    Path Parameters:
        space_id: 要移动的空间ID
        
    Request Body:
        targetParentId: 目标父空间ID (可选，null表示移到顶级)
        updater: 更新人ID (必填)
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        
    Returns:
        200: 移动成功
        404: 空间不存在
        403: 权限不足
        409: 移动限制或重名
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        # 验证请求数据
        move_request = KnowledgeSpaceMoveRequest(**request.json)
        
        # 调用服务层
        success, message = service.move_space(space_id, move_request, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif any(x in message for x in ["重名", "层级", "后代", "自己"]):
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except ValidationError as e:
        return error_response(f"数据验证失败: {str(e)}", HTTPStatus.BAD_REQUEST)
    except Exception as e:
        logging.error(f"移动知识空间失败: {e}")
        return error_response("移动失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/<int:space_id>', methods=['DELETE'])
@handle_exceptions
@log_request
def delete_space(space_id):
    """
    删除知识空间
    
    Path Parameters:
        space_id: 空间ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        deleter: 删除人ID (必填)
        hardDelete: 是否硬删除 (默认: false)
        
    Returns:
        200: 删除成功
        404: 空间不存在
        403: 权限不足
        409: 有子空间无法删除
    """
    try:
        user_id = request.args.get('userId', type=int)
        deleter = request.args.get('deleter', type=int)
        hard_delete = request.args.get('hardDelete', 'false').lower() == 'true'
        
        if not deleter:
            return error_response("删除人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.delete_space(space_id, deleter, user_id, hard_delete)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif "子空间" in message:
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"删除知识空间失败: {e}")
        return error_response("删除失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/<int:space_id>/restore', methods=['PUT'])
@handle_exceptions
@log_request
def restore_space(space_id):
    """
    恢复已删除的知识空间
    
    Path Parameters:
        space_id: 空间ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        updater: 更新人ID (必填)
        
    Returns:
        200: 恢复成功
        404: 空间不存在
        403: 权限不足
        409: 重名或父空间不存在
    """
    try:
        user_id = request.args.get('userId', type=int)
        updater = request.args.get('updater', type=int)
        
        if not updater:
            return error_response("更新人ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message = service.restore_space(space_id, updater, user_id)
        
        if success:
            return success_response(message=message)
        else:
            if "不存在" in message:
                status_code = HTTPStatus.NOT_FOUND
            elif "权限" in message:
                status_code = HTTPStatus.FORBIDDEN
            elif any(x in message for x in ["重名", "父空间", "未被删除"]):
                status_code = HTTPStatus.CONFLICT
            else:
                status_code = HTTPStatus.BAD_REQUEST
            return error_response(message, status_code)
            
    except Exception as e:
        logging.error(f"恢复知识空间失败: {e}")
        return error_response("恢复失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/statistics', methods=['GET'])
@handle_exceptions
@log_request
def get_space_statistics():
    """
    获取知识空间统计信息
    
    Query Parameters:
        userId: 用户ID (必填)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        user_id = request.args.get('userId', type=int)
        
        if not user_id:
            return error_response("用户ID不能为空", HTTPStatus.BAD_REQUEST)
        
        # 调用服务层
        success, message, stats = service.get_space_statistics(user_id)
        
        if success:
            return success_response(data=stats, message=message)
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"获取空间统计失败: {e}")
        return error_response("获取统计失败", HTTPStatus.INTERNAL_SERVER_ERROR)


@knowledge_spaces_bp.route('/<int:space_id>/children', methods=['GET'])
@handle_exceptions
@log_request
def get_space_children(space_id):
    """
    获取指定空间的子空间列表
    
    Path Parameters:
        space_id: 父空间ID
        
    Query Parameters:
        userId: 用户ID (用于权限验证)
        includeGrandchildren: 是否包含孙子空间信息
        
    Returns:
        200: 获取成功
        404: 空间不存在
        403: 权限不足
    """
    try:
        user_id = request.args.get('userId', type=int)
        include_grandchildren = request.args.get('includeGrandchildren', 'false').lower() == 'true'
        
        # 先验证父空间是否存在和权限
        success, message, parent_space = service.get_space_by_id(space_id, user_id)
        if not success:
            status_code = HTTPStatus.NOT_FOUND if "不存在" in message else HTTPStatus.FORBIDDEN
            return error_response(message, status_code)
        
        # 获取子空间列表
        success, message, children = service.get_user_spaces(
            parent_space.userId, 
            space_id, 
            include_grandchildren
        )
        
        if success:
            return success_response(data=[child.dict() for child in children], message=message)
        else:
            return error_response(message, HTTPStatus.BAD_REQUEST)
            
    except Exception as e:
        logging.error(f"获取子空间列表失败: {e}")
        return error_response("获取子空间失败", HTTPStatus.INTERNAL_SERVER_ERROR)


# 获取所有的空间和文件  需要同时传递参数userId、parent_id
@knowledge_spaces_bp.route('/all', methods=['GET'])
@handle_exceptions
@log_request
def get_all_spaces_and_files():
    try:
        user_id = request.args.get('userId', type=int)
        parent_id = request.args.get('parent_id', type=int)
        if user_id is None or parent_id is None:
            return error_response("参数错误", HTTPStatus.BAD_REQUEST)
        data = service.get_all_spaces_and_files(user_id, parent_id)
        return success_response(data=data, message="获取成功")
    except Exception as e:
        logging.error(f"获取所有空间和文件失败: {e}")
        return error_response("获取失败", HTTPStatus.INTERNAL_SERVER_ERROR)