"""
知识空间服务层
提供知识空间的业务逻辑处理
"""
import logging
from typing import List, Optional, Dict, Any, Tuple

from knowledge_spaces.dao.knowledge_spaces_dao import KnowledgeSpaceDAO
from knowledge_spaces.dao.source_files_dao import SourceFileDAO
from knowledge_spaces.service.source_files_service import SourceFileService, _model_to_vo
from knowledge_spaces.model.knowledge_spaces_model import (
    KnowledgeSpaceModel, KnowledgeSpaceVO,
    KnowledgeSpaceCreateRequest, KnowledgeSpaceUpdateRequest,
    KnowledgeSpaceQueryRequest, KnowledgeSpaceMoveRequest
)


class KnowledgeSpaceService:
    """知识空间服务类"""

    def __init__(self):
        self.source_file_dao = SourceFileDAO()
        self.source_file_service = SourceFileService()
        self.dao = KnowledgeSpaceDAO()

    def create_space(self, request: KnowledgeSpaceCreateRequest) -> Tuple[bool, str, Optional[KnowledgeSpaceVO]]:
        """
        创建知识空间
        
        Args:
            request: 创建请求对象
            
        Returns:
            (成功标志, 消息, 空间VO对象)
        """
        try:
            # 验证父空间是否存在（如果指定了父空间）
            if request.parentId:
                parent_space = self.dao.get_by_id(request.parentId)
                if not parent_space:
                    return False, "父空间不存在", None

                # 检查层级深度限制（最多5级）
                if parent_space.level >= 4:  # 0-4共5级
                    return False, "空间层级过深，最多支持5级嵌套", None

            # 检查同一父空间下是否存在同名空间
            existing_spaces = self.dao.get_by_user_id(
                request.userId,
                request.parentId
            )

            if any(space.name == request.name for space in existing_spaces):
                return False, "同一层级下不能存在重名空间", None

            # 构建数据字典
            space_data = {
                'user_id': request.userId,
                'parent_id': request.parentId,
                'name': request.name,
                'description': request.description,
                'creator': request.creator,
                'updater': request.creator,
                'status': 1
            }

            # 创建空间
            space_model = self.dao.create(space_data)
            if space_model:
                space_vo = self._model_to_vo(space_model)
                return True, "创建成功", space_vo
            else:
                return False, "创建失败", None

        except Exception as e:
            logging.error(f"创建知识空间失败: {e}")
            return False, f"创建失败: {str(e)}", None

    def get_space_by_id(self, space_id: int, user_id: Optional[int] = None) -> Tuple[
        bool, str, Optional[KnowledgeSpaceVO]]:
        """
        根据ID获取知识空间
        
        Args:
            space_id: 空间ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息, 空间VO对象)
        """
        try:
            space_model = self.dao.get_by_id(space_id)
            if not space_model:
                return False, "空间不存在", None

            # 权限验证
            if user_id and space_model.user_id != user_id:
                return False, "无权限访问该空间", None

            space_vo = self._model_to_vo(space_model)
            # 添加子空间信息
            space_vo.childrenCount = self.dao.count_children(space_id)

            return True, "获取成功", space_vo

        except Exception as e:
            logging.error(f"获取知识空间失败: {e}")
            return False, f"获取失败: {str(e)}", None

    def get_user_spaces(self, user_id: int, parent_id: Optional[int] = None,
                        include_children: bool = False) -> Tuple[bool, str, List[KnowledgeSpaceVO]]:
        """
        获取用户的知识空间列表
        
        Args:
            user_id: 用户ID
            parent_id: 父空间ID
            include_children: 是否包含子空间信息
            
        Returns:
            (成功标志, 消息, 空间VO列表)
        """
        try:
            spaces = self.dao.get_by_user_id(user_id, parent_id)

            def convert_space_with_children(space):
                space_vo = self._model_to_vo(space)
                # 添加子空间信息
                if include_children:
                    children = self.dao.get_children(space.id)
                    space_vo.children = [self._model_to_vo(child) for child in children]
                space_vo.childrenCount = self.dao.count_children(space.id)
                return space_vo

            space_vos = [convert_space_with_children(space) for space in spaces]

            return True, "获取成功", space_vos

        except Exception as e:
            logging.error(f"获取用户空间列表失败: {e}")
            return False, f"获取失败: {str(e)}", []

    def get_space_tree(self, user_id: int, max_level: Optional[int] = None) -> Tuple[bool, str, List[KnowledgeSpaceVO]]:
        """
        获取用户的知识空间树结构
        
        Args:
            user_id: 用户ID
            max_level: 最大层级深度
            
        Returns:
            (成功标志, 消息, 树形结构的空间列表)
        """
        try:
            # 获取所有空间
            all_spaces = self.dao.get_space_tree(user_id, max_level)

            # 构建树形结构
            space_map = {}
            root_spaces = []

            for space in all_spaces:
                space_vo = self._model_to_vo(space)
                space_vo.children = []
                space_vo.childrenCount = self.dao.count_children(space.id)
                space_map[space.id] = space_vo

                if space.parent_id is None:
                    root_spaces.append(space_vo)
                else:
                    parent = space_map.get(space.parent_id)
                    if parent:
                        parent.children.append(space_vo)

            return True, "获取成功", root_spaces

        except Exception as e:
            logging.error(f"获取空间树结构失败: {e}")
            return False, f"获取失败: {str(e)}", []

    def search_spaces(self, request: KnowledgeSpaceQueryRequest) -> Tuple[bool, str, Dict[str, Any]]:
        """
        搜索知识空间
        
        Args:
            request: 查询请求对象
            
        Returns:
            (成功标志, 消息, 分页数据)
        """
        try:
            # 构建过滤条件
            filters = {}
            if request.userId:
                filters['user_id'] = request.userId
            if request.parentId is not None:
                filters['parent_id'] = request.parentId
            if request.name:
                filters['name'] = request.name
            if request.level is not None:
                filters['level'] = request.level
            if request.status is not None:
                filters['status'] = request.status

            filters['order_by'] = request.orderBy
            filters['order_desc'] = request.orderDesc

            # 计算分页参数
            offset = (request.page - 1) * request.pageSize

            # 获取数据
            spaces = self.dao.search_spaces(filters, request.pageSize, offset)
            total = self.dao.count_spaces(filters)

            def convert_space_with_search_info(space):
                space_vo = self._model_to_vo(space)
                if request.includeChildren:
                    children = self.dao.get_children(space.id)
                    space_vo.children = [self._model_to_vo(child) for child in children]
                space_vo.childrenCount = self.dao.count_children(space.id)
                return space_vo

            space_vos = [convert_space_with_search_info(space) for space in spaces]

            result = {
                'list': space_vos,
                'total': total,
                'page': request.page,
                'pageSize': request.pageSize,
                'totalPages': (total + request.pageSize - 1) // request.pageSize
            }

            return True, "搜索成功", result

        except Exception as e:
            logging.error(f"搜索知识空间失败: {e}")
            return False, f"搜索失败: {str(e)}", {}

    def update_space(self, space_id: int, request: KnowledgeSpaceUpdateRequest,
                     user_id: Optional[int] = None) -> Tuple[bool, str, Optional[KnowledgeSpaceVO]]:
        """
        更新知识空间
        
        Args:
            space_id: 空间ID
            request: 更新请求对象
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息, 更新后的空间VO对象)
        """
        try:
            # 获取现有空间
            space_model = self.dao.get_by_id(space_id)
            if not space_model:
                return False, "空间不存在", None

            # 权限验证
            if user_id and space_model.user_id != user_id:
                return False, "无权限修改该空间", None

            # 检查重名
            if request.name and request.name != space_model.name:
                existing_spaces = self.dao.get_by_user_id(
                    space_model.user_id,
                    space_model.parent_id
                )

                if any(space.name == request.name and space.id != space_id
                       for space in existing_spaces):
                    return False, "同一层级下不能存在重名空间", None

            # 构建更新数据
            update_data = {'updater': request.updater}

            if request.name is not None:
                update_data['name'] = request.name
            if request.description is not None:
                update_data['description'] = request.description
            if request.status is not None:
                update_data['status'] = request.status

            # 执行更新
            success = self.dao.update(space_id, update_data)
            if success:
                updated_space = self.dao.get_by_id(space_id)
                space_vo = self._model_to_vo(updated_space)
                return True, "更新成功", space_vo
            else:
                return False, "更新失败", None

        except Exception as e:
            logging.error(f"更新知识空间失败: {e}")
            return False, f"更新失败: {str(e)}", None

    def move_space(self, space_id: int, request: KnowledgeSpaceMoveRequest,
                   user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        移动知识空间
        
        Args:
            space_id: 要移动的空间ID
            request: 移动请求对象
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 获取要移动的空间
            space_model = self.dao.get_by_id(space_id)
            if not space_model:
                return False, "空间不存在"

            # 权限验证
            if user_id and space_model.user_id != user_id:
                return False, "无权限移动该空间"

            # 验证目标父空间
            if request.targetParentId:
                target_parent = self.dao.get_by_id(request.targetParentId)
                if not target_parent:
                    return False, "目标父空间不存在"

                # 检查是否属于同一用户
                if target_parent.user_id != space_model.user_id:
                    return False, "不能移动到其他用户的空间下"

                # 检查层级深度
                if target_parent.level >= 4:  # 移动后最多5级
                    return False, "目标父空间层级过深"

            # 检查重名
            if request.targetParentId != space_model.parent_id:
                existing_spaces = self.dao.get_by_user_id(
                    space_model.user_id,
                    request.targetParentId
                )

                if any(space.name == space_model.name for space in existing_spaces):
                    return False, "目标位置已存在同名空间"

            # 执行移动
            success = self.dao.move_space(space_id, request.targetParentId, request.updater)
            if success:
                return True, "移动成功"
            else:
                return False, "移动失败"

        except Exception as e:
            logging.error(f"移动知识空间失败: {e}")
            return False, f"移动失败: {str(e)}"

    def delete_space(self, space_id: int, deleter: int, user_id: Optional[int] = None,
                     hard_delete: bool = False) -> Tuple[bool, str]:
        """
        删除知识空间
        
        Args:
            space_id: 空间ID
            deleter: 删除者ID
            user_id: 用户ID（用于权限验证）
            hard_delete: 是否硬删除
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 获取空间
            space_model = self.dao.get_by_id(space_id, include_deleted=hard_delete)
            if not space_model:
                return False, "空间不存在"

            # 权限验证
            if user_id and space_model.user_id != user_id:
                return False, "无权限删除该空间"

            # 检查是否有子空间
            children_count = self.dao.count_children(space_id)
            if children_count > 0:
                return False, f"该空间下还有{children_count}个子空间，请先删除子空间"

            # 执行删除
            if hard_delete:
                success = self.dao.hard_delete(space_id)
                message = "永久删除成功" if success else "永久删除失败"
            else:
                success = self.dao.soft_delete(space_id, deleter)
                message = "删除成功" if success else "删除失败"

            return success, message

        except Exception as e:
            logging.error(f"删除知识空间失败: {e}")
            return False, f"删除失败: {str(e)}"

    def restore_space(self, space_id: int, updater: int, user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        恢复已删除的知识空间
        
        Args:
            space_id: 空间ID
            updater: 更新人ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 获取已删除的空间
            space_model = self.dao.get_by_id(space_id, include_deleted=True)
            if not space_model:
                return False, "空间不存在"

            if not space_model.deleted:
                return False, "空间未被删除"

            # 权限验证
            if user_id and space_model.user_id != user_id:
                return False, "无权限恢复该空间"

            # 检查父空间是否存在且未删除
            if space_model.parent_id:
                parent_space = self.dao.get_by_id(space_model.parent_id)
                if not parent_space:
                    return False, "父空间不存在或已被删除，无法恢复"

            # 检查重名
            existing_spaces = self.dao.get_by_user_id(
                space_model.user_id,
                space_model.parent_id
            )

            if any(space.name == space_model.name for space in existing_spaces):
                return False, "同一层级下已存在同名空间"

            # 执行恢复
            update_data = {
                'deleted': False,
                'updater': updater
            }

            success = self.dao.update(space_id, update_data)
            return success, "恢复成功" if success else "恢复失败"

        except Exception as e:
            logging.error(f"恢复知识空间失败: {e}")
            return False, f"恢复失败: {str(e)}"

    def get_space_statistics(self, user_id: int) -> Tuple[bool, str, Dict[str, Any]]:
        """
        获取用户知识空间统计信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            (成功标志, 消息, 统计数据)
        """
        try:
            # 获取各类统计数据
            total_count = self.dao.count_spaces({'user_id': user_id})
            active_count = self.dao.count_spaces({'user_id': user_id, 'status': 1})
            inactive_count = self.dao.count_spaces({'user_id': user_id, 'status': 0})

            # 按层级统计
            level_stats = {}
            for level in range(5):  # 0-4级
                count = self.dao.count_spaces({'user_id': user_id, 'level': level})
                level_stats[f'level_{level}'] = count

            stats = {
                'total': total_count,
                'active': active_count,
                'inactive': inactive_count,
                'levelStats': level_stats
            }

            return True, "获取成功", stats

        except Exception as e:
            logging.error(f"获取空间统计失败: {e}")
            return False, f"获取失败: {str(e)}", {}

    def _model_to_vo(self, model: KnowledgeSpaceModel) -> KnowledgeSpaceVO:
        """
        将Model对象转换为VO对象
        
        Args:
            model: 模型对象
            
        Returns:
            VO对象
        """
        return KnowledgeSpaceVO(
            id=model.id,
            userId=model.user_id,
            parentId=model.parent_id,
            name=model.name,
            description=model.description,
            level=model.level,
            path=model.path,
            creator=model.creator,
            createTime=model.create_time,
            updater=model.updater,
            updateTime=model.update_time,
            status=model.status,
            deleted=model.deleted
        )

    # 根据用户id，父知识空间id，获取信息
    def get_all_spaces_and_files(self, user_id, knowledge_spaces_id, parent_id) -> Dict[str, Any]:
        try:
            # 获取当前父空间下所有的空间
            all_spaces = self.dao.get_by_user_id(user_id, parent_id)
            space_vos = [self._model_to_vo(space) for space in all_spaces]

            all_files = self.source_file_dao.get_by_knowledge_space(knowledge_spaces_id)
            file_vos = [_model_to_vo(file) for file in all_files]
            return {
                'spaces': space_vos,
                'files': file_vos
            }
        except Exception as e:
            logging.error(f"获取所有空间和文件失败: {e}")
            return []
