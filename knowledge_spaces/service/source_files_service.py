"""
源文件服务层
提供源文件的业务逻辑处理
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from knowledge_spaces.dao.source_files_dao import SourceFileDAO
from knowledge_spaces.model.source_file_model import (
    SourceFileModel, SourceFileVO, SourceFileDO,
    SourceFileCreateRequest, SourceFileUpdateRequest,
    SourceFileQueryRequest, SourceFileBatchDeleteRequest,
    SourceFileBatchUpdateStatusRequest, SourceFileStatisticsResponse,
    UploadStatus
)


class SourceFileService:
    """源文件服务类"""
    
    def __init__(self):
        self.dao = SourceFileDAO()
    
    def create_file(self, request: SourceFileCreateRequest) -> Tuple[bool, str, Optional[SourceFileVO]]:
        """
        创建源文件记录
        
        Args:
            request: 创建请求对象
            
        Returns:
            (成功标志, 消息, 文件VO对象)
        """
        try:
            # 检查文件名是否重复
            existing_file = self.dao.get_by_file_name(
                request.fileName, 
                request.knowledgeSpacesId
            )
            
            if existing_file:
                return False, "文件名已存在", None
            
            # 构建数据字典
            file_data = {
                'knowledge_spaces_id': request.knowledgeSpacesId,
                'file_name': request.fileName,
                'original_file_name': request.originalFileName,
                'url': request.url,
                'file_format': request.fileFormat,
                'file_suffix': request.fileSuffix,
                'file_size': request.fileSize,
                'upload_status': request.uploadStatus,
                'creator': request.creator,
                'updater': request.creator
            }
            
            # 创建文件记录
            file_model = self.dao.create(file_data)
            if file_model:
                file_vo = self._model_to_vo(file_model)
                return True, "创建成功", file_vo
            else:
                return False, "创建失败", None
                
        except Exception as e:
            logging.error(f"创建源文件记录失败: {e}")
            return False, f"创建失败: {str(e)}", None
    
    def get_file_by_id(self, file_id: int) -> Tuple[bool, str, Optional[SourceFileVO]]:
        """
        根据ID获取源文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            (成功标志, 消息, 文件VO对象)
        """
        try:
            file_model = self.dao.get_by_id(file_id)
            if not file_model:
                return False, "文件不存在", None
            
            file_vo = self._model_to_vo(file_model)
            return True, "获取成功", file_vo
            
        except Exception as e:
            logging.error(f"获取源文件失败: {e}")
            return False, f"获取失败: {str(e)}", None
    
    def get_files_by_knowledge_space(self, knowledge_spaces_id: int) -> Tuple[bool, str, List[SourceFileVO]]:
        """
        根据知识空间ID获取文件列表
        
        Args:
            knowledge_spaces_id: 知识空间ID
            
        Returns:
            (成功标志, 消息, 文件VO列表)
        """
        try:
            files = self.dao.get_by_knowledge_space(knowledge_spaces_id)
            file_vos = [self._model_to_vo(file) for file in files]
            return True, "获取成功", file_vos
            
        except Exception as e:
            logging.error(f"获取知识空间文件列表失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def search_files(self, request: SourceFileQueryRequest) -> Tuple[bool, str, Dict[str, Any]]:
        """
        搜索源文件
        
        Args:
            request: 查询请求对象
            
        Returns:
            (成功标志, 消息, 分页数据)
        """
        try:
            # 构建过滤条件
            filters = {}
            if request.knowledgeSpacesId:
                filters['knowledge_spaces_id'] = request.knowledgeSpacesId
            if request.fileName:
                filters['file_name'] = request.fileName
            if request.originalFileName:
                filters['original_file_name'] = request.originalFileName
            if request.fileFormat:
                filters['file_format'] = request.fileFormat
            if request.fileSuffix:
                filters['file_suffix'] = request.fileSuffix
            if request.uploadStatus is not None:
                filters['upload_status'] = request.uploadStatus
            if request.creator:
                filters['creator'] = request.creator
            if request.minFileSize is not None:
                filters['min_file_size'] = request.minFileSize
            if request.maxFileSize is not None:
                filters['max_file_size'] = request.maxFileSize
            
            filters['order_by'] = request.orderBy
            filters['order_desc'] = request.orderDesc
            
            # 计算分页参数
            offset = (request.page - 1) * request.pageSize
            
            # 获取数据
            files = self.dao.search_files(filters, request.pageSize, offset)
            total = self.dao.count_files(filters)
            
            file_vos = [self._model_to_vo(file) for file in files]
            
            result = {
                'list': file_vos,
                'total': total,
                'page': request.page,
                'pageSize': request.pageSize,
                'totalPages': (total + request.pageSize - 1) // request.pageSize
            }
            
            return True, "搜索成功", result
            
        except Exception as e:
            logging.error(f"搜索源文件失败: {e}")
            return False, f"搜索失败: {str(e)}", {}
    
    def update_file(self, file_id: int, request: SourceFileUpdateRequest) -> Tuple[bool, str, Optional[SourceFileVO]]:
        """
        更新源文件
        
        Args:
            file_id: 文件ID
            request: 更新请求对象
            
        Returns:
            (成功标志, 消息, 更新后的文件VO对象)
        """
        try:
            # 获取现有文件
            file_model = self.dao.get_by_id(file_id)
            if not file_model:
                return False, "文件不存在", None
            
            # 构建更新数据
            update_data = {'updater': request.updater}
            
            if request.originalFileName is not None:
                update_data['original_file_name'] = request.originalFileName
            if request.url is not None:
                update_data['url'] = request.url
            if request.fileFormat is not None:
                update_data['file_format'] = request.fileFormat
            if request.fileSuffix is not None:
                update_data['file_suffix'] = request.fileSuffix
            if request.fileSize is not None:
                update_data['file_size'] = request.fileSize
            if request.uploadStatus is not None:
                update_data['upload_status'] = request.uploadStatus
            
            # 执行更新
            success = self.dao.update(file_id, update_data)
            if success:
                updated_file = self.dao.get_by_id(file_id)
                file_vo = self._model_to_vo(updated_file)
                return True, "更新成功", file_vo
            else:
                return False, "更新失败", None
                
        except Exception as e:
            logging.error(f"更新源文件失败: {e}")
            return False, f"更新失败: {str(e)}", None
    
    def batch_update_status(self, request: SourceFileBatchUpdateStatusRequest) -> Tuple[bool, str]:
        """
        批量更新文件状态
        
        Args:
            request: 批量更新状态请求对象
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if request.uploadStatus not in [UploadStatus.UPLOADING, UploadStatus.COMPLETED, UploadStatus.FAILED]:
                return False, "无效的上传状态"
            
            updated_count = self.dao.batch_update_status(
                request.fileIds, 
                request.uploadStatus, 
                request.updater
            )
            
            if updated_count > 0:
                status_text = {1: '上传中', 2: '已完成', 3: '失败'}[request.uploadStatus]
                return True, f"成功更新{updated_count}个文件状态为{status_text}"
            else:
                return False, "没有文件需要更新"
                
        except Exception as e:
            logging.error(f"批量更新文件状态失败: {e}")
            return False, f"批量更新失败: {str(e)}"
    
    def delete_file(self, file_id: int, deleter: int, hard_delete: bool = False) -> Tuple[bool, str]:
        """
        删除源文件
        
        Args:
            file_id: 文件ID
            deleter: 删除者ID
            hard_delete: 是否硬删除
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 获取文件
            file_model = self.dao.get_by_id(file_id, include_deleted=hard_delete)
            if not file_model:
                return False, "文件不存在"
            
            # 执行删除
            if hard_delete:
                success = self.dao.hard_delete(file_id)
                message = "永久删除成功" if success else "永久删除失败"
            else:
                success = self.dao.soft_delete(file_id, deleter)
                message = "删除成功" if success else "删除失败"
            
            return success, message
            
        except Exception as e:
            logging.error(f"删除源文件失败: {e}")
            return False, f"删除失败: {str(e)}"
    
    def batch_delete_files(self, request: SourceFileBatchDeleteRequest, hard_delete: bool = False) -> Tuple[bool, str]:
        """
        批量删除源文件
        
        Args:
            request: 批量删除请求对象
            hard_delete: 是否硬删除
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if hard_delete:
                # 硬删除需要逐个删除（如果需要额外业务逻辑）
                deleted_count = 0
                for file_id in request.fileIds:
                    if self.dao.hard_delete(file_id):
                        deleted_count += 1
                message = f"永久删除成功，删除数量: {deleted_count}"
            else:
                deleted_count = self.dao.batch_soft_delete(request.fileIds, request.deleter)
                message = f"删除成功，删除数量: {deleted_count}" if deleted_count > 0 else "没有文件需要删除"
            
            return deleted_count > 0, message
            
        except Exception as e:
            logging.error(f"批量删除源文件失败: {e}")
            return False, f"批量删除失败: {str(e)}"
    
    def get_file_statistics(self, knowledge_spaces_id: Optional[int] = None, 
                           creator_id: Optional[int] = None) -> Tuple[bool, str, SourceFileStatisticsResponse]:
        """
        获取文件统计信息
        
        Args:
            knowledge_spaces_id: 知识空间ID（可选）
            creator_id: 创建人ID（可选）
            
        Returns:
            (成功标志, 消息, 统计信息)
        """
        try:
            stats = self.dao.get_statistics(knowledge_spaces_id, creator_id)
            
            statistics = SourceFileStatisticsResponse(
                totalCount=stats['total_count'],
                totalSize=stats['total_size'],
                uploadingCount=stats['uploading_count'],
                completedCount=stats['completed_count'],
                failedCount=stats['failed_count'],
                formatStats=stats['format_stats']
            )
            
            return True, "获取成功", statistics
            
        except Exception as e:
            logging.error(f"获取文件统计失败: {e}")
            return False, f"获取失败: {str(e)}", SourceFileStatisticsResponse()
    
    def validate_file_upload(self, file_format: str, file_size: int, 
                           max_size_mb: int = 100) -> Tuple[bool, str]:
        """
        验证文件上传
        
        Args:
            file_format: 文件格式（MIME类型）
            file_size: 文件大小（字节）
            max_size_mb: 最大文件大小（MB）
            
        Returns:
            (验证通过标志, 消息)
        """
        try:
            # 支持的文件格式
            allowed_formats = {
                'application/pdf', 'application/msword', 
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain', 'text/markdown', 'text/html',
                'application/json', 'application/xml',
                'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                'application/zip', 'application/x-rar-compressed'
            }
            
            # 验证文件格式
            if file_format not in allowed_formats:
                return False, f"不支持的文件格式: {file_format}"
            
            # 验证文件大小
            max_size_bytes = max_size_mb * 1024 * 1024
            if file_size > max_size_bytes:
                return False, f"文件大小超出限制，最大允许{max_size_mb}MB"
            
            if file_size <= 0:
                return False, "文件大小无效"
            
            return True, "验证通过"
            
        except Exception as e:
            logging.error(f"文件上传验证失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def get_files_by_upload_status(self, upload_status: int, limit: Optional[int] = None) -> Tuple[bool, str, List[SourceFileVO]]:
        """
        根据上传状态获取文件列表
        
        Args:
            upload_status: 上传状态
            limit: 限制数量
            
        Returns:
            (成功标志, 消息, 文件VO列表)
        """
        try:
            files = self.dao.get_files_by_upload_status(upload_status, limit)
            file_vos = [self._model_to_vo(file) for file in files]
            return True, "获取成功", file_vos
            
        except Exception as e:
            logging.error(f"根据上传状态获取文件列表失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def get_files_by_format(self, file_format: str, knowledge_spaces_id: Optional[int] = None) -> Tuple[bool, str, List[SourceFileVO]]:
        """
        根据文件格式获取文件列表
        
        Args:
            file_format: 文件格式
            knowledge_spaces_id: 知识空间ID（可选）
            
        Returns:
            (成功标志, 消息, 文件VO列表)
        """
        try:
            files = self.dao.get_files_by_format(file_format, knowledge_spaces_id)
            file_vos = [self._model_to_vo(file) for file in files]
            return True, "获取成功", file_vos
            
        except Exception as e:
            logging.error(f"根据文件格式获取文件列表失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def process_upload_completion(self, file_id: int, updater: int) -> Tuple[bool, str]:
        """
        处理文件上传完成
        
        Args:
            file_id: 文件ID
            updater: 更新人ID
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 更新文件状态为已完成
            update_data = {
                'upload_status': UploadStatus.COMPLETED,
                'updater': updater
            }
            
            success = self.dao.update(file_id, update_data)
            if success:
                return True, "文件上传完成处理成功"
            else:
                return False, "文件上传完成处理失败"
                
        except Exception as e:
            logging.error(f"处理文件上传完成失败: {e}")
            return False, f"处理失败: {str(e)}"
    
    def process_upload_failure(self, file_id: int, error_message: str, updater: int) -> Tuple[bool, str]:
        """
        处理文件上传失败
        
        Args:
            file_id: 文件ID
            error_message: 错误消息
            updater: 更新人ID
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 更新文件状态为失败
            update_data = {
                'upload_status': UploadStatus.FAILED,
                'updater': updater
            }
            
            success = self.dao.update(file_id, update_data)
            if success:
                # 记录错误日志
                logging.error(f"文件上传失败 - 文件ID: {file_id}, 错误: {error_message}")
                return True, "文件上传失败处理成功"
            else:
                return False, "文件上传失败处理失败"
                
        except Exception as e:
            logging.error(f"处理文件上传失败失败: {e}")
            return False, f"处理失败: {str(e)}"
    
    def cleanup_failed_uploads(self, days_old: int = 7) -> Tuple[bool, str, int]:
        """
        清理过期的失败上传文件
        
        Args:
            days_old: 天数阈值
            
        Returns:
            (成功标志, 消息, 清理数量)
        """
        try:
            cleaned_count = self.dao.cleanup_failed_uploads(days_old)
            return True, f"清理完成，清理了{cleaned_count}个过期失败文件", cleaned_count
            
        except Exception as e:
            logging.error(f"清理失败上传文件失败: {e}")
            return False, f"清理失败: {str(e)}", 0
    
    def _model_to_vo(self, model: SourceFileModel) -> SourceFileVO:
        """
        将Model对象转换为VO对象
        
        Args:
            model: 模型对象
            
        Returns:
            VO对象
        """
        return SourceFileVO(
            id=model.id,
            knowledgeSpacesId=model.knowledge_spaces_id,
            fileName=model.file_name,
            originalFileName=model.original_file_name,
            url=model.url,
            fileFormat=model.file_format,
            fileSuffix=model.file_suffix,
            fileSize=model.file_size,
            uploadStatus=model.upload_status,
            creator=model.creator,
            createTime=model.create_time,
            updater=model.updater,
            updateTime=model.update_time,
            deleted=model.deleted
        )