"""
处理任务服务层
提供处理任务的业务逻辑处理
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from knowledge_spaces.dao.processing_task_dao import ProcessingTaskDAO
from knowledge_spaces.model.processing_task_model import (
    ProcessingTaskModel, ProcessingTaskVO, ProcessingTaskDO,
    ProcessingTaskCreateRequest, ProcessingTaskUpdateRequest,
    ProcessingTaskQueryRequest, ProcessingTaskBatchRequest,
    TaskType, TaskStatus
)


class ProcessingTaskService:
    """处理任务服务类"""
    
    def __init__(self):
        self.dao = ProcessingTaskDAO()
    
    def create_task(self, request: ProcessingTaskCreateRequest) -> Tuple[bool, str, Optional[ProcessingTaskVO]]:
        """
        创建处理任务
        
        Args:
            request: 创建请求对象
            
        Returns:
            (成功标志, 消息, 任务VO对象)
        """
        try:
            # 检查是否已存在相同的未完成任务
            existing_tasks = self.dao.get_by_source_file_id(
                request.sourceFileId, 
                request.taskType
            )
            
            # 过滤出未完成的任务
            pending_tasks = [
                task for task in existing_tasks 
                if task.status in [TaskStatus.QUEUED, TaskStatus.IN_PROGRESS]
            ]
            
            if pending_tasks:
                return False, "该文件的相同类型任务正在处理中", None
            
            # 构建数据字典
            task_data = {
                'source_file_id': request.sourceFileId,
                'user_id': request.userId,
                'task_type': request.taskType,
                'status': TaskStatus.QUEUED,
                'payload': request.payload,
                'creator': request.creator,
                'updater': request.creator
            }
            
            # 创建任务
            task_model = self.dao.create(task_data)
            if task_model:
                task_vo = self._model_to_vo(task_model)
                return True, "创建成功", task_vo
            else:
                return False, "创建失败", None
                
        except Exception as e:
            logging.error(f"创建处理任务失败: {e}")
            return False, f"创建失败: {str(e)}", None
    
    def get_task_by_id(self, task_id: int, user_id: Optional[int] = None) -> Tuple[bool, str, Optional[ProcessingTaskVO]]:
        """
        根据ID获取处理任务
        
        Args:
            task_id: 任务ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息, 任务VO对象)
        """
        try:
            task_model = self.dao.get_by_id(task_id)
            if not task_model:
                return False, "任务不存在", None
            
            # 权限验证
            if user_id and task_model.user_id != user_id:
                return False, "无权限访问该任务", None
            
            task_vo = self._model_to_vo(task_model)
            return True, "获取成功", task_vo
            
        except Exception as e:
            logging.error(f"获取处理任务失败: {e}")
            return False, f"获取失败: {str(e)}", None
    
    def get_tasks_by_file(self, source_file_id: int, user_id: Optional[int] = None) -> Tuple[bool, str, List[ProcessingTaskVO]]:
        """
        根据源文件ID获取处理任务列表
        
        Args:
            source_file_id: 源文件ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息, 任务VO列表)
        """
        try:
            tasks = self.dao.get_by_source_file_id(source_file_id)
            
            # 权限验证
            if user_id:
                tasks = [task for task in tasks if task.user_id == user_id]
            
            task_vos = [self._model_to_vo(task) for task in tasks]
            return True, "获取成功", task_vos
            
        except Exception as e:
            logging.error(f"根据文件获取处理任务失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def get_tasks_by_user(self, user_id: int, status: Optional[int] = None) -> Tuple[bool, str, List[ProcessingTaskVO]]:
        """
        根据用户ID获取处理任务列表
        
        Args:
            user_id: 用户ID
            status: 任务状态过滤（可选）
            
        Returns:
            (成功标志, 消息, 任务VO列表)
        """
        try:
            tasks = self.dao.get_by_user_id(user_id, status)
            task_vos = [self._model_to_vo(task) for task in tasks]
            return True, "获取成功", task_vos
            
        except Exception as e:
            logging.error(f"根据用户获取处理任务失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def search_tasks(self, request: ProcessingTaskQueryRequest) -> Tuple[bool, str, Dict[str, Any]]:
        """
        搜索处理任务
        
        Args:
            request: 查询请求对象
            
        Returns:
            (成功标志, 消息, 分页数据)
        """
        try:
            # 构建过滤条件
            filters = {}
            if request.taskId:
                filters['task_id'] = request.taskId
            if request.sourceFileId:
                filters['source_file_id'] = request.sourceFileId
            if request.userId:
                filters['user_id'] = request.userId
            if request.taskType:
                filters['task_type'] = request.taskType
            if request.status:
                filters['status'] = request.status
            if request.startTimeFrom:
                filters['start_time_from'] = request.startTimeFrom
            if request.startTimeTo:
                filters['start_time_to'] = request.startTimeTo
            if request.completedTimeFrom:
                filters['completed_time_from'] = request.completedTimeFrom
            if request.completedTimeTo:
                filters['completed_time_to'] = request.completedTimeTo
            
            filters['order_by'] = request.orderBy
            filters['order_desc'] = request.orderDesc
            
            # 计算分页参数
            offset = (request.page - 1) * request.pageSize
            
            # 获取数据
            tasks = self.dao.search_tasks(filters, request.pageSize, offset)
            total = self.dao.count_tasks(filters)
            
            task_vos = [self._model_to_vo(task) for task in tasks]
            
            result = {
                'list': task_vos,
                'total': total,
                'page': request.page,
                'pageSize': request.pageSize,
                'totalPages': (total + request.pageSize - 1) // request.pageSize
            }
            
            return True, "搜索成功", result
            
        except Exception as e:
            logging.error(f"搜索处理任务失败: {e}")
            return False, f"搜索失败: {str(e)}", {}
    
    def update_task(self, task_id: int, request: ProcessingTaskUpdateRequest,
                   user_id: Optional[int] = None) -> Tuple[bool, str, Optional[ProcessingTaskVO]]:
        """
        更新处理任务
        
        Args:
            task_id: 任务ID
            request: 更新请求对象
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息, 更新后的任务VO对象)
        """
        try:
            # 获取现有任务
            task_model = self.dao.get_by_id(task_id)
            if not task_model:
                return False, "任务不存在", None
            
            # 权限验证
            if user_id and task_model.user_id != user_id:
                return False, "无权限修改该任务", None
            
            # 构建更新数据
            update_data = {'updater': request.updater}
            
            if request.status is not None:
                update_data['status'] = request.status
            if request.payload is not None:
                update_data['payload'] = request.payload
            if request.errorMessage is not None:
                update_data['error_message'] = request.errorMessage
            if request.startedAt is not None:
                update_data['started_at'] = request.startedAt
            if request.completedAt is not None:
                update_data['completed_at'] = request.completedAt
            
            # 执行更新
            success = self.dao.update(task_id, update_data)
            if success:
                updated_task = self.dao.get_by_id(task_id)
                task_vo = self._model_to_vo(updated_task)
                return True, "更新成功", task_vo
            else:
                return False, "更新失败", None
                
        except Exception as e:
            logging.error(f"更新处理任务失败: {e}")
            return False, f"更新失败: {str(e)}", None
    
    def start_task(self, task_id: int, updater: int, user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        开始执行任务
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 权限验证
            if user_id:
                task_model = self.dao.get_by_id(task_id)
                if not task_model:
                    return False, "任务不存在"
                if task_model.user_id != user_id:
                    return False, "无权限操作该任务"
                if task_model.status != TaskStatus.QUEUED:
                    return False, "任务状态不允许开始执行"
            
            success = self.dao.start_task(task_id, updater)
            return success, "开始执行成功" if success else "开始执行失败"
            
        except Exception as e:
            logging.error(f"开始执行任务失败: {e}")
            return False, f"开始执行失败: {str(e)}"
    
    def complete_task(self, task_id: int, updater: int, payload: Optional[Dict[str, Any]] = None,
                     user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        完成任务
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            payload: 任务结果载荷（可选）
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 权限验证
            if user_id:
                task_model = self.dao.get_by_id(task_id)
                if not task_model:
                    return False, "任务不存在"
                if task_model.user_id != user_id:
                    return False, "无权限操作该任务"
                if task_model.status != TaskStatus.IN_PROGRESS:
                    return False, "任务状态不允许完成"
            
            success = self.dao.complete_task(task_id, updater, payload)
            return success, "任务完成成功" if success else "任务完成失败"
            
        except Exception as e:
            logging.error(f"完成任务失败: {e}")
            return False, f"完成任务失败: {str(e)}"
    
    def fail_task(self, task_id: int, error_message: str, updater: int,
                 user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        任务失败
        
        Args:
            task_id: 任务ID
            error_message: 错误信息
            updater: 更新人ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 权限验证
            if user_id:
                task_model = self.dao.get_by_id(task_id)
                if not task_model:
                    return False, "任务不存在"
                if task_model.user_id != user_id:
                    return False, "无权限操作该任务"
                if task_model.status != TaskStatus.IN_PROGRESS:
                    return False, "任务状态不允许设为失败"
            
            success = self.dao.fail_task(task_id, error_message, updater)
            return success, "任务设置失败成功" if success else "任务设置失败失败"
            
        except Exception as e:
            logging.error(f"设置任务失败失败: {e}")
            return False, f"设置任务失败失败: {str(e)}"
    
    def cancel_task(self, task_id: int, updater: int, user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 权限验证
            if user_id:
                task_model = self.dao.get_by_id(task_id)
                if not task_model:
                    return False, "任务不存在"
                if task_model.user_id != user_id:
                    return False, "无权限操作该任务"
            
            success = self.dao.cancel_task(task_id, updater)
            return success, "取消任务成功" if success else "取消任务失败"
            
        except Exception as e:
            logging.error(f"取消任务失败: {e}")
            return False, f"取消任务失败: {str(e)}"
    
    def retry_task(self, task_id: int, updater: int, user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        重试任务
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 权限验证
            if user_id:
                task_model = self.dao.get_by_id(task_id)
                if not task_model:
                    return False, "任务不存在"
                if task_model.user_id != user_id:
                    return False, "无权限操作该任务"
                if task_model.status != TaskStatus.FAILED:
                    return False, "只有失败的任务才能重试"
            
            success = self.dao.retry_task(task_id, updater)
            return success, "重试任务成功" if success else "重试任务失败"
            
        except Exception as e:
            logging.error(f"重试任务失败: {e}")
            return False, f"重试任务失败: {str(e)}"
    
    def batch_operation(self, request: ProcessingTaskBatchRequest, 
                       user_id: Optional[int] = None) -> Tuple[bool, str, int]:
        """
        批量操作任务
        
        Args:
            request: 批量操作请求对象
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息, 成功操作数量)
        """
        try:
            # 权限验证
            if user_id:
                for task_id in request.taskIds:
                    task_model = self.dao.get_by_id(task_id)
                    if not task_model:
                        return False, f"任务{task_id}不存在", 0
                    if task_model.user_id != user_id:
                        return False, f"无权限操作任务{task_id}", 0
            
            # 验证操作类型
            if request.action not in ['cancel', 'retry', 'delete']:
                return False, "无效的操作类型", 0
            
            success_count = self.dao.batch_operation(
                request.taskIds, 
                request.action, 
                request.updater
            )
            
            total_count = len(request.taskIds)
            if success_count == total_count:
                return True, f"批量{request.action}成功", success_count
            elif success_count > 0:
                return True, f"批量{request.action}部分成功，成功{success_count}个，失败{total_count - success_count}个", success_count
            else:
                return False, f"批量{request.action}全部失败", 0
                
        except Exception as e:
            logging.error(f"批量操作任务失败: {e}")
            return False, f"批量操作失败: {str(e)}", 0
    
    def delete_task(self, task_id: int, user_id: Optional[int] = None) -> Tuple[bool, str]:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            user_id: 用户ID（用于权限验证）
            
        Returns:
            (成功标志, 消息)
        """
        try:
            # 权限验证
            if user_id:
                task_model = self.dao.get_by_id(task_id)
                if not task_model:
                    return False, "任务不存在"
                if task_model.user_id != user_id:
                    return False, "无权限删除该任务"
            
            success = self.dao.delete_task(task_id)
            return success, "删除成功" if success else "删除失败"
            
        except Exception as e:
            logging.error(f"删除任务失败: {e}")
            return False, f"删除失败: {str(e)}"
    
    def get_queued_tasks(self, task_type: Optional[int] = None, limit: int = 10) -> Tuple[bool, str, List[ProcessingTaskVO]]:
        """
        获取排队中的任务列表（用于任务调度）
        
        Args:
            task_type: 任务类型过滤（可选）
            limit: 限制数量
            
        Returns:
            (成功标志, 消息, 任务VO列表)
        """
        try:
            tasks = self.dao.get_queued_tasks(task_type, limit)
            task_vos = [self._model_to_vo(task) for task in tasks]
            return True, "获取成功", task_vos
            
        except Exception as e:
            logging.error(f"获取排队任务失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def get_timeout_tasks(self, timeout_minutes: int = 30) -> Tuple[bool, str, List[ProcessingTaskVO]]:
        """
        获取超时的处理中任务
        
        Args:
            timeout_minutes: 超时时间（分钟）
            
        Returns:
            (成功标志, 消息, 超时任务列表)
        """
        try:
            tasks = self.dao.get_timeout_tasks(timeout_minutes)
            task_vos = [self._model_to_vo(task) for task in tasks]
            return True, "获取成功", task_vos
            
        except Exception as e:
            logging.error(f"获取超时任务失败: {e}")
            return False, f"获取失败: {str(e)}", []
    
    def get_task_statistics(self, user_id: Optional[int] = None, 
                           source_file_id: Optional[int] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """
        获取任务统计信息
        
        Args:
            user_id: 用户ID（可选）
            source_file_id: 源文件ID（可选）
            
        Returns:
            (成功标志, 消息, 统计数据)
        """
        try:
            stats = self.dao.get_statistics(user_id, source_file_id)
            return True, "获取成功", stats
            
        except Exception as e:
            logging.error(f"获取任务统计失败: {e}")
            return False, f"获取失败: {str(e)}", {}
    
    def cleanup_old_tasks(self, days: int = 30, status: Optional[int] = None) -> Tuple[bool, str, int]:
        """
        清理旧任务记录
        
        Args:
            days: 天数，超过此时间的任务将被删除
            status: 任务状态过滤（可选）
            
        Returns:
            (成功标志, 消息, 清理数量)
        """
        try:
            cleanup_count = self.dao.cleanup_old_tasks(days, status)
            return True, f"清理完成，共清理{cleanup_count}条记录", cleanup_count
            
        except Exception as e:
            logging.error(f"清理旧任务记录失败: {e}")
            return False, f"清理失败: {str(e)}", 0
    
    def _model_to_vo(self, model: ProcessingTaskModel) -> ProcessingTaskVO:
        """
        将Model对象转换为VO对象
        
        Args:
            model: 模型对象
            
        Returns:
            VO对象
        """
        vo = ProcessingTaskVO(
            id=model.id,
            sourceFileId=model.source_file_id,
            userId=model.user_id,
            taskType=model.task_type,
            status=model.status,
            payload=model.payload,
            errorMessage=model.error_message,
            startedAt=model.started_at,
            completedAt=model.completed_at,
            creator=model.creator,
            createTime=model.create_time,
            updater=model.updater,
            updateTime=model.update_time
        )
        
        # 设置扩展字段
        vo.taskTypeName = TaskType.get_name(model.task_type)
        vo.statusName = TaskStatus.get_name(model.status)
        
        # 计算执行时长
        if model.started_at and model.completed_at:
            duration = (model.completed_at - model.started_at).total_seconds()
            vo.duration = int(duration)
        elif model.started_at and model.status == TaskStatus.IN_PROGRESS:
            # 正在执行中的任务计算当前时长
            duration = (datetime.now() - model.started_at).total_seconds()
            vo.duration = int(duration)
        
        return vo