"""
知识空间数据访问对象
提供knowledge_spaces表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.exc import SQLAlchemyError
from knowledge_spaces.model.source_file_model import SourceFileModel, UploadStatus
from knowledge_spaces.model.knowledge_spaces_model import KnowledgeSpaceModel
from multi_agent.model.ai_application_model import db


class KnowledgeSpaceDAO:
    """知识空间数据访问对象"""
    
    @staticmethod
    def create(space_data: Dict[str, Any]) -> Optional[KnowledgeSpaceModel]:
        """
        创建知识空间
        
        Args:
            space_data: 空间数据字典
            
        Returns:
            创建的空间模型对象，失败返回None
        """
        try:
            # 计算层级和路径
            if space_data.get('parent_id'):
                parent = KnowledgeSpaceDAO.get_by_id(space_data['parent_id'])
                if parent:
                    space_data['level'] = parent.level + 1
                    space_data['path'] = f"{parent.path},{parent.id}" if parent.path else str(parent.id)
                else:
                    logging.error(f"父空间不存在: {space_data.get('parent_id')}")
                    return None
            else:
                space_data['level'] = 0
                space_data['path'] = ''
            
            space = KnowledgeSpaceModel(**space_data)
            db.session.add(space)
            db.session.commit()
            
            # 创建完成后更新路径（包含自己的ID）
            if space.path:
                space.path = f"{space.path},{space.id}"
            else:
                space.path = str(space.id)
            db.session.commit()
            
            logging.info(f"成功创建知识空间: {space.id}")
            return space
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建知识空间失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(space_id: int, include_deleted: bool = False) -> Optional[KnowledgeSpaceModel]:
        """
        根据ID获取知识空间
        
        Args:
            space_id: 空间ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            空间模型对象，不存在返回None
        """
        try:
            query = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.id == space_id
            )
            
            if not include_deleted:
                query = query.filter(KnowledgeSpaceModel.deleted == False)
            
            return query.first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取知识空间失败: {e}")
            return None
    
    @staticmethod
    def get_by_user_id(user_id: int, parent_id: Optional[int] = None, 
                      include_deleted: bool = False) -> List[KnowledgeSpaceModel]:
        """
        根据用户ID获取知识空间列表
        
        Args:
            user_id: 用户ID
            parent_id: 父空间ID，None表示获取顶级空间
            include_deleted: 是否包含已删除的记录
            
        Returns:
            空间模型对象列表
        """
        try:
            query = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.user_id == user_id
            )
            
            if parent_id is None:
                query = query.filter(KnowledgeSpaceModel.parent_id.is_(None))
            else:
                query = query.filter(KnowledgeSpaceModel.parent_id == parent_id)
            
            if not include_deleted:
                query = query.filter(KnowledgeSpaceModel.deleted == False)
            
            return query.filter(KnowledgeSpaceModel.status == 1).order_by(
                asc(KnowledgeSpaceModel.create_time)
            ).all()
        except SQLAlchemyError as e:
            logging.error(f"根据用户ID获取知识空间失败: {e}")
            return []
    
    @staticmethod
    def get_children(parent_id: int, include_deleted: bool = False) -> List[KnowledgeSpaceModel]:
        """
        获取指定空间的子空间列表
        
        Args:
            parent_id: 父空间ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            子空间列表
        """
        try:
            query = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.parent_id == parent_id,
                KnowledgeSpaceModel.status == 1
            )
            
            if not include_deleted:
                query = query.filter(KnowledgeSpaceModel.deleted == False)
            
            return query.order_by(asc(KnowledgeSpaceModel.create_time)).all()
        except SQLAlchemyError as e:
            logging.error(f"获取子空间列表失败: {e}")
            return []
    
    @staticmethod
    def get_descendants(space_id: int, include_deleted: bool = False) -> List[KnowledgeSpaceModel]:
        """
        获取指定空间的所有后代空间
        
        Args:
            space_id: 空间ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            后代空间列表
        """
        try:
            # 先获取当前空间的路径
            current_space = KnowledgeSpaceDAO.get_by_id(space_id, include_deleted)
            if not current_space:
                return []
            
            # 构建查询条件：path包含当前空间ID
            query = db.session.query(KnowledgeSpaceModel).filter(
                or_(
                    KnowledgeSpaceModel.path.like(f"{current_space.path},%"),
                    KnowledgeSpaceModel.path.like(f"%,{space_id},%"),
                    KnowledgeSpaceModel.path.like(f"%,{space_id}")
                ),
                KnowledgeSpaceModel.id != space_id,
                KnowledgeSpaceModel.status == 1
            )
            
            if not include_deleted:
                query = query.filter(KnowledgeSpaceModel.deleted == False)
            
            return query.order_by(asc(KnowledgeSpaceModel.level), 
                               asc(KnowledgeSpaceModel.create_time)).all()
        except SQLAlchemyError as e:
            logging.error(f"获取后代空间失败: {e}")
            return []
    
    @staticmethod
    def search_spaces(filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[KnowledgeSpaceModel]:
        """
        搜索知识空间
        
        Args:
            filters: 过滤条件字典
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            空间模型对象列表
        """
        try:
            query = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.deleted == False,
                KnowledgeSpaceModel.status == 1
            )
            
            # 构建查询条件
            conditions = []
            
            # 用户ID过滤
            user_id = filters.get('user_id')
            if user_id:
                conditions.append(KnowledgeSpaceModel.user_id == user_id)
            
            # 父空间ID过滤
            parent_id = filters.get('parent_id')
            if parent_id is not None:
                if parent_id == 0:  # 0表示顶级空间
                    conditions.append(KnowledgeSpaceModel.parent_id.is_(None))
                else:
                    conditions.append(KnowledgeSpaceModel.parent_id == parent_id)
            
            # 名称模糊搜索
            name = filters.get('name')
            if name:
                conditions.append(KnowledgeSpaceModel.name.ilike(f'%{name}%'))
            
            # 层级过滤
            level = filters.get('level')
            if level is not None:
                conditions.append(KnowledgeSpaceModel.level == level)
            
            # 状态过滤
            status = filters.get('status')
            if status is not None:
                conditions.append(KnowledgeSpaceModel.status == status)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            # 排序
            order_by = filters.get('order_by', 'create_time')
            order_desc = filters.get('order_desc', True)
            
            if hasattr(KnowledgeSpaceModel, order_by):
                order_field = getattr(KnowledgeSpaceModel, order_by)
                if order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
            
            return query.offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"搜索知识空间失败: {e}")
            return []
    
    @staticmethod
    def count_spaces(filters: Dict[str, Any]) -> int:
        """
        统计满足条件的知识空间数量
        
        Args:
            filters: 过滤条件字典
            
        Returns:
            空间数量
        """
        try:
            query = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.deleted == False,
                KnowledgeSpaceModel.status == 1
            )
            
            # 构建查询条件（同search_spaces方法）
            conditions = []
            
            user_id = filters.get('user_id')
            if user_id:
                conditions.append(KnowledgeSpaceModel.user_id == user_id)
            
            parent_id = filters.get('parent_id')
            if parent_id is not None:
                if parent_id == 0:
                    conditions.append(KnowledgeSpaceModel.parent_id.is_(None))
                else:
                    conditions.append(KnowledgeSpaceModel.parent_id == parent_id)
            
            name = filters.get('name')
            if name:
                conditions.append(KnowledgeSpaceModel.name.ilike(f'%{name}%'))
            
            level = filters.get('level')
            if level is not None:
                conditions.append(KnowledgeSpaceModel.level == level)
            
            status = filters.get('status')
            if status is not None:
                conditions.append(KnowledgeSpaceModel.status == status)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"统计知识空间数量失败: {e}")
            return 0
    
    @staticmethod
    def update(space_id: int, update_data: Dict[str, Any]) -> bool:
        """
        更新知识空间信息
        
        Args:
            space_id: 空间ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            # 添加更新时间
            update_data['update_time'] = datetime.now()
            
            result = db.session.query(KnowledgeSpaceModel).filter(
                and_(
                    KnowledgeSpaceModel.id == space_id,
                    KnowledgeSpaceModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新知识空间: {space_id}")
                return True
            else:
                logging.warning(f"未找到要更新的知识空间: {space_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新知识空间失败: {e}")
            return False
    
    @staticmethod
    def move_space(space_id: int, new_parent_id: Optional[int], updater: int) -> bool:
        """
        移动知识空间到新的父空间下
        
        Args:
            space_id: 要移动的空间ID
            new_parent_id: 新的父空间ID
            updater: 更新人ID
            
        Returns:
            移动成功返回True，失败返回False
        """
        try:
            # 获取要移动的空间
            space = KnowledgeSpaceDAO.get_by_id(space_id)
            if not space:
                logging.error(f"要移动的空间不存在: {space_id}")
                return False
            
            # 防止移动到自己或自己的后代空间
            if new_parent_id:
                if new_parent_id == space_id:
                    logging.error("不能将空间移动到自己下面")
                    return False
                
                # 检查是否移动到后代空间
                descendants = KnowledgeSpaceDAO.get_descendants(space_id)
                if any(desc.id == new_parent_id for desc in descendants):
                    logging.error("不能将空间移动到自己的后代空间下")
                    return False
            
            # 计算新的层级和路径
            if new_parent_id:
                new_parent = KnowledgeSpaceDAO.get_by_id(new_parent_id)
                if not new_parent:
                    logging.error(f"新的父空间不存在: {new_parent_id}")
                    return False
                
                new_level = new_parent.level + 1
                new_path = f"{new_parent.path},{space_id}" if new_parent.path else str(space_id)
            else:
                new_level = 0
                new_path = str(space_id)
            
            # 更新空间信息
            update_data = {
                'parent_id': new_parent_id,
                'level': new_level,
                'path': new_path,
                'updater': updater,
                'update_time': datetime.now()
            }
            
            result = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.id == space_id
            ).update(update_data)
            
            if result > 0:
                # 递归更新所有后代空间的路径和层级
                KnowledgeSpaceDAO._update_descendants_path(space_id, new_level, new_path)
                
                db.session.commit()
                logging.info(f"成功移动知识空间: {space_id} -> {new_parent_id}")
                return True
            
            return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"移动知识空间失败: {e}")
            return False
    
    @staticmethod
    def _update_descendants_path(parent_id: int, parent_level: int, parent_path: str):
        """
        递归更新后代空间的路径和层级
        
        Args:
            parent_id: 父空间ID
            parent_level: 父空间层级
            parent_path: 父空间路径
        """
        try:
            children = KnowledgeSpaceDAO.get_children(parent_id)
            
            for child in children:
                new_level = parent_level + 1
                new_path = f"{parent_path},{child.id}"
                
                db.session.query(KnowledgeSpaceModel).filter(
                    KnowledgeSpaceModel.id == child.id
                ).update({
                    'level': new_level,
                    'path': new_path,
                    'update_time': datetime.now()
                })
                
                # 递归更新子空间的后代
                KnowledgeSpaceDAO._update_descendants_path(child.id, new_level, new_path)
        except SQLAlchemyError as e:
            logging.error(f"更新后代空间路径失败: {e}")
            raise e
    
    @staticmethod
    def soft_delete(space_id: int, deleter: int) -> bool:
        """
        软删除知识空间（同时删除所有后代空间）
        
        Args:
            space_id: 空间ID
            deleter: 删除者ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            # 获取所有后代空间ID
            descendants = KnowledgeSpaceDAO.get_descendants(space_id)
            space_ids = [space_id] + [desc.id for desc in descendants]
            
            # 批量软删除
            update_data = {
                'deleted': True,
                'updater': deleter,
                'update_time': datetime.now()
            }
            
            result = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.id.in_(space_ids)
            ).update(update_data, synchronize_session=False)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功软删除知识空间及其后代: {space_ids}")
                return True
            else:
                logging.warning(f"未找到要删除的知识空间: {space_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"软删除知识空间失败: {e}")
            return False
    
    @staticmethod
    def hard_delete(space_id: int) -> bool:
        """
        硬删除知识空间（同时删除所有后代空间）
        
        Args:
            space_id: 空间ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            # 获取所有后代空间ID
            descendants = KnowledgeSpaceDAO.get_descendants(space_id, include_deleted=True)
            space_ids = [space_id] + [desc.id for desc in descendants]
            
            # 批量硬删除
            result = db.session.query(KnowledgeSpaceModel).filter(
                KnowledgeSpaceModel.id.in_(space_ids)
            ).delete(synchronize_session=False)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功硬删除知识空间及其后代: {space_ids}")
                return True
            else:
                logging.warning(f"未找到要删除的知识空间: {space_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"硬删除知识空间失败: {e}")
            return False
    
    @staticmethod
    def count_children(space_id: int) -> int:
        """
        统计指定空间的直接子空间数量
        
        Args:
            space_id: 空间ID
            
        Returns:
            子空间数量
        """
        try:
            return db.session.query(KnowledgeSpaceModel).filter(
                and_(
                    KnowledgeSpaceModel.parent_id == space_id,
                    KnowledgeSpaceModel.deleted == False,
                    KnowledgeSpaceModel.status == 1
                )
            ).count()
        except SQLAlchemyError as e:
            logging.error(f"统计子空间数量失败: {e}")
            return 0
    
    @staticmethod
    def get_space_tree(user_id: int, max_level: Optional[int] = None) -> List[KnowledgeSpaceModel]:
        """
        获取用户的知识空间树结构
        
        Args:
            user_id: 用户ID
            max_level: 最大层级深度
            
        Returns:
            空间列表（按层级排序）
        """
        try:
            query = db.session.query(KnowledgeSpaceModel).filter(
                and_(
                    KnowledgeSpaceModel.user_id == user_id,
                    KnowledgeSpaceModel.deleted == False,
                    KnowledgeSpaceModel.status == 1
                )
            )
            
            if max_level is not None:
                query = query.filter(KnowledgeSpaceModel.level <= max_level)
            
            return query.order_by(
                asc(KnowledgeSpaceModel.level),
                asc(KnowledgeSpaceModel.create_time)
            ).all()
        except SQLAlchemyError as e:
            logging.error(f"获取空间树结构失败: {e}")
            return []


class SourceFileDAO:
    """源文件数据访问对象"""
    
    @staticmethod
    def create(file_data: Dict[str, Any]) -> Optional[SourceFileModel]:
        """
        创建源文件记录
        
        Args:
            file_data: 文件数据字典
            
        Returns:
            创建的文件模型对象，失败返回None
        """
        try:
            file_model = SourceFileModel(**file_data)
            db.session.add(file_model)
            db.session.commit()
            logging.info(f"成功创建源文件记录: {file_model.id}")
            return file_model
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建源文件记录失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(file_id: int, include_deleted: bool = False) -> Optional[SourceFileModel]:
        """
        根据ID获取源文件
        
        Args:
            file_id: 文件ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            文件模型对象，不存在返回None
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                SourceFileModel.id == file_id
            )
            
            if not include_deleted:
                query = query.filter(SourceFileModel.deleted == False)
            
            return query.first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取源文件失败: {e}")
            return None
    
    @staticmethod
    def get_by_knowledge_space(knowledge_spaces_id: int, include_deleted: bool = False) -> List[SourceFileModel]:
        """
        根据知识空间ID获取文件列表
        
        Args:
            knowledge_spaces_id: 知识空间ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            文件模型对象列表
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                SourceFileModel.knowledge_spaces_id == knowledge_spaces_id
            )
            
            if not include_deleted:
                query = query.filter(SourceFileModel.deleted == False)
            
            return query.order_by(desc(SourceFileModel.create_time)).all()
        except SQLAlchemyError as e:
            logging.error(f"根据知识空间ID获取文件列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_creator(creator_id: int, include_deleted: bool = False) -> List[SourceFileModel]:
        """
        根据创建人ID获取文件列表
        
        Args:
            creator_id: 创建人ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            文件模型对象列表
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                SourceFileModel.creator == creator_id
            )
            
            if not include_deleted:
                query = query.filter(SourceFileModel.deleted == False)
            
            return query.order_by(desc(SourceFileModel.create_time)).all()
        except SQLAlchemyError as e:
            logging.error(f"根据创建人ID获取文件列表失败: {e}")
            return []
    
    @staticmethod
    def search_files(filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[SourceFileModel]:
        """
        搜索源文件
        
        Args:
            filters: 过滤条件字典
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            文件模型对象列表
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                SourceFileModel.deleted == False
            )
            
            # 构建查询条件
            conditions = []
            
            # 知识空间ID过滤
            knowledge_spaces_id = filters.get('knowledge_spaces_id')
            if knowledge_spaces_id:
                conditions.append(SourceFileModel.knowledge_spaces_id == knowledge_spaces_id)
            
            # 文件名搜索
            file_name = filters.get('file_name')
            if file_name:
                conditions.append(SourceFileModel.file_name.ilike(f'%{file_name}%'))
            
            # 原始文件名搜索
            original_file_name = filters.get('original_file_name')
            if original_file_name:
                conditions.append(SourceFileModel.original_file_name.ilike(f'%{original_file_name}%'))
            
            # 文件格式过滤
            file_format = filters.get('file_format')
            if file_format:
                conditions.append(SourceFileModel.file_format == file_format)
            
            # 文件后缀过滤
            file_suffix = filters.get('file_suffix')
            if file_suffix:
                conditions.append(SourceFileModel.file_suffix == file_suffix)
            
            # 上传状态过滤
            upload_status = filters.get('upload_status')
            if upload_status is not None:
                conditions.append(SourceFileModel.upload_status == upload_status)
            
            # 创建人过滤
            creator = filters.get('creator')
            if creator:
                conditions.append(SourceFileModel.creator == creator)
            
            # 文件大小范围过滤
            min_file_size = filters.get('min_file_size')
            if min_file_size is not None:
                conditions.append(SourceFileModel.file_size >= min_file_size)
            
            max_file_size = filters.get('max_file_size')
            if max_file_size is not None:
                conditions.append(SourceFileModel.file_size <= max_file_size)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            # 排序
            order_by = filters.get('order_by', 'create_time')
            order_desc = filters.get('order_desc', True)
            
            if hasattr(SourceFileModel, order_by):
                order_field = getattr(SourceFileModel, order_by)
                if order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
            
            return query.offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"搜索源文件失败: {e}")
            return []
    
    @staticmethod
    def count_files(filters: Dict[str, Any]) -> int:
        """
        统计满足条件的文件数量
        
        Args:
            filters: 过滤条件字典
            
        Returns:
            文件数量
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                SourceFileModel.deleted == False
            )
            
            # 构建查询条件（同search_files方法）
            conditions = []
            
            knowledge_spaces_id = filters.get('knowledge_spaces_id')
            if knowledge_spaces_id:
                conditions.append(SourceFileModel.knowledge_spaces_id == knowledge_spaces_id)
            
            file_name = filters.get('file_name')
            if file_name:
                conditions.append(SourceFileModel.file_name.ilike(f'%{file_name}%'))
            
            original_file_name = filters.get('original_file_name')
            if original_file_name:
                conditions.append(SourceFileModel.original_file_name.ilike(f'%{original_file_name}%'))
            
            file_format = filters.get('file_format')
            if file_format:
                conditions.append(SourceFileModel.file_format == file_format)
            
            file_suffix = filters.get('file_suffix')
            if file_suffix:
                conditions.append(SourceFileModel.file_suffix == file_suffix)
            
            upload_status = filters.get('upload_status')
            if upload_status is not None:
                conditions.append(SourceFileModel.upload_status == upload_status)
            
            creator = filters.get('creator')
            if creator:
                conditions.append(SourceFileModel.creator == creator)
            
            min_file_size = filters.get('min_file_size')
            if min_file_size is not None:
                conditions.append(SourceFileModel.file_size >= min_file_size)
            
            max_file_size = filters.get('max_file_size')
            if max_file_size is not None:
                conditions.append(SourceFileModel.file_size <= max_file_size)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"统计源文件数量失败: {e}")
            return 0
    
    @staticmethod
    def update(file_id: int, update_data: Dict[str, Any]) -> bool:
        """
        更新源文件信息
        
        Args:
            file_id: 文件ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            # 添加更新时间
            update_data['update_time'] = datetime.now()
            
            result = db.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id == file_id,
                    SourceFileModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新源文件: {file_id}")
                return True
            else:
                logging.warning(f"未找到要更新的源文件: {file_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新源文件失败: {e}")
            return False
    
    @staticmethod
    def batch_update_status(file_ids: List[int], upload_status: int, updater: int) -> int:
        """
        批量更新文件上传状态
        
        Args:
            file_ids: 文件ID列表
            upload_status: 新的上传状态
            updater: 更新人ID
            
        Returns:
            成功更新的文件数量
        """
        try:
            update_data = {
                'upload_status': upload_status,
                'updater': updater,
                'update_time': datetime.now()
            }
            
            result = db.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id.in_(file_ids),
                    SourceFileModel.deleted == False
                )
            ).update(update_data, synchronize_session=False)
            
            db.session.commit()
            logging.info(f"批量更新文件状态成功，更新数量: {result}")
            return result
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"批量更新文件状态失败: {e}")
            return 0
    
    @staticmethod
    def soft_delete(file_id: int, deleter: int) -> bool:
        """
        软删除源文件
        
        Args:
            file_id: 文件ID
            deleter: 删除者ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            update_data = {
                'deleted': True,
                'updater': deleter,
                'update_time': datetime.now()
            }
            
            result = db.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id == file_id,
                    SourceFileModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功软删除源文件: {file_id}")
                return True
            else:
                logging.warning(f"未找到要删除的源文件: {file_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"软删除源文件失败: {e}")
            return False
    
    @staticmethod
    def batch_soft_delete(file_ids: List[int], deleter: int) -> int:
        """
        批量软删除源文件
        
        Args:
            file_ids: 文件ID列表
            deleter: 删除者ID
            
        Returns:
            成功删除的文件数量
        """
        try:
            update_data = {
                'deleted': True,
                'updater': deleter,
                'update_time': datetime.now()
            }
            
            result = db.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id.in_(file_ids),
                    SourceFileModel.deleted == False
                )
            ).update(update_data, synchronize_session=False)
            
            db.session.commit()
            logging.info(f"批量软删除源文件成功，删除数量: {result}")
            return result
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"批量软删除源文件失败: {e}")
            return 0
    
    @staticmethod
    def hard_delete(file_id: int) -> bool:
        """
        硬删除源文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(SourceFileModel).filter(
                SourceFileModel.id == file_id
            ).delete()
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功硬删除源文件: {file_id}")
                return True
            else:
                logging.warning(f"未找到要删除的源文件: {file_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"硬删除源文件失败: {e}")
            return False
    
    @staticmethod
    def get_statistics(knowledge_spaces_id: Optional[int] = None, creator_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取文件统计信息
        
        Args:
            knowledge_spaces_id: 知识空间ID（可选）
            creator_id: 创建人ID（可选）
            
        Returns:
            统计信息字典
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                SourceFileModel.deleted == False
            )
            
            # 添加过滤条件
            if knowledge_spaces_id:
                query = query.filter(SourceFileModel.knowledge_spaces_id == knowledge_spaces_id)
            if creator_id:
                query = query.filter(SourceFileModel.creator == creator_id)
            
            # 总数统计
            total_count = query.count()
            
            # 文件大小统计
            total_size = query.with_entities(func.sum(SourceFileModel.file_size)).scalar() or 0
            
            # 按状态统计
            status_stats = query.with_entities(
                SourceFileModel.upload_status,
                func.count(SourceFileModel.id)
            ).group_by(SourceFileModel.upload_status).all()
            
            uploading_count = 0
            completed_count = 0
            failed_count = 0
            
            for status, count in status_stats:
                if status == UploadStatus.UPLOADING:
                    uploading_count = count
                elif status == UploadStatus.COMPLETED:
                    completed_count = count
                elif status == UploadStatus.FAILED:
                    failed_count = count
            
            # 按格式统计
            format_stats = query.with_entities(
                SourceFileModel.file_format,
                func.count(SourceFileModel.id)
            ).group_by(SourceFileModel.file_format).all()
            
            format_dict = {format_type: count for format_type, count in format_stats}
            
            return {
                'total_count': total_count,
                'total_size': total_size,
                'uploading_count': uploading_count,
                'completed_count': completed_count,
                'failed_count': failed_count,
                'format_stats': format_dict
            }
        except SQLAlchemyError as e:
            logging.error(f"获取文件统计信息失败: {e}")
            return {
                'total_count': 0,
                'total_size': 0,
                'uploading_count': 0,
                'completed_count': 0,
                'failed_count': 0,
                'format_stats': {}
            }
    
    @staticmethod
    def get_by_file_name(file_name: str, knowledge_spaces_id: Optional[int] = None) -> Optional[SourceFileModel]:
        """
        根据文件名获取源文件（用于检查重复）
        
        Args:
            file_name: 系统生成的文件名
            knowledge_spaces_id: 知识空间ID（可选）
            
        Returns:
            文件模型对象，不存在返回None
        """
        try:
            query = db.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.file_name == file_name,
                    SourceFileModel.deleted == False
                )
            )
            
            if knowledge_spaces_id:
                query = query.filter(SourceFileModel.knowledge_spaces_id == knowledge_spaces_id)
            
            return query.first()
        except SQLAlchemyError as e:
            logging.error(f"根据文件名获取源文件失败: {e}")
            return None
    
    @staticmethod
    def cleanup_failed_uploads(hours: int = 24) -> int:
        """
        清理失败的上传记录（超过指定小时数）
        
        Args:
            hours: 小时数，超过此时间的失败记录将被删除
            
        Returns:
            清理的记录数量
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            result = db.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.upload_status == UploadStatus.FAILED,
                    SourceFileModel.create_time < cutoff_time,
                    SourceFileModel.deleted == False
                )
            ).update({
                'deleted': True,
                'update_time': datetime.now()
            })
            
            db.session.commit()
            logging.info(f"清理失败上传记录成功，清理数量: {result}")
            return result
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"清理失败上传记录失败: {e}")
            return 0