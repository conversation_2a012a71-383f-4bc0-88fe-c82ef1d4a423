"""
处理任务数据访问对象
提供processing_tasks表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.exc import SQLAlchemyError
from knowledge_spaces.model.processing_task_model import ProcessingTaskModel, TaskType, TaskStatus
from multi_agent.model.ai_application_model import db


class ProcessingTaskDAO:
    """处理任务数据访问对象"""
    
    @staticmethod
    def create(task_data: Dict[str, Any]) -> Optional[ProcessingTaskModel]:
        """
        创建处理任务
        
        Args:
            task_data: 任务数据字典
            
        Returns:
            创建的任务模型对象，失败返回None
        """
        try:
            task = ProcessingTaskModel(**task_data)
            db.session.add(task)
            db.session.commit()
            
            logging.info(f"成功创建处理任务: {task.id}")
            return task
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建处理任务失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(task_id: int) -> Optional[ProcessingTaskModel]:
        """
        根据ID获取处理任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务模型对象，不存在返回None
        """
        try:
            return db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.id == task_id
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取处理任务失败: {e}")
            return None
    
    @staticmethod
    def get_by_source_file_id(source_file_id: int, task_type: Optional[int] = None) -> List[ProcessingTaskModel]:
        """
        根据源文件ID获取处理任务列表
        
        Args:
            source_file_id: 源文件ID
            task_type: 任务类型过滤（可选）
            
        Returns:
            任务模型对象列表
        """
        try:
            query = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.source_file_id == source_file_id
            )
            
            if task_type:
                query = query.filter(ProcessingTaskModel.task_type == task_type)
            
            return query.order_by(desc(ProcessingTaskModel.create_time)).all()
        except SQLAlchemyError as e:
            logging.error(f"根据源文件ID获取处理任务失败: {e}")
            return []
    
    @staticmethod
    def get_by_user_id(user_id: int, status: Optional[int] = None) -> List[ProcessingTaskModel]:
        """
        根据用户ID获取处理任务列表
        
        Args:
            user_id: 用户ID
            status: 任务状态过滤（可选）
            
        Returns:
            任务模型对象列表
        """
        try:
            query = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.user_id == user_id
            )
            
            if status:
                query = query.filter(ProcessingTaskModel.status == status)
            
            return query.order_by(desc(ProcessingTaskModel.create_time)).all()
        except SQLAlchemyError as e:
            logging.error(f"根据用户ID获取处理任务失败: {e}")
            return []
    
    @staticmethod
    def get_queued_tasks(task_type: Optional[int] = None, limit: int = 10) -> List[ProcessingTaskModel]:
        """
        获取排队中的任务列表
        
        Args:
            task_type: 任务类型过滤（可选）
            limit: 限制数量
            
        Returns:
            排队任务列表
        """
        try:
            query = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.status == TaskStatus.QUEUED
            )
            
            if task_type:
                query = query.filter(ProcessingTaskModel.task_type == task_type)
            
            return query.order_by(asc(ProcessingTaskModel.create_time)).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"获取排队任务失败: {e}")
            return []
    
    @staticmethod
    def get_in_progress_tasks(task_type: Optional[int] = None) -> List[ProcessingTaskModel]:
        """
        获取处理中的任务列表
        
        Args:
            task_type: 任务类型过滤（可选）
            
        Returns:
            处理中任务列表
        """
        try:
            query = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.status == TaskStatus.IN_PROGRESS
            )
            
            if task_type:
                query = query.filter(ProcessingTaskModel.task_type == task_type)
            
            return query.order_by(asc(ProcessingTaskModel.started_at)).all()
        except SQLAlchemyError as e:
            logging.error(f"获取处理中任务失败: {e}")
            return []
    
    @staticmethod
    def search_tasks(filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[ProcessingTaskModel]:
        """
        搜索处理任务
        
        Args:
            filters: 过滤条件字典
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            任务模型对象列表
        """
        try:
            query = db.session.query(ProcessingTaskModel)
            
            # 构建查询条件
            conditions = []
            
            # 任务ID过滤
            task_id = filters.get('task_id')
            if task_id:
                conditions.append(ProcessingTaskModel.id == task_id)
            
            # 源文件ID过滤
            source_file_id = filters.get('source_file_id')
            if source_file_id:
                conditions.append(ProcessingTaskModel.source_file_id == source_file_id)
            
            # 用户ID过滤
            user_id = filters.get('user_id')
            if user_id:
                conditions.append(ProcessingTaskModel.user_id == user_id)
            
            # 任务类型过滤
            task_type = filters.get('task_type')
            if task_type:
                conditions.append(ProcessingTaskModel.task_type == task_type)
            
            # 任务状态过滤
            status = filters.get('status')
            if status:
                conditions.append(ProcessingTaskModel.status == status)
            
            # 开始时间范围过滤
            start_time_from = filters.get('start_time_from')
            if start_time_from:
                conditions.append(ProcessingTaskModel.started_at >= start_time_from)
            
            start_time_to = filters.get('start_time_to')
            if start_time_to:
                conditions.append(ProcessingTaskModel.started_at <= start_time_to)
            
            # 完成时间范围过滤
            completed_time_from = filters.get('completed_time_from')
            if completed_time_from:
                conditions.append(ProcessingTaskModel.completed_at >= completed_time_from)
            
            completed_time_to = filters.get('completed_time_to')
            if completed_time_to:
                conditions.append(ProcessingTaskModel.completed_at <= completed_time_to)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            # 排序
            order_by = filters.get('order_by', 'create_time')
            order_desc = filters.get('order_desc', True)
            
            if hasattr(ProcessingTaskModel, order_by):
                order_field = getattr(ProcessingTaskModel, order_by)
                if order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
            
            return query.offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"搜索处理任务失败: {e}")
            return []
    
    @staticmethod
    def count_tasks(filters: Dict[str, Any]) -> int:
        """
        统计满足条件的任务数量
        
        Args:
            filters: 过滤条件字典
            
        Returns:
            任务数量
        """
        try:
            query = db.session.query(ProcessingTaskModel)
            
            # 构建查询条件（同search_tasks方法）
            conditions = []
            
            task_id = filters.get('task_id')
            if task_id:
                conditions.append(ProcessingTaskModel.id == task_id)
            
            source_file_id = filters.get('source_file_id')
            if source_file_id:
                conditions.append(ProcessingTaskModel.source_file_id == source_file_id)
            
            user_id = filters.get('user_id')
            if user_id:
                conditions.append(ProcessingTaskModel.user_id == user_id)
            
            task_type = filters.get('task_type')
            if task_type:
                conditions.append(ProcessingTaskModel.task_type == task_type)
            
            status = filters.get('status')
            if status:
                conditions.append(ProcessingTaskModel.status == status)
            
            start_time_from = filters.get('start_time_from')
            if start_time_from:
                conditions.append(ProcessingTaskModel.started_at >= start_time_from)
            
            start_time_to = filters.get('start_time_to')
            if start_time_to:
                conditions.append(ProcessingTaskModel.started_at <= start_time_to)
            
            completed_time_from = filters.get('completed_time_from')
            if completed_time_from:
                conditions.append(ProcessingTaskModel.completed_at >= completed_time_from)
            
            completed_time_to = filters.get('completed_time_to')
            if completed_time_to:
                conditions.append(ProcessingTaskModel.completed_at <= completed_time_to)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"统计处理任务数量失败: {e}")
            return 0
    
    @staticmethod
    def update(task_id: int, update_data: Dict[str, Any]) -> bool:
        """
        更新处理任务信息
        
        Args:
            task_id: 任务ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            # 添加更新时间
            update_data['update_time'] = datetime.now()
            
            result = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.id == task_id
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新处理任务: {task_id}")
                return True
            else:
                logging.warning(f"未找到要更新的处理任务: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新处理任务失败: {e}")
            return False
    
    @staticmethod
    def start_task(task_id: int, updater: int) -> bool:
        """
        开始执行任务（更新状态为处理中并设置开始时间）
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            update_data = {
                'status': TaskStatus.IN_PROGRESS,
                'started_at': datetime.now(),
                'updater': updater
            }
            
            result = db.session.query(ProcessingTaskModel).filter(
                and_(
                    ProcessingTaskModel.id == task_id,
                    ProcessingTaskModel.status == TaskStatus.QUEUED
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功开始处理任务: {task_id}")
                return True
            else:
                logging.warning(f"任务不存在或状态不正确: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"开始处理任务失败: {e}")
            return False
    
    @staticmethod
    def complete_task(task_id: int, updater: int, payload: Optional[Dict[str, Any]] = None) -> bool:
        """
        完成任务（更新状态为已完成并设置完成时间）
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            payload: 任务结果载荷（可选）
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            update_data = {
                'status': TaskStatus.COMPLETED,
                'completed_at': datetime.now(),
                'updater': updater
            }
            
            if payload:
                update_data['payload'] = payload
            
            result = db.session.query(ProcessingTaskModel).filter(
                and_(
                    ProcessingTaskModel.id == task_id,
                    ProcessingTaskModel.status == TaskStatus.IN_PROGRESS
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功完成处理任务: {task_id}")
                return True
            else:
                logging.warning(f"任务不存在或状态不正确: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"完成处理任务失败: {e}")
            return False
    
    @staticmethod
    def fail_task(task_id: int, error_message: str, updater: int) -> bool:
        """
        任务失败（更新状态为失败并记录错误信息）
        
        Args:
            task_id: 任务ID
            error_message: 错误信息
            updater: 更新人ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            update_data = {
                'status': TaskStatus.FAILED,
                'error_message': error_message,
                'completed_at': datetime.now(),
                'updater': updater
            }
            
            result = db.session.query(ProcessingTaskModel).filter(
                and_(
                    ProcessingTaskModel.id == task_id,
                    ProcessingTaskModel.status == TaskStatus.IN_PROGRESS
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"任务处理失败: {task_id}, 错误: {error_message}")
                return True
            else:
                logging.warning(f"任务不存在或状态不正确: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新任务失败状态失败: {e}")
            return False
    
    @staticmethod
    def cancel_task(task_id: int, updater: int) -> bool:
        """
        取消任务（将排队中或处理中的任务设置为失败状态）
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            update_data = {
                'status': TaskStatus.FAILED,
                'error_message': '任务被取消',
                'completed_at': datetime.now(),
                'updater': updater
            }
            
            result = db.session.query(ProcessingTaskModel).filter(
                and_(
                    ProcessingTaskModel.id == task_id,
                    ProcessingTaskModel.status.in_([TaskStatus.QUEUED, TaskStatus.IN_PROGRESS])
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功取消处理任务: {task_id}")
                return True
            else:
                logging.warning(f"任务不存在或状态不允许取消: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"取消处理任务失败: {e}")
            return False
    
    @staticmethod
    def retry_task(task_id: int, updater: int) -> bool:
        """
        重试失败的任务（将失败任务重新设置为排队状态）
        
        Args:
            task_id: 任务ID
            updater: 更新人ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            update_data = {
                'status': TaskStatus.QUEUED,
                'error_message': None,
                'started_at': None,
                'completed_at': None,
                'updater': updater
            }
            
            result = db.session.query(ProcessingTaskModel).filter(
                and_(
                    ProcessingTaskModel.id == task_id,
                    ProcessingTaskModel.status == TaskStatus.FAILED
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功重试处理任务: {task_id}")
                return True
            else:
                logging.warning(f"任务不存在或状态不允许重试: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"重试处理任务失败: {e}")
            return False
    
    @staticmethod
    def batch_operation(task_ids: List[int], action: str, updater: int) -> int:
        """
        批量操作任务
        
        Args:
            task_ids: 任务ID列表
            action: 操作类型 ('cancel', 'retry', 'delete')
            updater: 操作人ID
            
        Returns:
            成功操作的任务数量
        """
        try:
            success_count = 0
            
            for task_id in task_ids:
                if action == 'cancel':
                    if ProcessingTaskDAO.cancel_task(task_id, updater):
                        success_count += 1
                elif action == 'retry':
                    if ProcessingTaskDAO.retry_task(task_id, updater):
                        success_count += 1
                elif action == 'delete':
                    if ProcessingTaskDAO.delete_task(task_id):
                        success_count += 1
            
            logging.info(f"批量{action}任务完成，成功数量: {success_count}")
            return success_count
        except Exception as e:
            logging.error(f"批量操作任务失败: {e}")
            return 0
    
    @staticmethod
    def delete_task(task_id: int) -> bool:
        """
        删除任务（硬删除）
        
        Args:
            task_id: 任务ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.id == task_id
            ).delete()
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功删除处理任务: {task_id}")
                return True
            else:
                logging.warning(f"未找到要删除的处理任务: {task_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"删除处理任务失败: {e}")
            return False
    
    @staticmethod
    def cleanup_old_tasks(days: int = 30, status: Optional[int] = None) -> int:
        """
        清理旧任务记录
        
        Args:
            days: 天数，超过此时间的任务将被删除
            status: 任务状态过滤（可选，为空时清理所有已完成和失败的任务）
            
        Returns:
            清理的任务数量
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            query = db.session.query(ProcessingTaskModel).filter(
                ProcessingTaskModel.create_time < cutoff_time
            )
            
            if status:
                query = query.filter(ProcessingTaskModel.status == status)
            else:
                # 默认只清理已完成和失败的任务
                query = query.filter(
                    ProcessingTaskModel.status.in_([TaskStatus.COMPLETED, TaskStatus.FAILED])
                )
            
            result = query.delete(synchronize_session=False)
            
            db.session.commit()
            logging.info(f"清理旧任务记录成功，清理数量: {result}")
            return result
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"清理旧任务记录失败: {e}")
            return 0
    
    @staticmethod
    def get_statistics(user_id: Optional[int] = None, source_file_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Args:
            user_id: 用户ID（可选）
            source_file_id: 源文件ID（可选）
            
        Returns:
            统计信息字典
        """
        try:
            query = db.session.query(ProcessingTaskModel)
            
            # 添加过滤条件
            if user_id:
                query = query.filter(ProcessingTaskModel.user_id == user_id)
            if source_file_id:
                query = query.filter(ProcessingTaskModel.source_file_id == source_file_id)
            
            # 总数统计
            total_count = query.count()
            
            # 按状态统计
            status_stats = query.with_entities(
                ProcessingTaskModel.status,
                func.count(ProcessingTaskModel.id)
            ).group_by(ProcessingTaskModel.status).all()
            
            queued_count = 0
            in_progress_count = 0
            completed_count = 0
            failed_count = 0
            
            for status, count in status_stats:
                if status == TaskStatus.QUEUED:
                    queued_count = count
                elif status == TaskStatus.IN_PROGRESS:
                    in_progress_count = count
                elif status == TaskStatus.COMPLETED:
                    completed_count = count
                elif status == TaskStatus.FAILED:
                    failed_count = count
            
            # 按类型统计
            type_stats = query.with_entities(
                ProcessingTaskModel.task_type,
                func.count(ProcessingTaskModel.id)
            ).group_by(ProcessingTaskModel.task_type).all()
            
            type_dict = {}
            for task_type, count in type_stats:
                type_name = TaskType.get_name(task_type)
                type_dict[type_name] = count
            
            return {
                'total_count': total_count,
                'queued_count': queued_count,
                'in_progress_count': in_progress_count,
                'completed_count': completed_count,
                'failed_count': failed_count,
                'type_stats': type_dict
            }
        except SQLAlchemyError as e:
            logging.error(f"获取任务统计信息失败: {e}")
            return {
                'total_count': 0,
                'queued_count': 0,
                'in_progress_count': 0,
                'completed_count': 0,
                'failed_count': 0,
                'type_stats': {}
            }
    
    @staticmethod
    def get_timeout_tasks(timeout_minutes: int = 30) -> List[ProcessingTaskModel]:
        """
        获取超时的处理中任务
        
        Args:
            timeout_minutes: 超时时间（分钟）
            
        Returns:
            超时任务列表
        """
        try:
            timeout_time = datetime.now() - timedelta(minutes=timeout_minutes)
            
            return db.session.query(ProcessingTaskModel).filter(
                and_(
                    ProcessingTaskModel.status == TaskStatus.IN_PROGRESS,
                    ProcessingTaskModel.started_at < timeout_time
                )
            ).all()
        except SQLAlchemyError as e:
            logging.error(f"获取超时任务失败: {e}")
            return []