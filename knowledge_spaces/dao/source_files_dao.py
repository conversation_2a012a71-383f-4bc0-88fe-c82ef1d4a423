"""
源文件数据访问层
提供源文件的数据库操作功能
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, func, text
from sqlalchemy.orm import sessionmaker

from knowledge_spaces.model.source_file_model import SourceFileModel, UploadStatus
from multi_agent.model.ai_application_model import db


class SourceFileDAO:
    """源文件数据访问对象"""
    
    def __init__(self):
        self.db = db
        self.session = db.session
    
    def create(self, data: Dict[str, Any]) -> Optional[SourceFileModel]:
        """
        创建源文件记录
        
        Args:
            data: 文件数据字典
            
        Returns:
            创建的文件模型对象
        """
        try:
            file_model = SourceFileModel(**data)
            self.session.add(file_model)
            self.session.commit()
            return file_model
        except Exception as e:
            self.session.rollback()
            logging.error(f"创建源文件记录失败: {e}")
            return None
    
    def get_by_id(self, file_id: int, include_deleted: bool = False) -> Optional[SourceFileModel]:
        """
        根据ID获取源文件
        
        Args:
            file_id: 文件ID
            include_deleted: 是否包含已删除的记录
            
        Returns:
            文件模型对象
        """
        try:
            query = self.session.query(SourceFileModel).filter(SourceFileModel.id == file_id)
            
            if not include_deleted:
                query = query.filter(SourceFileModel.deleted == False)
            
            return query.first()
        except Exception as e:
            logging.error(f"根据ID获取源文件失败: {e}")
            return None
    
    def get_by_file_name(self, file_name: str, knowledge_spaces_id: int) -> Optional[SourceFileModel]:
        """
        根据文件名和知识空间ID获取源文件
        
        Args:
            file_name: 文件名
            knowledge_spaces_id: 知识空间ID
            
        Returns:
            文件模型对象
        """
        try:
            return self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.file_name == file_name,
                    SourceFileModel.knowledge_spaces_id == knowledge_spaces_id,
                    SourceFileModel.deleted == False
                )
            ).first()
        except Exception as e:
            logging.error(f"根据文件名获取源文件失败: {e}")
            return None
    
    def get_by_knowledge_space(self, knowledge_spaces_id: int) -> List[SourceFileModel]:
        """
        根据知识空间ID获取文件列表
        
        Args:
            knowledge_spaces_id: 知识空间ID
            
        Returns:
            文件模型列表
        """
        try:
            return self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.knowledge_spaces_id == knowledge_spaces_id,
                    SourceFileModel.deleted == False
                )
            ).order_by(SourceFileModel.create_time.desc()).all()
        except Exception as e:
            logging.error(f"根据知识空间获取文件列表失败: {e}")
            return []
    
    def search_files(self, filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[SourceFileModel]:
        """
        搜索源文件
        
        Args:
            filters: 过滤条件
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            文件模型列表
        """
        try:
            query = self.session.query(SourceFileModel).filter(SourceFileModel.deleted == False)
            
            # 应用过滤条件
            if 'knowledge_spaces_id' in filters and filters['knowledge_spaces_id']:
                query = query.filter(SourceFileModel.knowledge_spaces_id == filters['knowledge_spaces_id'])
            
            if 'file_name' in filters and filters['file_name']:
                query = query.filter(SourceFileModel.file_name.like(f"%{filters['file_name']}%"))
            
            if 'original_file_name' in filters and filters['original_file_name']:
                query = query.filter(SourceFileModel.original_file_name.like(f"%{filters['original_file_name']}%"))
            
            if 'file_format' in filters and filters['file_format']:
                query = query.filter(SourceFileModel.file_format == filters['file_format'])
            
            if 'file_suffix' in filters and filters['file_suffix']:
                query = query.filter(SourceFileModel.file_suffix == filters['file_suffix'])
            
            if 'upload_status' in filters and filters['upload_status'] is not None:
                query = query.filter(SourceFileModel.upload_status == filters['upload_status'])
            
            if 'creator' in filters and filters['creator']:
                query = query.filter(SourceFileModel.creator == filters['creator'])
            
            if 'min_file_size' in filters and filters['min_file_size'] is not None:
                query = query.filter(SourceFileModel.file_size >= filters['min_file_size'])
            
            if 'max_file_size' in filters and filters['max_file_size'] is not None:
                query = query.filter(SourceFileModel.file_size <= filters['max_file_size'])
            
            # 排序
            order_by = filters.get('order_by', 'create_time')
            order_desc = filters.get('order_desc', True)
            
            if hasattr(SourceFileModel, order_by):
                order_field = getattr(SourceFileModel, order_by)
                if order_desc:
                    query = query.order_by(order_field.desc())
                else:
                    query = query.order_by(order_field.asc())
            else:
                query = query.order_by(SourceFileModel.create_time.desc())
            
            return query.offset(offset).limit(limit).all()
            
        except Exception as e:
            logging.error(f"搜索源文件失败: {e}")
            return []
    
    def count_files(self, filters: Dict[str, Any]) -> int:
        """
        统计符合条件的文件数量
        
        Args:
            filters: 过滤条件
            
        Returns:
            文件数量
        """
        try:
            query = self.session.query(SourceFileModel).filter(SourceFileModel.deleted == False)
            
            # 应用相同的过滤条件
            if 'knowledge_spaces_id' in filters and filters['knowledge_spaces_id']:
                query = query.filter(SourceFileModel.knowledge_spaces_id == filters['knowledge_spaces_id'])
            
            if 'file_name' in filters and filters['file_name']:
                query = query.filter(SourceFileModel.file_name.like(f"%{filters['file_name']}%"))
            
            if 'original_file_name' in filters and filters['original_file_name']:
                query = query.filter(SourceFileModel.original_file_name.like(f"%{filters['original_file_name']}%"))
            
            if 'file_format' in filters and filters['file_format']:
                query = query.filter(SourceFileModel.file_format == filters['file_format'])
            
            if 'file_suffix' in filters and filters['file_suffix']:
                query = query.filter(SourceFileModel.file_suffix == filters['file_suffix'])
            
            if 'upload_status' in filters and filters['upload_status'] is not None:
                query = query.filter(SourceFileModel.upload_status == filters['upload_status'])
            
            if 'creator' in filters and filters['creator']:
                query = query.filter(SourceFileModel.creator == filters['creator'])
            
            if 'min_file_size' in filters and filters['min_file_size'] is not None:
                query = query.filter(SourceFileModel.file_size >= filters['min_file_size'])
            
            if 'max_file_size' in filters and filters['max_file_size'] is not None:
                query = query.filter(SourceFileModel.file_size <= filters['max_file_size'])
            
            return query.count()
            
        except Exception as e:
            logging.error(f"统计文件数量失败: {e}")
            return 0
    
    def update(self, file_id: int, data: Dict[str, Any]) -> bool:
        """
        更新源文件
        
        Args:
            file_id: 文件ID
            data: 更新数据
            
        Returns:
            更新是否成功
        """
        try:
            data['update_time'] = datetime.now()
            
            result = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id == file_id,
                    SourceFileModel.deleted == False
                )
            ).update(data)
            
            self.session.commit()
            return result > 0
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"更新源文件失败: {e}")
            return False
    
    def batch_update_status(self, file_ids: List[int], upload_status: int, updater: int) -> int:
        """
        批量更新文件状态
        
        Args:
            file_ids: 文件ID列表
            upload_status: 新的上传状态
            updater: 更新人ID
            
        Returns:
            更新的文件数量
        """
        try:
            result = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id.in_(file_ids),
                    SourceFileModel.deleted == False
                )
            ).update({
                'upload_status': upload_status,
                'updater': updater,
                'update_time': datetime.now()
            }, synchronize_session=False)
            
            self.session.commit()
            return result
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"批量更新文件状态失败: {e}")
            return 0
    
    def soft_delete(self, file_id: int, deleter: int) -> bool:
        """
        软删除源文件
        
        Args:
            file_id: 文件ID
            deleter: 删除人ID
            
        Returns:
            删除是否成功
        """
        try:
            result = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id == file_id,
                    SourceFileModel.deleted == False
                )
            ).update({
                'deleted': True,
                'updater': deleter,
                'update_time': datetime.now()
            })
            
            self.session.commit()
            return result > 0
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"软删除源文件失败: {e}")
            return False
    
    def batch_soft_delete(self, file_ids: List[int], deleter: int) -> int:
        """
        批量软删除源文件
        
        Args:
            file_ids: 文件ID列表
            deleter: 删除人ID
            
        Returns:
            删除的文件数量
        """
        try:
            result = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.id.in_(file_ids),
                    SourceFileModel.deleted == False
                )
            ).update({
                'deleted': True,
                'updater': deleter,
                'update_time': datetime.now()
            }, synchronize_session=False)
            
            self.session.commit()
            return result
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"批量软删除源文件失败: {e}")
            return 0
    
    def hard_delete(self, file_id: int) -> bool:
        """
        硬删除源文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            删除是否成功
        """
        try:
            result = self.session.query(SourceFileModel).filter(
                SourceFileModel.id == file_id
            ).delete()
            
            self.session.commit()
            return result > 0
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"硬删除源文件失败: {e}")
            return False
    
    def get_statistics(self, knowledge_spaces_id: Optional[int] = None, 
                      creator_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取文件统计信息
        
        Args:
            knowledge_spaces_id: 知识空间ID（可选）
            creator_id: 创建人ID（可选）
            
        Returns:
            统计信息字典
        """
        try:
            query = self.session.query(SourceFileModel).filter(SourceFileModel.deleted == False)
            
            # 应用过滤条件
            if knowledge_spaces_id:
                query = query.filter(SourceFileModel.knowledge_spaces_id == knowledge_spaces_id)
            
            if creator_id:
                query = query.filter(SourceFileModel.creator == creator_id)
            
            # 基础统计
            total_count = query.count()
            total_size = query.with_entities(func.sum(SourceFileModel.file_size)).scalar() or 0
            
            # 按状态统计
            status_stats = query.with_entities(
                SourceFileModel.upload_status,
                func.count(SourceFileModel.id).label('count')
            ).group_by(SourceFileModel.upload_status).all()
            
            uploading_count = 0
            completed_count = 0
            failed_count = 0
            
            for status, count in status_stats:
                if status == UploadStatus.UPLOADING:
                    uploading_count = count
                elif status == UploadStatus.COMPLETED:
                    completed_count = count
                elif status == UploadStatus.FAILED:
                    failed_count = count
            
            # 按格式统计
            format_stats = {}
            format_data = query.with_entities(
                SourceFileModel.file_format,
                func.count(SourceFileModel.id).label('count'),
                func.sum(SourceFileModel.file_size).label('size')
            ).group_by(SourceFileModel.file_format).all()
            
            for file_format, count, size in format_data:
                format_stats[file_format] = {
                    'count': count,
                    'size': size or 0
                }
            
            return {
                'total_count': total_count,
                'total_size': int(total_size),
                'uploading_count': uploading_count,
                'completed_count': completed_count,
                'failed_count': failed_count,
                'format_stats': format_stats
            }
            
        except Exception as e:
            logging.error(f"获取文件统计信息失败: {e}")
            return {
                'total_count': 0,
                'total_size': 0,
                'uploading_count': 0,
                'completed_count': 0,
                'failed_count': 0,
                'format_stats': {}
            }
    
    def get_files_by_upload_status(self, upload_status: int, limit: Optional[int] = None) -> List[SourceFileModel]:
        """
        根据上传状态获取文件列表
        
        Args:
            upload_status: 上传状态
            limit: 限制数量
            
        Returns:
            文件模型列表
        """
        try:
            query = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.upload_status == upload_status,
                    SourceFileModel.deleted == False
                )
            ).order_by(SourceFileModel.create_time.asc())
            
            if limit:
                query = query.limit(limit)
            
            return query.all()
            
        except Exception as e:
            logging.error(f"根据上传状态获取文件列表失败: {e}")
            return []
    
    def get_files_by_format(self, file_format: str, knowledge_spaces_id: Optional[int] = None) -> List[SourceFileModel]:
        """
        根据文件格式获取文件列表
        
        Args:
            file_format: 文件格式
            knowledge_spaces_id: 知识空间ID（可选）
            
        Returns:
            文件模型列表
        """
        try:
            query = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.file_format == file_format,
                    SourceFileModel.deleted == False
                )
            )
            
            if knowledge_spaces_id:
                query = query.filter(SourceFileModel.knowledge_spaces_id == knowledge_spaces_id)
            
            return query.order_by(SourceFileModel.create_time.desc()).all()
            
        except Exception as e:
            logging.error(f"根据文件格式获取文件列表失败: {e}")
            return []
    
    def cleanup_failed_uploads(self, days_old: int = 7) -> int:
        """
        清理过期的失败上传文件
        
        Args:
            days_old: 天数阈值
            
        Returns:
            清理的文件数量
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            result = self.session.query(SourceFileModel).filter(
                and_(
                    SourceFileModel.upload_status == UploadStatus.FAILED,
                    SourceFileModel.create_time < cutoff_date,
                    SourceFileModel.deleted == False
                )
            ).update({
                'deleted': True,
                'update_time': datetime.now()
            })
            
            self.session.commit()
            return result
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"清理失败上传文件失败: {e}")
            return 0