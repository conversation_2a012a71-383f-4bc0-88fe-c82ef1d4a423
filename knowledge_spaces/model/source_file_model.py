from datetime import datetime
from typing import Optional, List
from enum import IntEnum
from pydantic import BaseModel, Field, validator
from sqlalchemy import Index
from multi_agent.model.ai_application_model import db


class UploadStatus(IntEnum):
    """文件上传状态枚举"""
    UPLOADING = 1  # 上传中
    COMPLETED = 2  # 已完成
    FAILED = 3     # 失败


class SourceFileModel(db.Model):
    """源文件表SQLAlchemy模型"""
    __tablename__ = 'source_files'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='文件ID')
    knowledge_spaces_id = db.Column(db.BigInteger, nullable=False, comment='所属知识空间ID')
    file_name = db.Column(db.String(255), nullable=False, comment='系统生成的唯一文件名')
    original_file_name = db.Column(db.String(255), nullable=False, comment='用户上传时的原始文件名')
    url = db.Column(db.String(512), nullable=False, comment='文件存储地址')
    file_format = db.Column(db.String(50), nullable=False, comment='文件格式 (MIME类型)')
    file_suffix = db.Column(db.String(50), nullable=False, comment='文件后缀名')
    file_size = db.Column(db.BigInteger, nullable=False, comment='文件大小 (字节)')
    upload_status = db.Column(db.SmallInteger, nullable=False, comment='上传状态(1上传中，2已完成3失败)')
    creator = db.Column(db.BigInteger, nullable=False, comment='创建人ID（上传者）')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updater = db.Column(db.BigInteger, nullable=False, comment='更新人ID')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')
    deleted = db.Column(db.Boolean, default=False, nullable=False, comment='是否删除')
    
    # 创建索引
    __table_args__ = (
        Index('idx_knowledge_spaces_id', 'knowledge_spaces_id'),
        Index('idx_upload_status', 'upload_status'),
        Index('idx_file_format', 'file_format'),
        Index('idx_creator', 'creator'),
        Index('idx_deleted', 'deleted'),
    )
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'id': self.id,
            'knowledgeSpacesId': self.knowledge_spaces_id,
            'fileName': self.file_name,
            'originalFileName': self.original_file_name,
            'url': self.url,
            'fileFormat': self.file_format,
            'fileSuffix': self.file_suffix,
            'fileSize': self.file_size,
            'uploadStatus': self.upload_status,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None,
            'deleted': self.deleted,
        }


# DO (Data Object) - 数据传输对象，用于API请求/响应
class SourceFileDO(BaseModel):
    """源文件数据对象 - 统一的数据模型"""
    
    knowledgeSpacesId: Optional[int] = Field(None, alias='knowledge_spaces_id', description='所属知识空间ID')
    fileName: Optional[str] = Field(None, max_length=255, description='系统生成的唯一文件名')
    originalFileName: Optional[str] = Field(None, max_length=255, description='用户上传时的原始文件名')
    url: Optional[str] = Field(None, max_length=512, description='文件存储地址')
    fileFormat: Optional[str] = Field(None, max_length=50, description='文件格式 (MIME类型)')
    fileSuffix: Optional[str] = Field(None, max_length=50, description='文件后缀名')
    fileSize: Optional[int] = Field(None, ge=0, description='文件大小 (字节)')
    uploadStatus: Optional[int] = Field(None, ge=1, le=3, description='上传状态(1上传中，2已完成，3失败)')
    creator: Optional[int] = Field(None, description='创建人ID（上传者）')
    updater: Optional[int] = Field(None, description='更新人ID')
    
    @validator('fileFormat')
    def validate_file_format(cls, v):
        if v and not ('/' in v and len(v.split('/')) == 2):
            raise ValueError('文件格式必须是有效的MIME类型')
        return v
    
    @validator('fileSuffix')
    def validate_file_suffix(cls, v):
        if v and not v.startswith('.'):
            v = '.' + v
        return v
    
    class Config:
        from_attributes = True
        populate_by_name = True


# VO (View Object) - 视图对象，用于响应展示
class SourceFileVO(SourceFileDO):
    """源文件视图对象 - 包含展示相关字段"""
    
    id: Optional[int] = Field(None, description='文件ID')
    createTime: Optional[datetime] = Field(None, alias='create_time', description='创建时间')
    updateTime: Optional[datetime] = Field(None, alias='update_time', description='更新时间')
    deleted: Optional[bool] = Field(False, description='是否删除')
    
    # 扩展字段
    uploadStatusText: Optional[str] = Field(None, description='上传状态文本')
    fileSizeText: Optional[str] = Field(None, description='文件大小文本')
    
    def __init__(self, **data):
        super().__init__(**data)
        # 设置状态文本
        if self.uploadStatus:
            status_map = {1: '上传中', 2: '已完成', 3: '失败'}
            self.uploadStatusText = status_map.get(self.uploadStatus, '未知')
        
        # 设置文件大小文本
        if self.fileSize is not None:
            self.fileSizeText = self._format_file_size(self.fileSize)
    
    @staticmethod
    def _format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes}B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f}KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f}MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f}GB"


# 创建请求对象
class SourceFileCreateRequest(BaseModel):
    """创建源文件请求对象"""
    
    knowledgeSpacesId: int = Field(..., description='所属知识空间ID')
    fileName: str = Field(..., min_length=1, max_length=255, description='系统生成的唯一文件名')
    originalFileName: str = Field(..., min_length=1, max_length=255, description='用户上传时的原始文件名')
    url: str = Field(..., min_length=1, max_length=512, description='文件存储地址')
    fileFormat: str = Field(..., min_length=1, max_length=50, description='文件格式 (MIME类型)')
    fileSuffix: str = Field(..., min_length=1, max_length=50, description='文件后缀名')
    fileSize: int = Field(..., ge=0, description='文件大小 (字节)')
    uploadStatus: int = Field(2, ge=1, le=3, description='上传状态(1上传中，2已完成，3失败)')
    creator: int = Field(..., description='创建人ID（上传者）')
    
    @validator('fileFormat')
    def validate_file_format(cls, v):
        if not ('/' in v and len(v.split('/')) == 2):
            raise ValueError('文件格式必须是有效的MIME类型')
        return v
    
    @validator('fileSuffix')
    def validate_file_suffix(cls, v):
        if not v.startswith('.'):
            v = '.' + v
        return v


# 更新请求对象
class SourceFileUpdateRequest(BaseModel):
    """更新源文件请求对象"""
    
    originalFileName: Optional[str] = Field(None, min_length=1, max_length=255, description='用户上传时的原始文件名')
    url: Optional[str] = Field(None, min_length=1, max_length=512, description='文件存储地址')
    fileFormat: Optional[str] = Field(None, min_length=1, max_length=50, description='文件格式 (MIME类型)')
    fileSuffix: Optional[str] = Field(None, min_length=1, max_length=50, description='文件后缀名')
    fileSize: Optional[int] = Field(None, ge=0, description='文件大小 (字节)')
    uploadStatus: Optional[int] = Field(None, ge=1, le=3, description='上传状态(1上传中，2已完成，3失败)')
    updater: int = Field(..., description='更新人ID')
    
    @validator('fileFormat')
    def validate_file_format(cls, v):
        if v and not ('/' in v and len(v.split('/')) == 2):
            raise ValueError('文件格式必须是有效的MIME类型')
        return v
    
    @validator('fileSuffix')
    def validate_file_suffix(cls, v):
        if v and not v.startswith('.'):
            v = '.' + v
        return v


# 查询请求对象
class SourceFileQueryRequest(BaseModel):
    """查询源文件请求对象"""
    
    knowledgeSpacesId: Optional[int] = Field(None, description='所属知识空间ID')
    fileName: Optional[str] = Field(None, description='文件名搜索')
    originalFileName: Optional[str] = Field(None, description='原始文件名搜索')
    fileFormat: Optional[str] = Field(None, description='文件格式过滤')
    fileSuffix: Optional[str] = Field(None, description='文件后缀过滤')
    uploadStatus: Optional[int] = Field(None, ge=1, le=3, description='上传状态过滤')
    creator: Optional[int] = Field(None, description='创建人ID过滤')
    minFileSize: Optional[int] = Field(None, ge=0, description='最小文件大小')
    maxFileSize: Optional[int] = Field(None, ge=0, description='最大文件大小')
    
    # 分页参数
    page: Optional[int] = Field(1, ge=1, description='页码')
    pageSize: Optional[int] = Field(20, ge=1, le=100, description='每页大小')
    
    # 排序参数
    orderBy: Optional[str] = Field('create_time', description='排序字段')
    orderDesc: Optional[bool] = Field(True, description='是否倒序')
    
    @validator('maxFileSize')
    def validate_file_sizes(cls, v, values):
        if v is not None and 'minFileSize' in values and values['minFileSize'] is not None:
            if v < values['minFileSize']:
                raise ValueError('最大文件大小不能小于最小文件大小')
        return v


# 批量删除请求对象
class SourceFileBatchDeleteRequest(BaseModel):
    """批量删除源文件请求对象"""
    
    fileIds: List[int] = Field(..., min_items=1, description='文件ID列表')
    deleter: int = Field(..., description='删除人ID')


# 批量更新状态请求对象
class SourceFileBatchUpdateStatusRequest(BaseModel):
    """批量更新文件状态请求对象"""
    
    fileIds: List[int] = Field(..., min_items=1, description='文件ID列表')
    uploadStatus: int = Field(..., ge=1, le=3, description='新的上传状态')
    updater: int = Field(..., description='更新人ID')


# 文件统计响应对象
class SourceFileStatisticsResponse(BaseModel):
    """文件统计响应对象"""
    
    totalCount: int = Field(0, description='总文件数')
    totalSize: int = Field(0, description='总文件大小(字节)')
    totalSizeText: str = Field('0B', description='总文件大小文本')
    uploadingCount: int = Field(0, description='上传中文件数')
    completedCount: int = Field(0, description='已完成文件数')
    failedCount: int = Field(0, description='失败文件数')
    formatStats: dict = Field(default_factory=dict, description='文件格式统计')
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.totalSize:
            self.totalSizeText = SourceFileVO._format_file_size(self.totalSize)