from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from sqlalchemy import Index
from multi_agent.model.ai_application_model import db


class ProcessingTaskModel(db.Model):
    """处理任务表SQLAlchemy模型"""
    __tablename__ = 'processing_tasks'
    
    id = db.Column(db.<PERSON>Integer, primary_key=True, autoincrement=True, comment='任务ID')
    source_file_id = db.Column(db.BigInteger, nullable=False, comment='关联的源文件ID')
    user_id = db.Column(db.BigInteger, nullable=False, comment='任务所属用户ID (冗余, 便于查询)')
    task_type = db.Column(db.<PERSON>Integer, nullable=False, comment='任务类型: 1-PARSE_DOCUMENT(文档解析), 2-VECTORIZE_CHUNKS(向量化分块), 3-GENERATE_SUMMARY(生成摘要)')
    status = db.Column(db.<PERSON><PERSON><PERSON><PERSON>, nullable=False, default=1, comment='任务状态: 1-QUEUED(排队中), 2-IN_PROGRESS(处理中), 3-COMPLETED(已完成), 4-FAILED(失败)')
    payload = db.Column(db.JSON, nullable=True, comment='任务载荷, 存储额外参数')
    error_message = db.Column(db.String(1000), nullable=True, comment='任务失败时的错误信息')
    started_at = db.Column(db.DateTime, nullable=True, comment='任务开始执行时间')
    completed_at = db.Column(db.DateTime, nullable=True, comment='任务完成或失败的时间')
    creator = db.Column(db.BigInteger, nullable=False, comment='创建人ID')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updater = db.Column(db.BigInteger, nullable=False, comment='更新人ID')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')
    
    # 创建索引
    __table_args__ = (
        Index('idx_source_file_id', 'source_file_id'),
        Index('idx_user_id', 'user_id'),
        Index('idx_task_type', 'task_type'),
        Index('idx_status', 'status'),
        Index('idx_create_time', 'create_time'),
        Index('idx_user_status', 'user_id', 'status'),
        Index('idx_file_status', 'source_file_id', 'status'),
    )
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'id': self.id,
            'sourceFileId': self.source_file_id,
            'userId': self.user_id,
            'taskType': self.task_type,
            'status': self.status,
            'payload': self.payload,
            'errorMessage': self.error_message,
            'startedAt': self.started_at.isoformat() if self.started_at else None,
            'completedAt': self.completed_at.isoformat() if self.completed_at else None,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None,
        }


# DO (Data Object) - 数据传输对象，用于API请求/响应
class ProcessingTaskDO(BaseModel):
    """处理任务数据对象 - 统一的数据模型"""
    
    sourceFileId: Optional[int] = Field(None, alias='source_file_id', description='关联的源文件ID')
    userId: Optional[int] = Field(None, alias='user_id', description='任务所属用户ID')
    taskType: Optional[int] = Field(None, alias='task_type', description='任务类型')
    status: Optional[int] = Field(1, description='任务状态')
    payload: Optional[dict] = Field(None, description='任务载荷')
    errorMessage: Optional[str] = Field(None, alias='error_message', max_length=1000, description='错误信息')
    startedAt: Optional[datetime] = Field(None, alias='started_at', description='开始执行时间')
    completedAt: Optional[datetime] = Field(None, alias='completed_at', description='完成时间')
    creator: Optional[int] = Field(None, description='创建人ID')
    updater: Optional[int] = Field(None, description='更新人ID')
    
    class Config:
        from_attributes = True
        populate_by_name = True


# VO (View Object) - 视图对象，用于响应展示
class ProcessingTaskVO(ProcessingTaskDO):
    """处理任务视图对象 - 包含展示相关字段"""
    
    id: Optional[int] = Field(None, description='任务ID')
    createTime: Optional[datetime] = Field(None, alias='create_time', description='创建时间')
    updateTime: Optional[datetime] = Field(None, alias='update_time', description='更新时间')
    
    # 扩展字段
    taskTypeName: Optional[str] = Field(None, description='任务类型名称')
    statusName: Optional[str] = Field(None, description='状态名称')
    duration: Optional[int] = Field(None, description='执行时长(秒)')


# 创建请求对象
class ProcessingTaskCreateRequest(BaseModel):
    """创建处理任务请求对象"""
    
    sourceFileId: int = Field(..., description='关联的源文件ID')
    userId: int = Field(..., description='任务所属用户ID')
    taskType: int = Field(..., ge=1, le=3, description='任务类型: 1-PARSE_DOCUMENT, 2-VECTORIZE_CHUNKS, 3-GENERATE_SUMMARY')
    payload: Optional[dict] = Field(None, description='任务载荷')
    creator: int = Field(..., description='创建人ID')


# 更新请求对象
class ProcessingTaskUpdateRequest(BaseModel):
    """更新处理任务请求对象"""
    
    status: Optional[int] = Field(None, ge=1, le=4, description='任务状态')
    payload: Optional[dict] = Field(None, description='任务载荷')
    errorMessage: Optional[str] = Field(None, max_length=1000, description='错误信息')
    startedAt: Optional[datetime] = Field(None, description='开始执行时间')
    completedAt: Optional[datetime] = Field(None, description='完成时间')
    updater: int = Field(..., description='更新人ID')


# 查询请求对象
class ProcessingTaskQueryRequest(BaseModel):
    """查询处理任务请求对象"""
    
    taskId: Optional[int] = Field(None, description='任务ID')
    sourceFileId: Optional[int] = Field(None, description='源文件ID')
    userId: Optional[int] = Field(None, description='用户ID')
    taskType: Optional[int] = Field(None, ge=1, le=3, description='任务类型')
    status: Optional[int] = Field(None, ge=1, le=4, description='任务状态')
    startTimeFrom: Optional[datetime] = Field(None, description='开始时间范围-起始')
    startTimeTo: Optional[datetime] = Field(None, description='开始时间范围-结束')
    completedTimeFrom: Optional[datetime] = Field(None, description='完成时间范围-起始')
    completedTimeTo: Optional[datetime] = Field(None, description='完成时间范围-结束')
    
    # 分页参数
    page: Optional[int] = Field(1, ge=1, description='页码')
    pageSize: Optional[int] = Field(20, ge=1, le=100, description='每页大小')
    
    # 排序参数
    orderBy: Optional[str] = Field('create_time', description='排序字段')
    orderDesc: Optional[bool] = Field(True, description='是否倒序')


# 批量操作请求对象
class ProcessingTaskBatchRequest(BaseModel):
    """批量处理任务请求对象"""
    
    taskIds: List[int] = Field(..., min_items=1, description='任务ID列表')
    action: str = Field(..., description='操作类型: cancel, retry, delete')
    updater: int = Field(..., description='操作人ID')


# 任务类型枚举
class TaskType:
    PARSE_DOCUMENT = 1      # 文档解析
    VECTORIZE_CHUNKS = 2    # 向量化分块
    GENERATE_SUMMARY = 3    # 生成摘要
    
    @classmethod
    def get_name(cls, task_type: int) -> str:
        """获取任务类型名称"""
        names = {
            cls.PARSE_DOCUMENT: "文档解析",
            cls.VECTORIZE_CHUNKS: "向量化分块", 
            cls.GENERATE_SUMMARY: "生成摘要"
        }
        return names.get(task_type, "未知类型")


# 任务状态枚举
class TaskStatus:
    QUEUED = 1          # 排队中
    IN_PROGRESS = 2     # 处理中
    COMPLETED = 3       # 已完成
    FAILED = 4          # 失败
    
    @classmethod
    def get_name(cls, status: int) -> str:
        """获取状态名称"""
        names = {
            cls.QUEUED: "排队中",
            cls.IN_PROGRESS: "处理中",
            cls.COMPLETED: "已完成",
            cls.FAILED: "失败"
        }
        return names.get(status, "未知状态")