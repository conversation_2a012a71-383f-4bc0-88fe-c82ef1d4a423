import sys
import logging

from config import config
from app import create_app

if __name__ == '__main__':
    # 首先加载配置
    env = sys.argv[1] if len(sys.argv) > 1 else "dev"
    success = config.load_config(env)

    if not success:
        logging.error(f"配置加载失败，环境: {env}")
        sys.exit(1)

    # 验证数据源配置
    config_data = config.conf()
    datasources = config_data.get('datasources')

    if not datasources:
        logging.error("未找到datasources配置")
        sys.exit(1)

    # 验证主数据库配置
    main_config = datasources.get('main')
    if not main_config:
        logging.error("未找到main数据源配置")
        sys.exit(1)

    required_keys = ['host', 'user', 'password', 'database']
    missing_keys = [key for key in required_keys if not main_config.get(key)]

    if missing_keys:
        logging.error(f"主数据库配置缺失: {missing_keys}")
        sys.exit(1)

    # 验证dify数据库配置
    dify_config = datasources.get('dify')
    if dify_config:
        dify_missing_keys = [key for key in required_keys if not dify_config.get(key)]
        if dify_missing_keys:
            logging.error(f"Dify数据库配置缺失: {dify_missing_keys}")
            sys.exit(1)
        logging.info("Dify数据库配置验证通过")
    else:
        logging.warning("未找到Dify数据库配置，Dify功能可能不可用")

    # 配置加载成功后创建应用
    app = create_app()
    app.run(host='0.0.0.0', port=9000)
