# AI 智能问答系统

## 项目简介
这是一个基于 Python 和 Flask 框架开发的智能问答系统，集成了多个大语言模型（LLM）服务，支持知识库检索和上下文对话功能。

## 技术架构

### 核心框架
- **Web框架**: Flask 3.1.0
- **跨域支持**: flask-cors 5.0.1
- **HTTP客户端**: requests 2.32.3, httpx 0.28.1
- **数据验证**: pydantic 2.11.3
- **数据库**: MySQL (mysql-connector-python 9.3.0)

### 主要功能模块
1. **主应用服务** (`app/`)
   - 提供 RESTful API 接口
   - 处理用户请求和响应
   - 集成多个 LLM 服务

2. **企查查服务** (`retrieval/`)
   - 企业检索
   - 企业主要人员

3. **智能建议服务** (`suggest/`)
   - 问题建议生成
   - 上下文分析
   - 智能推荐

4. **配置管理** (`config/`)
   - 多环境配置支持（开发/生产）
   - 用户数据管理
   - 插件系统配置

5. **工具类** (`utils/`)
   - 通用工具函数
   - 辅助功能模块

### 项目结构
```
.
├── app/                   # 主应用模块
├── config/               # 配置管理模块
├── retrieval/           # 知识库检索模块
├── suggest/             # 智能建议模块
├── utils/               # 工具类模块
├── run.py               # 应用启动入口
├── requirements.txt     # 项目依赖
├── Dockerfile          # Docker 构建文件
├── start.sh            # 启动脚本
├── stop.sh             # 停止脚本
└── knowledge_base.json # 知识库配置文件
```

## 环境要求
- Python 3.12
- MySQL 数据库
- Docker (可选)

## 快速开始

### 本地开发环境
1. 创建虚拟环境
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate  # Windows
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 启动服务
```bash
# 开发环境
./start.sh dev 5000

# 生产环境
./start.sh prod 5000
```

### Docker 部署
```bash
# 构建镜像
docker buildx build --platform linux/amd64 -t ai-answer:latest --load .

# 运行容器
docker run -d -p 9000:9000 --name ai-answer ai-answer:latest
```

## API 接口

### 智能建议生成
- **接口**: `/suggest`
- **方法**: POST
- **功能**: 基于上下文提供智能问答建议
- **请求体**:
```json
{
    "user": "用户名",
    "Authorization": "认证信息",
    "conversation_id": "会话ID"
}
```

## 配置说明

### 环境配置
项目支持多环境配置：
- 开发环境 (`config.dev.json`)
- 生产环境 (`config.prod.json`)

### 主要配置项
- `env`: 运行环境
- `debug`: 调试模式
- `appdata_dir`: 数据存储目录
- `openai_api_key`: OpenAI API 密钥
- `model`: 使用的模型
- `temperature`: 模型温度参数
- `max_tokens`: 最大 token 数

## 开发指南

### 添加新的 LLM 服务
1. 在 `config.py` 中添加相关配置项
2. 创建新的服务集成文件
3. 在 `app.py` 中集成新服务

### 扩展知识库
1. 在 `knowledge_base.py` 中添加新的检索方法
2. 更新知识库数据结构
3. 配置新的知识库源

## 注意事项
1. 请确保正确配置 API 密钥
2. 生产环境部署时关闭调试模式
3. 定期备份用户数据
4. 注意 API 调用限制和成本控制

## 许可证
[待补充]

## 贡献指南
[待补充] 