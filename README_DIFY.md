# Dify数据库查询功能

本项目新增了对Dify PostgreSQL数据库的查询功能，提供了完整的用户对话和消息记录查询API。

## 功能特性

✅ **PostgreSQL数据库支持** - 连接Dify的PostgreSQL数据库  
✅ **完整的数据模型** - 支持end_users、conversations、messages三个核心表  
✅ **RESTful API** - 提供标准的REST API接口  
✅ **复杂查询** - 支持多条件搜索和统计分析  
✅ **分页支持** - 所有列表查询都支持分页  
✅ **错误处理** - 完善的错误处理和响应格式  

## 快速开始

### 1. 安装依赖

```bash
# 安装PostgreSQL驱动
pip install psycopg2-binary

# 或者安装所有依赖
pip install -r requirements.txt
```

### 2. 配置数据库

创建或编辑 `config/config.dify.json`：

```json
{
  "env": "dify",
  "debug": true,
  "host": "**************",
  "port": 5444,
  "user": "postgres",
  "password": "difyai123456",
  "database": "dify",
  "database_type": "postgresql"
}
```

### 3. 启动服务

```bash
# 方式1: 使用专用启动脚本（推荐）
python start_dify.py

# 方式2: 使用标准启动方式
python run.py dify
```

### 4. 测试功能

```bash
# 运行测试脚本
python tests/test_dify_api.py

# 或者手动测试
curl http://localhost:9000/api/dify/health
```

## API接口

### 基础URL
```
http://localhost:9000/api/dify
```

### 主要端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/users/{user_id}/conversations` | 根据用户查询对话和消息 |
| GET | `/sessions/{session_id}/conversations` | 根据session_id查询对话和消息 |
| GET | `/conversations/{conversation_id}/messages` | 根据对话查询消息记录 |
| POST | `/conversations/search` | 搜索对话会话 |
| GET | `/users/{user_id}/statistics` | 获取用户统计信息 |
| POST | `/conversations` | 创建对话会话 |
| POST | `/messages` | 创建消息记录 |

### 示例请求

#### 1. 查询用户对话和消息
```bash
curl "http://localhost:9000/api/dify/users/550e8400-e29b-41d4-a716-446655440000/conversations?limit=10&offset=0"
```

#### 2. 根据session_id查询对话和消息
```bash
curl "http://localhost:9000/api/dify/sessions/test-session-001/conversations?limit=10&offset=0"
```

#### 3. 查询对话消息记录
```bash
curl "http://localhost:9000/api/dify/conversations/550e8400-e29b-41d4-a716-446655440001/messages?limit=20"
```

#### 4. 搜索对话
```bash
curl -X POST "http://localhost:9000/api/dify/conversations/search" \
  -H "Content-Type: application/json" \
  -d '{
    "from_source": "api",
    "limit": 10,
    "offset": 0
  }'
```

## 项目结构

```
├── config/
│   └── config.dify.json          # Dify数据库配置
├── models/
│   └── dify_models.py            # Dify数据模型
├── dao/
│   ├── end_users_dao.py          # 用户数据访问层
│   ├── conversations_dao.py      # 对话数据访问层
│   └── messages_dao.py           # 消息数据访问层
├── services/
│   └── dify_service.py           # 业务逻辑层
├── controllers/
│   └── dify_controller.py        # API控制器
├── utils/
│   └── response_utils.py         # 响应工具类
├── tests/
│   └── test_dify_api.py          # 测试脚本
├── docs/
│   └── dify_api_documentation.md # API文档
├── start_dify.py                 # 专用启动脚本
└── README_DIFY.md               # 本文档
```

## 数据库表结构

### end_users (终端用户表)
- `id` - 用户ID (UUID)
- `tenant_id` - 租户ID
- `app_id` - 应用ID
- `type` - 用户类型
- `external_user_id` - 外部用户ID
- `name` - 用户名称
- `is_anonymous` - 是否匿名用户
- `session_id` - 会话ID
- `created_at` - 创建时间
- `updated_at` - 更新时间

### conversations (对话会话表)
- `id` - 对话ID (UUID)
- `app_id` - 应用ID
- `mode` - 对话模式
- `name` - 对话名称
- `status` - 状态
- `from_source` - 来源
- `from_end_user_id` - 来源用户ID
- `dialogue_count` - 对话轮数
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `is_deleted` - 是否删除

### messages (消息表)
- `id` - 消息ID (UUID)
- `app_id` - 应用ID
- `conversation_id` - 对话ID
- `query` - 查询内容
- `answer` - 回答内容
- `message_tokens` - 消息token数
- `answer_tokens` - 回答token数
- `total_price` - 总价格
- `currency` - 货币
- `from_source` - 来源
- `from_end_user_id` - 来源用户ID
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 响应格式

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": {...},
    "code": 200
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误信息",
    "data": null,
    "code": 400
}
```

## 开发指南

### 添加新的查询功能

1. **在DAO层添加查询方法** (`dao/`)
2. **在Service层添加业务逻辑** (`services/dify_service.py`)
3. **在Controller层添加API端点** (`controllers/dify_controller.py`)
4. **更新API文档** (`docs/dify_api_documentation.md`)

### 测试

```bash
# 运行完整测试
python tests/test_dify_api.py

# 测试数据库连接
python -c "from tests.test_dify_api import test_database_connection; test_database_connection()"

# 测试API端点
python -c "from tests.test_dify_api import test_api_endpoints; test_api_endpoints()"
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证配置文件中的连接信息
   - 确认网络连接和防火墙设置

2. **依赖安装失败**
   ```bash
   # 在Ubuntu/Debian上
   sudo apt-get install libpq-dev python3-dev
   pip install psycopg2-binary
   
   # 在CentOS/RHEL上
   sudo yum install postgresql-devel python3-devel
   pip install psycopg2-binary
   ```

3. **API响应404**
   - 确认服务已正确启动
   - 检查URL路径是否正确
   - 验证蓝图是否已注册

### 日志查看

启动服务时会显示详细的日志信息，包括：
- 数据库连接状态
- 表结构创建情况
- API端点注册情况
- 请求处理日志

## 性能优化

1. **数据库索引** - 已为常用查询字段创建索引
2. **分页查询** - 所有列表查询都支持分页
3. **连接池** - 使用SQLAlchemy连接池管理数据库连接
4. **查询优化** - 避免N+1查询问题

## 安全考虑

1. **SQL注入防护** - 使用SQLAlchemy ORM防止SQL注入
2. **参数验证** - 对所有输入参数进行验证
3. **错误处理** - 不暴露敏感的系统信息
4. **访问控制** - 可根据需要添加认证和授权

## 贡献

欢迎提交Issue和Pull Request来改进这个功能！

## 许可证

本项目遵循原项目的许可证。
