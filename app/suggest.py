import json
from flask import request, Blueprint
from flask_cors import cross_origin
import sys

from utils.response_utils import success_response

import logging

from suggest import suggest, opening_remarks_questions

suggest_controller = Blueprint('suggest', __name__)


# 下一步建议
@suggest_controller.route('/suggest', methods=['POST'])
@cross_origin()
def suggest_next_question():
    logging.info("当前环境为：%s", sys.argv[1])
    # 解析入参
    jsonObj = request.json
    result = suggest.suggest_question(jsonObj)

    # 5. 返回结果
    return success_response(result)


# 智能体的默认开场白问题
@suggest_controller.route('/suggested_questions', methods=['GET'])
@cross_origin()
def suggested_questions_dict():
    params_map = {
        'bus_type': request.args.get('type',default=None),
        'business_type': request.args.get('businessType',default=None),
        'company_id': request.args.get('c_id',default=None)
    }

    # 过滤空值并拼接参数
    valid_params = [v for v in params_map.values() if v not in (None, '')]
    combined_param = '#'.join(valid_params)

    result = opening_remarks_questions.get_opening_remarks_questions_dict(sys.argv[1], combined_param)
    # 返回结果
    return success_response(result)
