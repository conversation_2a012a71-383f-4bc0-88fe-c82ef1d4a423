import logging
from flask import Flask
from flask_cors import CORS
from config.config import conf
import urllib.parse


# 注册蓝图


def create_app():
    app = Flask(__name__)

    # 配置数据源
    # 从config中获取数据源配置
    config_data = conf()

    # 验证配置结构
    if not config_data:
        raise RuntimeError("配置未加载，请确保在创建应用前调用 config.load_config()")

    datasources = config_data.get('datasources')
    if not datasources:
        raise RuntimeError("未找到datasources配置")

    # 配置主数据库连接
    main_config = datasources.get('main')
    if not main_config:
        raise RuntimeError("未找到main数据源配置")

    # 验证主数据库配置
    required_keys = ['host', 'user', 'password', 'database']
    missing_keys = [key for key in required_keys if not main_config.get(key)]

    if missing_keys:
        raise RuntimeError(f"主数据库配置缺失必要字段: {missing_keys}")

    # 构建主数据库连接字符串
    database_type = main_config.get('database_type', 'mysql')
    print(main_config)
    if database_type == 'mysql':
        # MySQL连接字符串
        port = main_config.get('port', 3306)
        password_encoded = urllib.parse.quote(main_config.get('password'))
        if port != 3306:
            database_uri = (
                f"mysql+pymysql://{main_config.get('user')}:{password_encoded}"
                f"@{main_config.get('host')}:{port}/{main_config.get('database')}"
            )
        else:
            database_uri = (
                f"mysql+pymysql://{main_config.get('user')}:{password_encoded}"
                f"@{main_config.get('host')}/{main_config.get('database')}"
            )
        logging.info(
            f"主数据库连接配置: mysql+pymysql://{main_config.get('user')}:***@{main_config.get('host')}/{main_config.get('database')}")
        print(f"主数据库连接配置: mysql+pymysql://{main_config.get('user')}:***@{main_config.get('host')}/{main_config.get('database')}")
    else:
        raise RuntimeError(f"主数据库不支持的数据库类型: {database_type}")

    app.config['SQLALCHEMY_DATABASE_URI'] = database_uri
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_recycle': 3600,
        'pool_pre_ping': True
    }

    # 初始化主数据库（MySQL）
    from multi_agent.model.ai_application_model import db
    # 初始化dify数据库（PostgreSQL）
    init_dify_database(app)

    db.init_app(app)


    # 跨域配置
    CORS(app)
    # CORS(app,
    #      origins="*",  # 允许所有来源，生产环境建议指定具体域名
    #      methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    #      allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
    #      supports_credentials=False)

    from .suggest import suggest_controller
    from .retrieval import retrieval_controller
    from app.sensitive_words import sensitive_words_bp
    from multi_agent import ai_application_bp, ai_tags_bp, ai_application_favorites_bp
    from dify.controller.dify_controller import dify_bp
    from stock import stock_bp
    from knowledge_spaces import knowledge_spaces_bp, source_files_bp, processing_tasks_bp

    app.register_blueprint(suggest_controller, url_prefix='/ai-answer')
    app.register_blueprint(retrieval_controller, url_prefix='/ai-answer')
    app.register_blueprint(sensitive_words_bp, url_prefix='/ai-answer/sensitive-words')
    app.register_blueprint(ai_application_bp, url_prefix='/ai-answer/multiagent/application')
    app.register_blueprint(ai_tags_bp, url_prefix='/ai-answer/multiagent/tags')
    app.register_blueprint(ai_application_favorites_bp, url_prefix='/ai-answer/multiagent/favorites')
    app.register_blueprint(dify_bp, url_prefix='/ai-answer/dify')
    app.register_blueprint(stock_bp, url_prefix='/ai-answer/stock')
    app.register_blueprint(knowledge_spaces_bp, url_prefix='/ai-answer/knowledge-spaces')
    app.register_blueprint(processing_tasks_bp, url_prefix='/ai-answer/processing-tasks')
    app.register_blueprint(source_files_bp, url_prefix='/ai-answer/source-files')

    return app


def init_dify_database(app):
    """
    初始化Dify数据库连接
    使用主应用的SQLAlchemy实例，通过BINDS配置独立连接

    Args:
        app: Flask应用实例

    Returns:
        bool: 初始化是否成功
    """
    try:
        # 获取配置
        config = conf()

        # 获取数据源配置
        datasources = config.get('datasources')
        if not datasources:
            logging.warning("未找到datasources配置，跳过Dify数据库初始化")
            return False

        # 获取dify数据库配置
        dify_config = datasources.get('dify')
        if not dify_config:
            logging.warning("未找到dify数据源配置，跳过Dify数据库初始化")
            return False

        # 验证必要字段
        required_keys = ['host', 'user', 'password', 'database']
        missing_keys = [key for key in required_keys if not dify_config.get(key)]

        if missing_keys:
            logging.warning(f"Dify数据库配置缺失必要字段: {missing_keys}，跳过初始化")
            return False

        # 构建PostgreSQL连接字符串
        port = dify_config.get('port', 5432)
        database_uri = (
            f"postgresql+psycopg2://{dify_config.get('user')}:{dify_config.get('password')}"
            f"@{dify_config.get('host')}:{port}/{dify_config.get('database')}"
        )

        # 配置dify数据库连接到BINDS
        app.config['SQLALCHEMY_BINDS'] = app.config.get('SQLALCHEMY_BINDS', {})
        app.config['SQLALCHEMY_BINDS']['dify'] = database_uri

        logging.info(
            f"Dify数据库连接配置: postgresql+psycopg2://{dify_config.get('user')}:***@{dify_config.get('host')}:{port}/{dify_config.get('database')}")

        return True

    except Exception as e:
        logging.warning(f"Dify数据库初始化失败: {e}，将跳过Dify功能")
        return False
