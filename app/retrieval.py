from dotenv import load_dotenv
from flask import request, Blueprint
from flask_cors import cross_origin

from utils.response_utils import success_response
from retrieval import retrieval

load_dotenv()

retrieval_controller = Blueprint('retrieval', __name__)


@retrieval_controller.route('/retrieval', methods=['GET'])
@cross_origin()
def retrieval_name():
    # logging.info("当前环境为：%s", sys.argv[1])
    # jsonObj = request.json
    name = request.args['companyName']

    if name and len(name) > 0:
        result = retrieval.qichacha_fuzzy_search(name)
        return success_response(result)
    else:
        return success_response({"result": []})


@retrieval_controller.route('/pdf', methods=['POST'])
@cross_origin()
def toPdfc():
    jsobj = request.json
    arg1 = jsobj.get("content")
    url = retrieval.toPdf(arg1)
    return url
