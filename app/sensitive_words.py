
from flask import request, Blueprint, jsonify
from sensitive_words import ali_sensitive_words
from sensitive_words.sensitive_words_service import check_sensitive_words_comprehensive, filter_sensitive_words

sensitive_words_bp = Blueprint("sensitive_words", __name__)

#  敏感词检查接口
@sensitive_words_bp.route("/check_input", methods=["POST"])
def check_input_sensitive_words():
    """检查输入内容的敏感词"""
    try:
        data = request.get_json()
        if not data or 'content' not in data:
            return jsonify({"error": "缺少content参数"}), 400

        content = data.get("content")
        use_local = data.get("use_local", True)
        use_ali = data.get("use_ali", True)

        result = check_sensitive_words_comprehensive(
            content,
            "llm_query_moderation",
            use_local=use_local,
            use_ali=use_ali
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"检查失败: {str(e)}"}), 500


#  输出内容敏感词检查接口
@sensitive_words_bp.route("/check_output", methods=["POST"])
def check_output_sensitive_words():
    """检查输出内容的敏感词"""
    try:
        data = request.get_json()
        if not data or 'content' not in data:
            return jsonify({"error": "缺少content参数"}), 400

        content = data.get("content")
        use_local = data.get("use_local", True)
        use_ali = data.get("use_ali", True)

        result = check_sensitive_words_comprehensive(
            content,
            "llm_response_moderation",
            use_local=use_local,
            use_ali=use_ali
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"检查失败: {str(e)}"}), 500


#  通用敏感词检查接口
@sensitive_words_bp.route("/check", methods=["GET", "POST"])
def check_sensitive_words():
    """通用敏感词检查接口"""
    if request.method == "GET":
        return jsonify({
            "message": "敏感词检测服务正常运行",
            "status": "ok",
            "version": "2.0",
            "features": ["本地DFA过滤", "阿里云API过滤", "综合检测"]
        })
    else:
        try:
            data = request.get_json()
            if not data or 'content' not in data:
                return jsonify({"error": "缺少content参数"}), 400

            content = data.get("content")
            service_type = data.get("serviceType", "llm_query_moderation")
            use_local = data.get("use_local", True)
            use_ali = data.get("use_ali", True)

            result = check_sensitive_words_comprehensive(
                content,
                service_type,
                use_local=use_local,
                use_ali=use_ali
            )
            return jsonify(result)
        except Exception as e:
            return jsonify({"error": f"检查失败: {str(e)}"}), 500


#  文本过滤接口
@sensitive_words_bp.route("/filter", methods=["POST"])
def filter_text():
    """过滤文本中的敏感词"""
    try:
        data = request.get_json()
        if not data or 'content' not in data:
            return jsonify({"error": "缺少content参数"}), 400

        content = data.get("content")
        replacement = data.get("replacement", "*")

        filtered_content = filter_sensitive_words(content, replacement)

        return jsonify({
            "original_content": content,
            "filtered_content": filtered_content,
            "replacement": replacement,
            "status": "success"
        })
    except Exception as e:
        return jsonify({"error": f"过滤失败: {str(e)}"}), 500


#  兼容旧版本接口  只调用阿里云的内容安全检测服务
@sensitive_words_bp.route("/legacy/check_input", methods=["POST"])
def legacy_check_input():
    """兼容旧版本的输入检查接口"""
    try:
        data = request.get_json()
        content = data.get("content") if data else None
        if not content:
            return jsonify({"error": "缺少content参数"}), 400

        words = ali_sensitive_words.ali_check_sensitive_words(content, "llm_query_moderation")
        return jsonify(words)
    except Exception as e:
        return jsonify({"error": f"检查失败: {str(e)}"}), 500


#  兼容旧版本接口  只调用阿里云的内容安全检测服务
@sensitive_words_bp.route("/legacy/check_output", methods=["POST"])
def legacy_check_output():
    """兼容旧版本的输出检查接口"""
    try:
        data = request.get_json()
        content = data.get("content") if data else None
        if not content:
            return jsonify({"error": "缺少content参数"}), 400

        words = ali_sensitive_words.ali_check_sensitive_words(content, "llm_response_moderation")
        return jsonify(words)
    except Exception as e:
        return jsonify({"error": f"检查失败: {str(e)}"}), 500
