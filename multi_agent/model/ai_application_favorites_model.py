"""
AI应用收藏表模型定义
提供AI应用收藏的数据模型和传输对象
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field
from multi_agent.model.ai_application_model import db


# SQLAlchemy Model
class AIApplicationFavoritesModel(db.Model):
    """AI应用收藏表SQLAlchemy模型"""
    __tablename__ = 'ai_application_favorites'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    tenant_id = db.Column(db.String(20), default='8', nullable=True, comment='租户编号')
    device_id = db.Column(db.String(64), nullable=True, comment='用户设备ID')
    user_id = db.Column(db.String(64), nullable=False, comment='用户ID')
    application_id = db.Column(db.BigInteger, nullable=False, comment='应用ID')
    creator = db.Column(db.String(20), nullable=True, comment='创建人')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updater = db.Column(db.String(20), nullable=True, comment='更新人')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')
    
    # 添加唯一约束，防止重复收藏
    __table_args__ = (
        db.UniqueConstraint('user_id', 'application_id', name='uk_user_app'),
    )
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'id': self.id,
            'tenantId': self.tenant_id,
            'deviceId': self.device_id,
            'userId': self.user_id,
            'applicationId': self.application_id,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None
        }


# DO (Data Object) - 数据传输对象，用于API请求/响应
class AIApplicationFavoritesDO(BaseModel):
    """AI应用收藏数据对象 - 统一的数据模型"""

    # 基础字段
    tenantId: Optional[str] = Field('8', alias='tenant_id')
    deviceId: Optional[str] = Field(None, alias='device_id')
    userId: Optional[str] = Field(None, alias='user_id')
    applicationId: Optional[int] = Field(None, alias='application_id')

    # 审计字段
    creator: Optional[str] = None
    updater: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        validate_by_name = True


# VO (View Object) - 视图对象，用于响应展示
class AIApplicationFavoritesVO(AIApplicationFavoritesDO):
    """AI应用收藏视图对象 - 包含展示相关字段"""

    # 系统字段
    id: Optional[int] = None
    createTime: Optional[datetime] = Field(None, alias='create_time')
    updateTime: Optional[datetime] = Field(None, alias='update_time')


# 扩展的视图对象，包含应用信息
class AIApplicationFavoritesWithAppVO(AIApplicationFavoritesVO):
    """AI应用收藏视图对象 - 包含应用详细信息"""

    # 应用信息字段
    appKey: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    appType: Optional[int] = None
    openingStatement: Optional[str] = None
    placeholder: Optional[str] = None
    isHasFile: Optional[str] = None
    apiBaseUrl: Optional[str] = None
    apiKey: Optional[str] = None

