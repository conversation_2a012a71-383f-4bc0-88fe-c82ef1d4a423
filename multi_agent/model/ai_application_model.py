from datetime import datetime
from typing import Optional, Dict, Any, List
import json

from pydantic import BaseModel, Field
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.types import TypeDecorator, TEXT

db = SQLAlchemy()

# Custom JSON type for SQLAlchemy
class JSONType(TypeDecorator):
    impl = TEXT
    
    def process_bind_param(self, value, dialect):
        if value is not None:
            value = json.dumps(value)
        return value
        
    def process_result_value(self, value, dialect):
        if value is not None:
            value = json.loads(value)
        return value


# SQLAlchemy Model
class AIApplicationModel(db.Model):
    """AI应用信息表SQLAlchemy模型"""
    __tablename__ = 'ai_application'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    app_key = db.Column(db.String(128), unique=True, nullable=False, index=True)
    name = db.Column(db.String(128), nullable=False)
    sort = db.Column(db.Integer, nullable=True)
    logo_url = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text, nullable=True)
    opening_statement = db.Column(db.Text, nullable=True)
    placeholder = db.Column(db.Text, nullable=True)
    is_has_file = db.Column(db.Text,default=0, nullable=True)
    is_show = db.Column(db.Text,default=1, nullable=True)
    app_type = db.Column(db.Integer, default=1, nullable=False)
    status = db.Column(db.Integer, default=1, nullable=False)
    visibility = db.Column(db.Integer, default=2, nullable=False)
    is_public = db.Column(db.Integer, default=0, nullable=False)
    is_featured = db.Column(db.Integer, default=0, nullable=False)
    is_skill_center = db.Column(db.Integer, default=0, nullable=False)
    api_base_url = db.Column(db.String(255), nullable=True)
    api_key = db.Column(db.String(255), nullable=True)
    dify_app_id = db.Column(db.String(255), nullable=True)
    api_secret = db.Column(db.String(255), nullable=True)
    api_headers = db.Column(JSONType, nullable=True)
    config = db.Column(JSONType, nullable=True)
    tenant_id = db.Column(db.Integer, default=8, nullable=False)
    company_id = db.Column(db.Integer, default=0, nullable=False)
    owner_id = db.Column(db.Integer, nullable=True)
    team_id = db.Column(db.Integer, nullable=True)
    version = db.Column(db.String(20), default="1.0.0", nullable=False)
    views_count = db.Column(db.Integer, default=0, nullable=False)
    likes_count = db.Column(db.Integer, default=0, nullable=False)
    shares_count = db.Column(db.Integer, default=0, nullable=False)
    chats_count = db.Column(db.Integer, default=0, nullable=False)
    deleted = db.Column(db.Boolean, default=False, nullable=False)
    creator = db.Column(db.String(50), nullable=True)
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False)
    updater = db.Column(db.String(50), nullable=True)
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'id': self.id,
            'appKey': self.app_key,
            'name': self.name,
            'sort': self.sort,
            'logoUrl': self.logo_url,
            'description': self.description,
            'openingStatement': self.opening_statement,
            'placeholder': self.placeholder,
            'isHasFile': self.is_has_file,
            'isShow': self.is_show,
            'appType': self.app_type,
            'status': self.status,
            'visibility': self.visibility,
            'isPublic': self.is_public,
            'isFeatured': self.is_featured,
            'isSkillCenter': self.is_skill_center,
            'apiBaseUrl': self.api_base_url,
            'apiKey': self.api_key,
            'difyAppId': self.dify_app_id,
            'apiSecret': self.api_secret,
            'apiHeaders': self.api_headers,
            'config': self.config,
            'tenantId': self.tenant_id,
            'companyId': self.company_id,
            'ownerId': self.owner_id,
            'teamId': self.team_id,
            'version': self.version,
            'viewsCount': self.views_count,
            'likesCount': self.likes_count,
            'sharesCount': self.shares_count,
            'chatsCount': self.chats_count,
            'deleted': self.deleted,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None,
        }


# ============= 简化模式：DO + VO =============

# DO (Data Object) - 数据传输对象，用于API请求/响应
class AIApplicationDO(BaseModel):
    """AI应用数据对象 - 统一的数据模型"""

    # 基础字段
    appKey: Optional[str] = Field(None, alias='app_key')
    name: Optional[str] = None
    sort: Optional[int] = None
    logoUrl: Optional[str] = Field(None, alias='logo_url')
    description: Optional[str] = None
    openingStatement: Optional[str] = None
    placeholder: Optional[str] = None
    isHasFile: Optional[int] = Field(0, alias='is_has_file')
    isShow: Optional[int] = Field(1, alias='is_show')
    
    # 配置字段
    appType: Optional[int] = Field(1, alias='app_type')
    status: Optional[int] = 1
    visibility: Optional[int] = 2
    isPublic: Optional[int] = Field(0, alias='is_public')
    isFeatured: Optional[int] = Field(0, alias='is_featured')
    isSkillCenter: Optional[int] = Field(0, alias='is_skill_center')

    # API配置
    apiBaseUrl: Optional[str] = Field(None, alias='api_base_url')
    apiKey: Optional[str] = Field(None, alias='api_key')
    difyAppId: Optional[str] = Field(None, alias='dify_app_id')
    apiSecret: Optional[str] = Field(None, alias='api_secret')
    apiHeaders: Optional[Dict[str, Any]] = Field(None, alias='api_headers')
    config: Optional[Dict[str, Any]] = None

    # 组织字段
    tenantId: Optional[int] = Field(8, alias='tenant_id')
    companyId: Optional[int] = Field(0, alias='company_id')
    ownerId: Optional[int] = Field(None, alias='owner_id')
    teamId: Optional[int] = Field(None, alias='team_id')
    version: Optional[str] = "1.0.0"

    # 审计字段
    creator: Optional[str] = None
    updater: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True


# VO (View Object) - 视图对象，用于响应展示
class AIApplicationVO(AIApplicationDO):
    """AI应用视图对象 - 包含展示相关字段"""

    # 系统字段
    id: Optional[int] = None
    createTime: Optional[datetime] = Field(None, alias='create_time')
    updateTime: Optional[datetime] = Field(None, alias='update_time')

    # 统计字段
    viewsCount: Optional[int] = Field(0, alias='views_count')
    likesCount: Optional[int] = Field(0, alias='likes_count')
    sharesCount: Optional[int] = Field(0, alias='shares_count')
    chatsCount: Optional[int] = Field(0, alias='chats_count')



