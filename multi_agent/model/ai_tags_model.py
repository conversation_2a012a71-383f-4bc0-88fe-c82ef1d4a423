from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field

# 使用现有的db实例
from multi_agent.model.ai_application_model import db


# SQLAlchemy Models
class AITagsModel(db.Model):
    """AI标签表SQLAlchemy模型"""
    __tablename__ = 'ai_tags'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='标签ID')
    tenant_id = db.Column(db.String(20), default='8', nullable=True, comment='租户编号')
    name = db.Column(db.String(50), nullable=False, comment='标签名称')
    slug = db.Column(db.String(50), nullable=False, unique=True, comment='标签标识')
    description = db.Column(db.String(255), nullable=True, comment='标签描述')
    color = db.Column(db.String(20), nullable=True, comment='标签颜色')
    sort = db.Column(db.Integer, default=0, nullable=False, comment='排序')
    post_count = db.Column(db.Integer, default=0, nullable=False, comment='使用次数')
    is_hot = db.Column(db.Integer, default=2, nullable=False, comment='是否热门标签 1热门 2不热门')
    deleted = db.Column(db.Boolean, default=False, nullable=False, comment='删除标志')
    creator = db.Column(db.String(20), nullable=True, comment='创建人')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updater = db.Column(db.String(20), nullable=True, comment='更新人')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'id': self.id,
            'tenantId': self.tenant_id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'color': self.color,
            'sort': self.sort,
            'postCount': self.post_count,
            'isHot': self.is_hot,
            'deleted': self.deleted,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None
        }


class AIApplicationTagsModel(db.Model):
    """AI应用与标签关联表SQLAlchemy模型"""
    __tablename__ = 'ai_application_tags'
    
    application_id = db.Column(db.BigInteger, primary_key=True, nullable=False, comment='应用ID')
    tag_id = db.Column(db.Integer, primary_key=True, nullable=False, comment='标签ID')
    tenant_id = db.Column(db.String(20), default='8', nullable=True, comment='租户编号')
    deleted = db.Column(db.Boolean, default=False, nullable=False, comment='删除标志')
    creator = db.Column(db.String(20), nullable=True, comment='创建人')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updater = db.Column(db.String(20), nullable=True, comment='更新人')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'applicationId': self.application_id,
            'tagId': self.tag_id,
            'tenantId': self.tenant_id,
            'deleted': self.deleted,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None
        }


# DO (Data Object) - 数据传输对象，用于API请求/响应
class AITagsDO(BaseModel):
    """AI标签数据对象 - 统一的数据模型"""

    # 基础字段
    tenantId: Optional[str] = Field('8', alias='tenant_id')
    name: Optional[str] = None
    slug: Optional[str] = None
    description: Optional[str] = None
    color: Optional[str] = None
    sort: Optional[int] = 0
    postCount: Optional[int] = Field(0, alias='post_count')
    isHot: Optional[int] = Field(2, alias='is_hot')

    # 审计字段
    creator: Optional[str] = None
    updater: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True


# VO (View Object) - 视图对象，用于响应展示
class AITagsVO(AITagsDO):
    """AI标签视图对象 - 包含展示相关字段"""

    # 系统字段
    id: Optional[int] = None
    createTime: Optional[datetime] = Field(None, alias='create_time')
    updateTime: Optional[datetime] = Field(None, alias='update_time')


class AIApplicationTagsDO(BaseModel):
    """AI应用标签关联数据对象"""

    applicationId: Optional[int] = Field(None, alias='application_id')
    tagId: Optional[int] = Field(None, alias='tag_id')
    tenantId: Optional[str] = Field('8', alias='tenant_id')

    # 审计字段
    creator: Optional[str] = None
    updater: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True


class AIApplicationTagsVO(AIApplicationTagsDO):
    """AI应用标签关联视图对象"""

    # 系统字段
    createTime: Optional[datetime] = Field(None, alias='create_time')
    updateTime: Optional[datetime] = Field(None, alias='update_time')
