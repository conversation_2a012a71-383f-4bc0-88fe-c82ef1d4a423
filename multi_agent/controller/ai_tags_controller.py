"""
AI标签控制器
提供AI标签的增删改查API接口
"""
import logging
from flask import Blueprint, jsonify, request, g
from http import HTTPStatus
from pydantic import ValidationError

from multi_agent.service.ai_tags_service import AITagsService
from multi_agent.model.ai_tags_model import AITagsDO
from utils.response_utils import success_response, error_response, paginated_response
from utils.request import get_pagination_params, get_str_param, get_int_param
from utils.decorators import handle_exceptions, log_request

# 创建Blueprint
ai_tags_bp = Blueprint('ai_tags', __name__)
service = AITagsService()


@ai_tags_bp.route('', methods=['GET'])
@handle_exceptions
@log_request
def get_tags():
    """
    获取标签列表
    
    Query Parameters:
        pageNo: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)
        keyword: 搜索关键词
        isHot: 是否热门标签 (1热门 2不热门)
        tenantId: 租户ID
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取分页参数
        page_no, page_size = get_pagination_params()

        # 2. 获取过滤参数
        keyword = get_str_param('keyword')
        is_hot = get_int_param('isHot')
        tenant_id = get_str_param('tenantId')

        # 3. 构建过滤条件（处理空字符串）
        filters = {
            'keyword': keyword.strip() if keyword and keyword.strip() else None,
            'isHot': is_hot,
            'tenantId': tenant_id.strip() if tenant_id and tenant_id.strip() else None
        }

        # 4. 调用服务层
        tags, total = service.search_tags_with_count(
            filters=filters,
            limit=page_size,
            offset=(page_no - 1) * page_size
        )

        # 5. 返回分页响应
        return paginated_response(
            data=[tag.dict() for tag in tags],
            total=total,
            page=page_no,
            page_size=page_size,
            message="获取标签列表成功"
        )

    except Exception as e:
        logging.error(f"获取标签列表失败: {e}")
        return error_response(
            message="获取标签列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@ai_tags_bp.route('/list', methods=['GET'])
@handle_exceptions
@log_request
def get_tags_list():
    """
    获取标签列表（不分页）

    Query Parameters:
        limit: 获取数量限制 (默认: 20, 最大: 100)
        isHot: 是否热门标签 (1热门 2不热门)

    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取limit参数
        limit = get_int_param('limit') or 20
        # 限制最大值
        if limit > 100:
            limit = 100
        if limit < 1:
            limit = 20

        # 2. 获取过滤参数
        keyword = get_str_param('keyword')
        is_hot = get_int_param('isHot')

        # 3. 构建过滤条件（处理空字符串）
        filters = {
            'keyword': keyword.strip() if keyword and keyword.strip() else None,
            'isHot': is_hot
        }

        # 4. 调用服务层（不分页，只传limit）
        tags, total = service.search_tags_with_count(
            filters=filters,
            limit=limit,
            offset=0
        )

        # 5. 返回简单的列表响应
        return success_response(
            data={
                'list': [tag.dict() for tag in tags],
                'total': total,
                'limit': limit
            },
            message="获取标签列表成功"
        )

    except Exception as e:
        logging.error(f"获取标签列表失败: {e}")
        return error_response(
            message="获取标签列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@ai_tags_bp.route('', methods=['POST'])
@handle_exceptions
@log_request
def create_tag():
    """
    创建新标签
    
    Request Body:
        AITagsDO: 创建请求数据
        
    Returns:
        201: 创建成功
        400: 数据验证失败
        409: slug已存在
    """
    try:
        # 1. Pydantic自动验证请求数据
        request_data = AITagsDO(**request.json)
        # 创建时必须提供name和slug
        if not request_data.name or not request_data.slug:
            raise ValueError("标签名称和标识为必填字段")

        # 2. 获取当前用户
        creator = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

        # 3. 调用服务层
        response_data = service.create_tag(request_data, creator)

        # 4. 返回标准响应
        return success_response(
            data=response_data.dict(),
            message="标签创建成功",
            status_code=HTTPStatus.OK
        )

    except ValidationError as e:
        return error_response(
            message=f"数据验证失败: {e.errors()}",
            status_code=HTTPStatus.BAD_REQUEST
        )

    except ValueError as e:
        # 业务逻辑错误（如slug已存在）
        return error_response(
            message=str(e),
            status_code=HTTPStatus.CONFLICT
        )


# @ai_tags_bp.route('/<int:tag_id>', methods=['GET'])
@handle_exceptions
@log_request
def get_tag(tag_id: int):
    """根据ID获取标签详情"""
    tag = service.get_tag_by_id(tag_id)
    if not tag:
        return error_response(
            message=f"标签不存在: ID={tag_id}",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(data=tag.dict())


# @ai_tags_bp.route('/<int:tag_id>', methods=['PUT'])
@handle_exceptions
@log_request
def update_tag(tag_id: int):
    """
    更新标签信息
    
    Path Parameters:
        tag_id: 标签ID
        
    Request Body:
        AITagsDO: 更新请求数据
        
    Returns:
        200: 更新成功
        400: 数据验证失败
        404: 标签不存在
    """
    try:
        # 1. Pydantic验证请求数据
        request_data = AITagsDO(**request.json)

        # 2. 获取当前用户
        updater = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

        # 3. 调用服务层
        response_data = service.update_tag(tag_id, request_data, updater)

        if not response_data:
            return error_response(
                message="标签不存在",
                status_code=HTTPStatus.NOT_FOUND
            )

        # 4. 返回响应
        return success_response(
            data=response_data.dict(),
            message="标签更新成功"
        )

    except ValidationError as e:
        return error_response(
            message=f"数据验证失败: {e.errors()}",
            status_code=HTTPStatus.BAD_REQUEST
        )


# @ai_tags_bp.route('/<int:tag_id>', methods=['DELETE'])
@handle_exceptions
@log_request
def delete_tag(tag_id: int):
    """
    删除标签（软删除）
    
    Path Parameters:
        tag_id: 标签ID
        
    Returns:
        200: 删除成功
        404: 标签不存在
    """
    # 获取当前用户
    deleter = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

    # 调用服务层
    success = service.delete_tag(tag_id, deleter)

    if not success:
        return error_response(
            message="标签不存在",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(
        message="标签删除成功"
    )


@ai_tags_bp.route('/by-slug/<slug>', methods=['GET'])
@handle_exceptions
@log_request
def get_tag_by_slug(slug: str):
    """根据slug获取标签详情"""
    tag = service.get_tag_by_slug(slug)
    if not tag:
        return error_response(
            message=f"标签不存在: slug={slug}",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(data=tag.dict())


@ai_tags_bp.route('/applications', methods=['GET'])
@handle_exceptions
@log_request
def get_applications_by_tag():
    """
    根据标签slug获取应用列表，支持关键词搜索

    Query Parameters:
        slug: 标签标识，不传递时获取所有应用
        keyword: 搜索关键词，同时搜索应用名称和描述
        pageNo: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)

    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取分页参数
        page_no, page_size = get_pagination_params()

        # 2. 获取查询参数（处理空字符串）
        slug = get_str_param('slug')
        keyword = get_str_param('keyword')

        # 处理空字符串
        slug = slug.strip() if slug and slug.strip() else None
        keyword = keyword.strip() if keyword and keyword.strip() else None

        # 3. 调用服务层
        applications, total = service.get_applications_by_tag_slug(
            slug=slug,
            keyword=keyword,
            limit=page_size,
            offset=(page_no - 1) * page_size
        )

        # 4. 返回分页响应
        # applications 已经是字典列表，不需要再调用 .dict() 方法
        return paginated_response(
            data=applications,
            total=total,
            page=page_no,
            page_size=page_size,
            message="获取应用列表成功"
        )

    except Exception as e:
        logging.error(f"根据标签获取应用列表失败: {e}")
        return error_response(
            message="获取应用列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


# 应用标签关联相关接口
@ai_tags_bp.route('/applications/<int:application_id>/tags', methods=['POST'])
@handle_exceptions
@log_request
def add_application_tag(application_id: int):
    """
    为应用添加标签

    Path Parameters:
        application_id: 应用ID

    Request Body:
        tagId: 标签ID

    Returns:
        201: 添加成功
        400: 数据验证失败
        404: 应用或标签不存在
        409: 关联已存在
    """
    try:
        # 1. 获取请求数据
        json_data = request.get_json()
        if not json_data or 'tagId' not in json_data:
            raise ValueError("tagId为必填字段")

        tag_id = json_data['tagId']
        if not isinstance(tag_id, int):
            raise ValueError("tagId必须为整数")

        # 2. 获取当前用户
        creator = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

        # 3. 调用服务层
        response_data = service.add_application_tag(application_id, tag_id, creator)

        # 4. 返回标准响应
        return success_response(
            data=response_data.dict(),
            message="应用标签添加成功",
            status_code=HTTPStatus.OK
        )

    except ValueError as e:
        status_code = HTTPStatus.NOT_FOUND if "不存在" in str(e) else HTTPStatus.BAD_REQUEST
        return error_response(
            message=str(e),
            status_code=status_code
        )


# @ai_tags_bp.route('/applications/<int:application_id>/tags/<int:tag_id>', methods=['DELETE'])
@handle_exceptions
@log_request
def remove_application_tag(application_id: int, tag_id: int):
    """
    移除应用标签

    Path Parameters:
        application_id: 应用ID
        tag_id: 标签ID

    Returns:
        200: 移除成功
        404: 关联不存在
    """
    # 调用服务层
    success = service.remove_application_tag(application_id, tag_id)

    if not success:
        return error_response(
            message="应用标签关联不存在",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(
        message="应用标签移除成功"
    )


@ai_tags_bp.route('/applications/<int:application_id>/tags', methods=['GET'])
@handle_exceptions
@log_request
def get_application_tags(application_id: int):
    """
    获取应用的所有标签

    Path Parameters:
        application_id: 应用ID

    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 调用服务层
        tags = service.get_application_tags(application_id)

        # 返回响应
        return success_response(
            data=[tag.dict() for tag in tags],
            message="获取应用标签成功"
        )

    except Exception as e:
        logging.error(f"获取应用标签失败: {e}")
        return error_response(
            message="获取应用标签失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )
