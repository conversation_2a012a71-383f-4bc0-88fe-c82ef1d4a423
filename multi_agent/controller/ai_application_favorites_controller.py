"""
AI应用收藏控制器
提供AI应用收藏的增删改查API接口
"""
import logging
from flask import Blueprint, jsonify, request, g
from http import HTTPStatus
from pydantic import ValidationError

from multi_agent.service.ai_application_favorites_service import AIApplicationFavoritesService
from multi_agent.model.ai_application_favorites_model import AIApplicationFavoritesDO
from utils.response_utils import success_response, error_response, paginated_response
from utils.request import get_pagination_params, get_str_param, get_int_param
from utils.decorators import handle_exceptions, log_request

# 创建Blueprint
ai_application_favorites_bp = Blueprint('ai_application_favorites', __name__)
service = AIApplicationFavoritesService()


@ai_application_favorites_bp.route('', methods=['POST'])
@handle_exceptions
@log_request
def add_favorite():
    """
    添加应用收藏
    
    Request Body:
        {
            "userId": 用户ID,
            "applicationId": 应用ID,
            "tenantId": "租户ID" (可选，默认为8)
        }
        
    Returns:
        201: 收藏成功
        400: 数据验证失败
        409: 应用已收藏或应用不存在
    """
    try:
        # 1. Pydantic自动验证请求数据
        request_data = AIApplicationFavoritesDO(**request.json)
        
        # 2. 验证必填字段
        if not request_data.userId or not request_data.applicationId:
            raise ValueError("userId和applicationId为必填字段")
        
        # 3. 获取当前用户（从认证中间件或session中获取）
        creator = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')
        
        # 4. 调用服务层
        response_data = service.add_favorite(request_data, creator)
        
        # 5. 返回标准响应
        return success_response(
            data=response_data.dict(),
            message="收藏成功",
            status_code=HTTPStatus.OK
        )

    except ValidationError as e:
        return error_response(
            message=f"数据验证失败: {e.errors()}",
            status_code=HTTPStatus.BAD_REQUEST
        )

    except ValueError as e:
        # 业务逻辑错误（如应用已收藏、应用不存在等）
        return error_response(
            message=str(e),
            status_code=HTTPStatus.CONFLICT
        )


@ai_application_favorites_bp.route('', methods=['DELETE'])
@handle_exceptions
@log_request
def remove_favorite():
    """
    取消应用收藏
    
    Query Parameters:
        userId: 用户ID
        applicationId: 应用ID
        
    Returns:
        200: 取消收藏成功
        400: 参数错误
        404: 收藏记录不存在
    """
    try:
        # 1. 获取查询参数
        user_id = get_str_param('userId')
        application_id = get_int_param('applicationId')
        
        # 2. 验证必填参数
        if not user_id or not application_id:
            raise ValueError("userId和applicationId为必填参数")
        
        # 3. 调用服务层
        success = service.remove_favorite(user_id, application_id)
        
        if not success:
            return error_response(
                message="收藏记录不存在",
                status_code=HTTPStatus.NOT_FOUND
            )

        return success_response(
            message="取消收藏成功"
        )

    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=HTTPStatus.BAD_REQUEST
        )


@ai_application_favorites_bp.route('/check', methods=['GET'])
@handle_exceptions
@log_request
def check_favorite():
    """
    检查应用是否已收藏
    
    Query Parameters:
        userId: 用户ID
        applicationId: 应用ID
        
    Returns:
        200: 检查成功
        400: 参数错误
    """
    try:
        # 1. 获取查询参数
        user_id = get_str_param('userId')
        application_id = get_int_param('applicationId')
        
        # 2. 验证必填参数
        if not user_id or not application_id:
            raise ValueError("userId和applicationId为必填参数")
        
        # 3. 调用服务层
        is_favorite = service.is_favorited(user_id, application_id)
        
        return success_response(
            data={"is_favorite": is_favorite},
            message="检查收藏状态成功"
        )

    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=HTTPStatus.BAD_REQUEST
        )


@ai_application_favorites_bp.route('/list', methods=['GET'])
@handle_exceptions
@log_request
def get_user_favorites():
    """
    获取用户收藏的应用列表（非分页）
    
    Query Parameters:
        userId: 用户ID
        
    Returns:
        200: 获取成功，默认返回按收藏时间倒序的100条记录
        400: 参数错误
    """
    try:
        # 1. 获取查询参数
        user_id = get_str_param('userId')
        
        # 2. 验证必填参数
        if not user_id:
            raise ValueError("userId为必填参数")
        
        # 3. 调用服务层，固定查询100条记录
        favorites = service.get_user_favorites(
            user_id=user_id,
            limit=100,
            offset=0
        )
        
        # 4. 返回标准响应
        return success_response(
            data=[favorite.to_dict() for favorite in favorites],
            message="获取收藏列表成功"
        )

    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=HTTPStatus.BAD_REQUEST
        )
    except Exception as e:
        logging.error(f"获取收藏列表失败: {e}")
        return error_response(
            message="获取收藏列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@ai_application_favorites_bp.route('/page', methods=['GET'])
@handle_exceptions
@log_request
def get_user_favorites_page():
    """
    获取用户收藏的应用列表（分页查询）
    
    Query Parameters:
        userId: 用户ID
        pageNo: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取查询参数
        user_id = get_str_param('userId')
        
        # 2. 验证必填参数
        if not user_id:
            raise ValueError("userId为必填参数")
        
        # 3. 获取分页参数
        page_no, page_size = get_pagination_params()
        
        # 4. 调用服务层
        favorites = service.get_user_favorites(
            user_id=user_id,
            limit=page_size,
            offset=(page_no - 1) * page_size
        )
        
        # 5. 获取总数
        total = service.get_user_favorite_count(user_id)
        
        # 6. 返回分页响应
        return paginated_response(
            data=[favorite.dict() for favorite in favorites],
            total=total,
            page=page_no,
            page_size=page_size,
            message="获取收藏列表成功"
        )

    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=HTTPStatus.BAD_REQUEST
        )
    except Exception as e:
        logging.error(f"获取收藏列表失败: {e}")
        return error_response(
            message="获取收藏列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@ai_application_favorites_bp.route('/count', methods=['GET'])
@handle_exceptions
@log_request
def get_favorites_count():
    """
    获取收藏数量统计
    
    Query Parameters:
        type: 统计类型 (user: 用户收藏总数, app: 应用收藏数量)
        userId: 用户ID (type=user时必填)
        applicationId: 应用ID (type=app时必填)
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取查询参数
        count_type = get_str_param('type')
        
        if count_type == 'user':
            # 获取用户收藏总数
            user_id = get_str_param('userId')
            
            if not user_id:
                raise ValueError("userId为必填参数")
            
            count = service.get_user_favorite_count(user_id)
            
        elif count_type == 'app':
            # 获取应用收藏数量
            application_id = get_int_param('applicationId')
            
            if not application_id:
                raise ValueError("applicationId为必填参数")
            
            count = service.get_application_favorite_count(application_id)
            
        else:
            raise ValueError("type参数必须为'user'或'app'")
        
        return success_response(
            data={"count": count},
            message="获取收藏数量成功"
        )

    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=HTTPStatus.BAD_REQUEST
        )
