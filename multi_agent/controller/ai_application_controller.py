"""
AI应用控制器
提供AI应用的增删改查API接口
"""
import logging
from flask import Blueprint, jsonify, request, g
from http import HTTPStatus
from pydantic import ValidationError

from multi_agent.service.ai_application_service import AIApplicationService
from multi_agent.model.ai_application_model import AIApplicationDO
from utils.response_utils import success_response, error_response, paginated_response
from utils.request import get_pagination_params, get_str_param, get_int_param
from utils.decorators import handle_exceptions, log_request
# 导入标签服务
from multi_agent.service.ai_tags_service import AITagsService

# 创建Blueprint
ai_application_bp = Blueprint('ai_application', __name__)
service = AIApplicationService()
tags_service = AITagsService()


@ai_application_bp.route('', methods=['GET'])
@handle_exceptions
@log_request
def get_applications():
    """
    获取AI应用列表
    
    Query Parameters:
        pageNo: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)
        keyword: 搜索关键词
        status: 应用状态
        appType: 应用类型
        tenantId: 租户ID
        
    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取分页参数
        page_no, page_size = get_pagination_params()

        # 2. 获取过滤参数
        keyword = get_str_param('keyword')
        status = get_int_param('status')
        app_type = get_int_param('appType')

        # 3. 构建过滤条件
        filters = {
            'keyword': keyword,
            'status': status,
            'appType': app_type
        }

        # 4. 调用服务层
        applications, total = service.search_applications_with_count(
            filters=filters,
            limit=page_size,
            offset=(page_no - 1) * page_size
        )

        # 5. 返回分页响应
        return paginated_response(
            data=[app.dict() for app in applications],
            total=total,
            page=page_no,
            page_size=page_size,
            message="获取AI应用列表成功"
        )

    except Exception as e:
        logging.error(f"获取AI应用列表失败: {e}")
        return error_response(
            message="获取列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


# @ai_application_bp.route('', methods=['POST'])
@handle_exceptions
@log_request
def create_application():
    """
    创建新AI应用
    
    Request Body:
        AIApplicationDO: 创建请求数据
        
    Returns:
        201: 创建成功
        400: 数据验证失败
        409: appKey已存在
    """
    try:
        # 1. Pydantic自动验证请求数据
        request_data = AIApplicationDO(**request.json)
        # 创建时必须提供appKey和name
        if not request_data.appKey or not request_data.name:
            raise ValueError("appKey和name为必填字段")

        # 2. 获取当前用户（从认证中间件或session中获取）
        creator = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

        # 3. 调用服务层
        response_data = service.create_application(request_data, creator)

        # 4. 返回标准响应
        return success_response(
            data=response_data.dict(),
            message="AI应用创建成功",
            status_code=HTTPStatus.OK
        )

    except ValidationError as e:
        return error_response(
            message=f"数据验证失败: {e.errors()}",
            status_code=HTTPStatus.BAD_REQUEST
        )

    except ValueError as e:
        # 业务逻辑错误（如appKey已存在）
        return error_response(
            message=str(e),
            status_code=HTTPStatus.CONFLICT
        )


# @ai_application_bp.route('/<int:app_id>', methods=['GET'])
@handle_exceptions
@log_request
def get_application(app_id: int):
    """根据ID获取AI应用详情"""
    application = service.get_application_by_id(app_id)
    if not application:
        return error_response(
            message=f"应用不存在: ID={app_id}",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(data=application.dict())


# @ai_application_bp.route('/<int:app_id>', methods=['PUT'])
@handle_exceptions
@log_request
def update_application(app_id: int):
    """
    更新AI应用信息
    
    Path Parameters:
        app_id: 应用ID
        
    Request Body:
        AIApplicationDO: 更新请求数据
        
    Returns:
        200: 更新成功
        400: 数据验证失败
        404: 应用不存在
    """
    try:
        # 1. Pydantic验证请求数据
        request_data = AIApplicationDO(**request.json)

        # 2. 获取当前用户
        updater = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

        # 3. 调用服务层
        response_data = service.update_application(app_id, request_data, updater)

        if not response_data:
            return error_response(
                message="AI应用不存在",
                status_code=HTTPStatus.NOT_FOUND
            )

        # 4. 返回响应
        return success_response(
            data=response_data.dict(),
            message="AI应用更新成功"
        )

    except ValidationError as e:
        return error_response(
            message=f"数据验证失败: {e.errors()}",
            status_code=HTTPStatus.BAD_REQUEST
        )


# @ai_application_bp.route('/<int:app_id>', methods=['DELETE'])
@handle_exceptions
@log_request
def delete_application(app_id: int):
    """
    删除AI应用（软删除）
    
    Path Parameters:
        app_id: 应用ID
        
    Returns:
        200: 删除成功
        404: 应用不存在
    """
    # 获取当前用户
    deleter = getattr(g, 'current_user', None) or request.headers.get('X-User-Name', 'system')

    # 调用服务层
    success = service.delete_application(app_id, deleter)

    if not success:
        return error_response(
            message="AI应用不存在",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(
        message="AI应用删除成功"
    )


@ai_application_bp.route('/get-by-app-key', methods=['GET'])
@handle_exceptions
@log_request
def get_application_by_key():
    """根据应用Key获取AI应用详情"""
    appKey =  get_str_param("appKey")
    application = service.get_application_by_app_key(appKey)
    if not application:
        return error_response(
            message=f"应用不存在: appKey={appKey}",
            status_code=HTTPStatus.NOT_FOUND
        )

    return success_response(data=application.dict())


@ai_application_bp.route('/list-skill-center', methods=['GET'])
@handle_exceptions
@log_request
def get_skill_center_applications():
    """
    获取技能中心应用列表

    Query Parameters:
        pageNo: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 20, 最大: 100)
        keyword: 搜索关键词
        status: 应用状态
        appType: 应用类型

    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取分页参数
        page_no, page_size = get_pagination_params()

        # 2. 获取过滤参数
        keyword = get_str_param('keyword')
        status = get_int_param('status')
        app_type = get_int_param('appType')

        # 3. 调用服务层获取技能中心应用
        applications, total = service.get_skill_center_applications(
            keyword=keyword,
            status=status,
            app_type=app_type,
            limit=page_size,
            offset=(page_no - 1) * page_size
        )

        # 4. 返回分页响应
        return paginated_response(
            data=[app.dict() for app in applications],
            total=total,
            page=page_no,
            page_size=page_size,
            message="获取技能中心应用列表成功"
        )

    except Exception as e:
        logging.error(f"获取技能中心应用列表失败: {e}")
        return error_response(
            message="获取技能中心应用列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@ai_application_bp.route('/by-tag', methods=['GET'])
@handle_exceptions
@log_request
def get_applications_by_tag():
    """
    根据标签slug获取应用列表

    Query Parameters:
        slug: 标签标识，不传递时获取所有应用
        pageNo: 页码 (默认: 1)
        pageSize: 每页大小 (默认: 100, 最大: 100)

    Returns:
        200: 获取成功
        400: 参数错误
    """
    try:
        # 1. 获取分页参数
        page_no, page_size = get_pagination_params()

        # 2. 获取标签slug参数
        slug = get_str_param('slug')
        keyword = get_str_param('keyword')

        # 3. 调用标签服务层
        applications, total = tags_service.get_applications_by_tag_slug(
            slug=slug,
            keyword=keyword,
            limit=page_size,
            offset=(page_no - 1) * page_size
        )

        # 4. 返回分页响应
        # applications 已经是字典列表，不需要再调用 .dict() 方法
        return paginated_response(
            data=applications,
            total=total,
            page=page_no,
            page_size=page_size,
            message="获取应用列表成功"
        )

    except Exception as e:
        logging.error(f"根据标签获取应用列表失败: {e}")
        return error_response(
            message="获取应用列表失败",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )

