"""
AI标签服务类 - 重构版本
提供AI标签的业务逻辑处理，使用DAO层进行数据访问
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from dify.service import dify_service
from multi_agent.model.ai_tags_model import (
    AITagsDO,
    AITagsVO,
    AIApplicationTagsVO
)
from multi_agent.dao.ai_tags_dao import AITagsDAO, AIApplicationTagsDAO
from multi_agent.dao.ai_application_dao import AIApplicationDAO


class AITagsService:
    """AI标签服务类，处理标签的业务逻辑"""

    def __init__(self):
        self.dify_service = dify_service.DifyService()
        self.tags_dao = AITagsDAO()
        self.app_tags_dao = AIApplicationTagsDAO()
        self.app_dao = AIApplicationDAO()

    def get_all_tags(self) -> List[AITagsVO]:
        """获取所有标签"""
        try:
            tags = self.tags_dao.get_all()
            return [AITagsVO.from_orm(tag) for tag in tags]
        except Exception as e:
            logging.error(f"获取所有标签失败: {e}")
            raise

    def get_tag_by_id(self, tag_id: int) -> Optional[AITagsVO]:
        """根据ID获取标签"""
        try:
            tag = self.tags_dao.get_by_id(tag_id)
            return AITagsVO.from_orm(tag) if tag else None
        except Exception as e:
            logging.error(f"获取标签失败, ID: {tag_id}, 错误: {e}")
            raise

    def get_tag_by_slug(self, slug: str) -> Optional[AITagsVO]:
        """根据slug获取标签"""
        try:
            tag = self.tags_dao.get_by_slug(slug)
            return AITagsVO.from_orm(tag) if tag else None
        except Exception as e:
            logging.error(f"获取标签失败, slug: {slug}, 错误: {e}")
            raise

    def create_tag(self, request_data: AITagsDO, creator: str = None) -> AITagsVO:
        """
        创建标签
        
        Args:
            request_data: 经过Pydantic验证的创建请求数据
            creator: 创建者用户名
            
        Returns:
            创建的标签响应数据
            
        Raises:
            ValueError: 当slug已存在或数据验证失败时
            IntegrityError: 数据库约束错误
        """
        try:
            # 1. 业务逻辑验证
            if not request_data.name or not request_data.slug:
                raise ValueError("标签名称和标识为必填字段")

            existing = self.tags_dao.get_by_slug(request_data.slug)
            if existing:
                raise ValueError(f"标签标识 '{request_data.slug}' 已存在")

            # 2. 转换Pydantic模型为字典数据
            model_data = request_data.dict(exclude_unset=True)
            if creator:
                model_data['creator'] = creator

            # 3. 创建数据库记录
            tag = self.tags_dao.create(model_data)
            if not tag:
                raise RuntimeError("创建标签失败")

            # 4. 返回响应模型
            return AITagsVO.from_orm(tag)

        except ValueError:
            raise
        except Exception as e:
            logging.error(f"创建标签失败: {e}")
            raise

    def update_tag(self, tag_id: int, request_data: AITagsDO, updater: str = None) -> Optional[AITagsVO]:
        """
        更新标签
        
        Args:
            tag_id: 标签ID
            request_data: 更新请求数据
            updater: 更新者用户名
            
        Returns:
            更新后的标签响应数据或None（如果标签不存在）
        """
        try:
            # 1. 检查标签是否存在
            tag = self.tags_dao.get_by_id(tag_id)
            if not tag:
                return None

            # 2. 检查slug唯一性（如果要更新slug）
            if request_data.slug and request_data.slug != tag.slug:
                existing = self.tags_dao.get_by_slug(request_data.slug)
                if existing:
                    raise ValueError(f"标签标识 '{request_data.slug}' 已存在")

            # 3. 准备更新数据
            update_data = request_data.dict(exclude_unset=True, exclude_none=True)
            if not update_data:
                return AITagsVO.from_orm(tag)

            # 4. 添加审计信息
            if updater:
                update_data['updater'] = updater
            update_data['update_time'] = datetime.now()

            # 5. 更新标签
            success = self.tags_dao.update(tag_id, update_data)
            if not success:
                return None

            # 6. 返回更新后的标签
            updated_tag = self.tags_dao.get_by_id(tag_id)
            return AITagsVO.from_orm(updated_tag) if updated_tag else None

        except ValueError:
            raise
        except Exception as e:
            logging.error(f"更新标签失败, ID: {tag_id}, 错误: {e}")
            raise

    def delete_tag(self, tag_id: int, deleter: str = None) -> bool:
        """
        删除标签（软删除）
        
        Args:
            tag_id: 标签ID
            deleter: 删除者用户名
            
        Returns:
            删除成功返回True，标签不存在返回False
        """
        try:
            return self.tags_dao.soft_delete(tag_id, deleter)
        except Exception as e:
            logging.error(f"删除标签失败, ID: {tag_id}, 错误: {e}")
            raise

    def search_tags(self,
                    keyword: Optional[str] = None,
                    tenant_id: Optional[str] = None,
                    is_hot: Optional[int] = None,
                    limit: int = 20,
                    offset: int = 0) -> Tuple[List[AITagsVO], int]:
        """搜索标签"""
        try:
            # 构建过滤条件字典
            filters = {
                'keyword': keyword,
                'tenantId': tenant_id,
                'isHot': is_hot
            }

            # 调用DAO层搜索
            tags = self.tags_dao.search_tags(filters, limit, offset)
            total = self.tags_dao.count_tags(filters)

            # 转换为VO对象
            tag_vos = [AITagsVO.from_orm(tag) for tag in tags]

            return tag_vos, total

        except Exception as e:
            logging.error(f"搜索标签失败: {e}")
            raise

    def get_hot_tags(self, limit: int = 10) -> List[AITagsVO]:
        """获取热门标签"""
        try:
            tags = self.tags_dao.get_hot_tags(limit)
            return [AITagsVO.from_orm(tag) for tag in tags]
        except Exception as e:
            logging.error(f"获取热门标签失败: {e}")
            raise

    def get_tags_by_tenant(self, tenant_id: str, limit: int = 100, offset: int = 0) -> List[AITagsVO]:
        """根据租户获取标签"""
        try:
            tags = self.tags_dao.get_by_tenant(tenant_id, limit, offset)
            return [AITagsVO.from_orm(tag) for tag in tags]
        except Exception as e:
            logging.error(f"根据租户获取标签失败: {e}")
            raise

    def add_application_tag(self, application_id: int, tag_id: int, creator: str = None) -> AIApplicationTagsVO:
        """
        为应用添加标签
        
        Args:
            application_id: 应用ID
            tag_id: 标签ID
            creator: 创建者用户名
            
        Returns:
            创建的关联记录
            
        Raises:
            ValueError: 当应用或标签不存在，或关联已存在时
        """
        try:
            # 1. 验证应用是否存在
            app = self.app_dao.get_by_id(application_id)
            if not app:
                raise ValueError(f"应用不存在: ID={application_id}")

            # 2. 验证标签是否存在
            tag = self.tags_dao.get_by_id(tag_id)
            if not tag:
                raise ValueError(f"标签不存在: ID={tag_id}")

            # 3. 检查关联是否已存在
            if self.app_tags_dao.exists(application_id, tag_id):
                raise ValueError("应用标签关联已存在")

            # 4. 创建关联记录
            relation_data = {
                'application_id': application_id,
                'tag_id': tag_id,
                'tenant_id': app.tenant_id
            }
            if creator:
                relation_data['creator'] = creator

            relation = self.app_tags_dao.create(relation_data)
            if not relation:
                raise RuntimeError("创建应用标签关联失败")

            # 5. 增加标签使用次数
            self.tags_dao.increment_post_count(tag_id)

            return AIApplicationTagsVO.from_orm(relation)

        except ValueError:
            raise
        except Exception as e:
            logging.error(f"添加应用标签失败: {e}")
            raise

    def remove_application_tag(self, application_id: int, tag_id: int) -> bool:
        """
        移除应用标签
        
        Args:
            application_id: 应用ID
            tag_id: 标签ID
            
        Returns:
            删除成功返回True，关联不存在返回False
        """
        try:
            return self.app_tags_dao.delete_by_application_and_tag(application_id, tag_id)
        except Exception as e:
            logging.error(f"移除应用标签失败: {e}")
            raise

    def get_application_tags(self, application_id: int) -> List[AITagsVO]:
        """获取应用的所有标签"""
        try:
            relations = self.app_tags_dao.get_by_application_id(application_id)
            tag_ids = [rel.tag_id for rel in relations]

            tags = []
            for tag_id in tag_ids:
                tag = self.tags_dao.get_by_id(tag_id)
                if tag:
                    tags.append(AITagsVO.from_orm(tag))

            return tags
        except Exception as e:
            logging.error(f"获取应用标签失败: {e}")
            raise

    def get_tag_applications(self, tag_id: int, limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
        """获取标签下的所有应用"""
        try:
            relations = self.app_tags_dao.get_by_tag_id(tag_id)
            app_ids = [rel.application_id for rel in relations]

            applications = []
            for app_id in app_ids[offset:offset + limit]:
                app = self.app_dao.get_by_id(app_id)
                if app:
                    applications.append({
                        'id': app.id,
                        'app_key': app.app_key,
                        'name': app.name,
                        'description': app.description,
                        'logo_url': app.logo_url,
                        'status': app.status,
                        'views_count': app.views_count,
                        'created_at': app.create_time.isoformat() if app.create_time else None
                    })

            return applications
        except Exception as e:
            logging.error(f"获取标签应用失败: {e}")
            raise

    def validate_slug(self, slug: str, exclude_id: int = None) -> bool:
        """验证slug是否可用"""
        try:
            existing = self.tags_dao.get_by_slug(slug)
            if not existing:
                return True

            # 如果是更新操作，排除当前标签ID
            if exclude_id and existing.id == exclude_id:
                return True

            return False
        except Exception as e:
            logging.error(f"验证slug失败: {e}")
            return False

    def get_applications_by_tag_slug(self, slug: str = None,
                                     keyword: str = None,
                                     limit: int = 20,
                                     offset: int = 0) -> Tuple[List[Dict[str, Any]], int]:
        """
        根据标签slug获取应用列表，支持关键词搜索

        Args:
            slug: 标签标识，为None时获取所有应用
            keyword: 搜索关键词，同时搜索应用名称和描述
            limit: 限制数量
            offset: 偏移量

        Returns:
            应用列表和总数
        """
        try:
            # 处理空字符串的slug和keyword
            slug = slug.strip() if slug and slug.strip() else None
            keyword = keyword.strip() if keyword and keyword.strip() else None

            if slug is None:
                # 不传递slug，获取全部应用
                filters = {}
                if keyword:
                    filters['keyword'] = keyword

                applications = self.app_dao.search_applications(filters, limit, offset)
                total = self.app_dao.count_applications(filters)
            else:
                # 传递slug，获取特定标签的应用
                # 先查找标签
                tag = self.tags_dao.get_by_slug(slug)
                if not tag:
                    return [], 0

                # 获取该标签下的应用
                relations = self.app_tags_dao.get_by_tag_id(tag.id)
                app_ids = [rel.application_id for rel in relations]

                if not app_ids:
                    return [], 0

                # 构建应用查询条件
                applications = []
                for app_id in app_ids[offset:offset + limit]:
                    app = self.app_dao.get_by_id(app_id)
                    if app:
                        # 如果有关键词，进行过滤
                        if keyword:
                            if (keyword.lower() in app.name.lower() or
                                    (app.description and keyword.lower() in app.description.lower())):
                                applications.append(app)
                        else:
                            applications.append(app)

                total = len([rel for rel in relations if self.app_dao.get_by_id(rel.application_id)])

                # 如果有关键词过滤，重新计算总数
                if keyword:
                    filtered_count = 0
                    for rel in relations:
                        app = self.app_dao.get_by_id(rel.application_id)
                        if app and (keyword.lower() in app.name.lower() or
                                    (app.description and keyword.lower() in app.description.lower())):
                            filtered_count += 1
                    total = filtered_count

            # 使用模型的to_dict方法转换（符合dify风格，返回小驼峰格式）
            app_list = []
            for app in applications:
                app_dict = app.to_dict()
                # 这里查询智能体的使用人数  dify表的  根据app_id获取end_users表的记录个数
                end_users_count = self.dify_service.get_end_users_by_app_id(app_dict['difyAppId'])
                app_dict['endUsersCount'] = end_users_count
                # 查询智能体的对话次数   dify数据库messages表的数据
                message_count = self.dify_service.get_message_count_by_app_id(app_dict['difyAppId'])
                app_dict['chatsCount'] = message_count
                app_list.append(app_dict)

            return app_list, total

        except Exception as e:
            logging.error(f"根据标签获取应用列表失败: {e}")
            raise

    def search_tags_with_count(self,
                               filters: Dict[str, Any],
                               limit: int = 20,
                               offset: int = 0) -> Tuple[List[AITagsVO], int]:
        """搜索标签并同时返回总数（保持与原接口兼容）"""
        try:
            # 调用DAO层搜索
            tags = self.tags_dao.search_tags(filters, limit, offset)
            total = self.tags_dao.count_tags(filters)

            # 转换为VO对象
            tag_vos = [AITagsVO.from_orm(tag) for tag in tags]

            return tag_vos, total
        except Exception as e:
            logging.error(f"搜索标签失败: {e}")
            raise

    def get_tag_statistics(self, tenant_id: str = None) -> Dict[str, Any]:
        """获取标签统计信息"""
        try:
            stats = {}

            if tenant_id:
                tags = self.tags_dao.get_by_tenant(tenant_id, limit=1000)
            else:
                tags = self.tags_dao.get_all(limit=1000)

            stats['total_tags'] = len(tags)
            stats['hot_tags'] = len([tag for tag in tags if tag.is_hot == 1])
            stats['total_usage'] = sum(tag.post_count for tag in tags)

            # 最受欢迎的标签
            popular_tags = sorted(tags, key=lambda x: x.post_count, reverse=True)[:5]
            stats['popular_tags'] = [
                {
                    'id': tag.id,
                    'name': tag.name,
                    'slug': tag.slug,
                    'post_count': tag.post_count
                }
                for tag in popular_tags
            ]

            return stats
        except Exception as e:
            logging.error(f"获取标签统计信息失败: {e}")
            return {}
