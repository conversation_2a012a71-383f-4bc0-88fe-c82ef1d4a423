"""
AI应用服务类 - 重构版本
提供AI应用的业务逻辑处理，使用DAO层进行数据访问
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from multi_agent.model.ai_application_model import (
    AIApplicationDO,
    AIApplicationVO
)
from multi_agent.dao.ai_application_dao import AIApplicationDAO


class AIApplicationService:
    """AI应用服务类，处理AI应用的业务逻辑"""

    def __init__(self):
        self.dao = AIApplicationDAO()

    def get_all_applications(self) -> List[AIApplicationVO]:
        """获取所有AI应用"""
        try:
            apps = self.dao.get_all()
            return [AIApplicationVO.from_orm(app) for app in apps]
        except Exception as e:
            logging.error(f"获取所有AI应用失败: {e}")
            raise

    def get_application_by_id(self, app_id: int) -> Optional[AIApplicationVO]:
        """根据ID获取AI应用"""
        try:
            app = self.dao.get_by_id(app_id)
            return AIApplicationVO.from_orm(app) if app else None
        except Exception as e:
            logging.error(f"获取AI应用失败, ID: {app_id}, 错误: {e}")
            raise

    def get_application_by_app_key(self, app_key: str) -> Optional[AIApplicationVO]:
        """根据app_key获取AI应用"""
        try:
            app = self.dao.get_by_app_key(app_key)
            return AIApplicationVO.from_orm(app) if app else None
        except Exception as e:
            logging.error(f"获取AI应用失败, app_key: {app_key}, 错误: {e}")
            raise

    def create_application(self, request_data: AIApplicationDO, creator: str = None) -> AIApplicationVO:
        """
        创建AI应用
        
        Args:
            request_data: 经过Pydantic验证的创建请求数据
            creator: 创建者用户名
            
        Returns:
            创建的应用响应数据
            
        Raises:
            ValueError: 当app_key已存在或数据验证失败时
            IntegrityError: 数据库约束错误
        """
        try:
            # 1. 业务逻辑验证
            existing = self.dao.get_by_app_key(request_data.app_key)
            if existing:
                raise ValueError(f"应用标识 '{request_data.app_key}' 已存在")

            # 2. 转换Pydantic模型为字典数据
            model_data = request_data.dict(exclude_unset=True)
            if creator:
                model_data['creator'] = creator

            # 3. 创建数据库记录
            app = self.dao.create(model_data)
            if not app:
                raise RuntimeError("创建应用失败")

            # 4. 返回响应模型
            return AIApplicationVO.from_orm(app)

        except ValueError:
            raise
        except Exception as e:
            logging.error(f"创建AI应用失败: {e}")
            raise

    def update_application(self, app_id: int, request_data: AIApplicationDO, updater: str = None) -> Optional[AIApplicationVO]:
        """
        更新AI应用
        
        Args:
            app_id: 应用ID
            request_data: 更新请求数据
            updater: 更新者用户名
            
        Returns:
            更新后的应用响应数据或None（如果应用不存在）
        """
        try:
            # 1. 检查应用是否存在
            app = self.dao.get_by_id(app_id)
            if not app:
                return None

            # 2. 准备更新数据（只更新提供的字段）
            update_data = request_data.dict(exclude_unset=True, exclude_none=True)
            if not update_data:
                return AIApplicationVO.from_orm(app)  # 没有需要更新的字段

            # 3. 添加审计信息
            if updater:
                update_data['updater'] = updater
            update_data['updateTime'] = datetime.now()

            # 4. 更新应用
            success = self.dao.update(app_id, update_data)
            if not success:
                return None

            # 5. 返回更新后的应用
            updated_app = self.dao.get_by_id(app_id)
            return AIApplicationVO.from_orm(updated_app) if updated_app else None

        except Exception as e:
            logging.error(f"更新AI应用失败, ID: {app_id}, 错误: {e}")
            raise

    def delete_application(self, app_id: int, deleter: str = None) -> bool:
        """
        删除AI应用（软删除）
        
        Args:
            app_id: 应用ID
            deleter: 删除者用户名
            
        Returns:
            删除成功返回True，应用不存在返回False
        """
        try:
            return self.dao.soft_delete(app_id, deleter)
        except Exception as e:
            logging.error(f"删除AI应用失败, ID: {app_id}, 错误: {e}")
            raise

    def search_applications(self,
                           keyword: Optional[str] = None,
                           app_type: Optional[int] = None,
                           status: Optional[int] = None,
                           visibility: Optional[int] = None,
                           is_public: Optional[int] = None,
                           is_featured: Optional[int] = None,
                           is_skill_center: Optional[int] = None,
                           tenant_id: Optional[int] = None,
                           company_id: Optional[int] = None,
                           limit: int = 20,
                           offset: int = 0) -> Tuple[List[AIApplicationVO], int]:
        """搜索AI应用"""
        try:
            # 构建过滤条件字典
            filters = {
                'keyword': keyword,
                'appType': app_type,
                'status': status,
                'visibility': visibility,
                'isPublic': is_public,
                'isFeatured': is_featured,
                'isSkillCenter': is_skill_center,
                'tenantId': tenant_id,
                'companyId': company_id
            }

            # 调用DAO层搜索
            applications = self.dao.search_applications(filters, limit, offset)
            total = self.dao.count_applications(filters)

            # 转换为VO对象
            app_vos = [AIApplicationVO.from_orm(app) for app in applications]

            return app_vos, total

        except Exception as e:
            logging.error(f"搜索AI应用失败: {e}")
            raise

    def get_featured_applications(self, limit: int = 10, offset: int = 0) -> List[AIApplicationVO]:
        """获取推荐应用"""
        try:
            apps = self.dao.get_featured_applications(limit, offset)
            return [AIApplicationVO.from_orm(app) for app in apps]
        except Exception as e:
            logging.error(f"获取推荐应用失败: {e}")
            raise

    def get_public_applications(self, limit: int = 20, offset: int = 0) -> List[AIApplicationVO]:
        """获取公开应用"""
        try:
            apps = self.dao.get_public_applications(limit, offset)
            return [AIApplicationVO.from_orm(app) for app in apps]
        except Exception as e:
            logging.error(f"获取公开应用失败: {e}")
            raise

    def get_applications_by_tenant(self, tenant_id: int, company_id: int = None, 
                                  limit: int = 100, offset: int = 0) -> List[AIApplicationVO]:
        """根据租户获取应用"""
        try:
            apps = self.dao.get_by_tenant_and_company(tenant_id, company_id, limit, offset)
            return [AIApplicationVO.from_orm(app) for app in apps]
        except Exception as e:
            logging.error(f"根据租户获取应用失败: {e}")
            raise

    def increment_views(self, app_id: int) -> bool:
        """增加应用浏览次数"""
        try:
            return self.dao.increment_views(app_id)
        except Exception as e:
            logging.error(f"增加应用浏览次数失败, ID: {app_id}, 错误: {e}")
            return False

    def get_application_statistics(self, tenant_id: int = None) -> Dict[str, Any]:
        """获取应用统计信息"""
        try:
            stats = {}
            
            if tenant_id:
                stats['totalApplications'] = self.dao.count_by_tenant(tenant_id)
                # 可以添加更多统计信息
                apps = self.dao.get_by_tenant_and_company(tenant_id, limit=1000)
                stats['publicApplications'] = len([app for app in apps if app.is_public == 1])
                stats['featuredApplications'] = len([app for app in apps if app.is_featured == 1])
                stats['activeApplications'] = len([app for app in apps if app.status == 1])
            else:
                # 全局统计
                all_apps = self.dao.get_all(limit=10000)  # 获取所有应用进行统计
                stats['totalApplications'] = len(all_apps)
                stats['publicApplications'] = len([app for app in all_apps if app.is_public == 1])
                stats['featuredApplications'] = len([app for app in all_apps if app.is_featured == 1])
                stats['activeApplications'] = len([app for app in all_apps if app.status == 1])
            
            return stats
        except Exception as e:
            logging.error(f"获取应用统计信息失败: {e}")
            return {}

    def batch_update_status(self, app_ids: List[int], status: int, updater: str = None) -> int:
        """批量更新应用状态"""
        try:
            success_count = 0
            update_data = {
                'status': status,
                'updateTime': datetime.now()
            }
            if updater:
                update_data['updater'] = updater
            
            for app_id in app_ids:
                if self.dao.update(app_id, update_data):
                    success_count += 1
            
            return success_count
        except Exception as e:
            logging.error(f"批量更新应用状态失败: {e}")
            return 0

    def search_applications_with_count(self,
                                      filters: Dict[str, Any],
                                      limit: int = 20,
                                      offset: int = 0) -> Tuple[List[AIApplicationVO], int]:
        """搜索应用并同时返回总数（保持与原接口兼容）"""
        try:
            # 调用DAO层搜索
            applications = self.dao.search_applications(filters, limit, offset)
            total = self.dao.count_applications(filters)

            # 转换为VO对象
            app_vos = [AIApplicationVO.from_orm(app) for app in applications]

            return app_vos, total
        except Exception as e:
            logging.error(f"搜索应用失败: {e}")
            raise

    def count_applications(self,
                          keyword: Optional[str] = None,
                          app_type: Optional[int] = None,
                          status: Optional[int] = None,
                          visibility: Optional[int] = None,
                          is_public: Optional[int] = None,
                          is_featured: Optional[int] = None,
                          is_skill_center: Optional[int] = None,
                          tenant_id: Optional[int] = None,
                          company_id: Optional[int] = None) -> int:
        """计算满足条件的AI应用数量"""
        try:
            # 构建过滤条件字典
            filters = {
                'keyword': keyword,
                'appType': app_type,
                'status': status,
                'visibility': visibility,
                'isPublic': is_public,
                'isFeatured': is_featured,
                'isSkillCenter': is_skill_center,
                'tenantId': tenant_id,
                'companyId': company_id
            }

            # 调用DAO层统计
            return self.dao.count_applications(filters)
        except Exception as e:
            logging.error(f"计算AI应用数量失败: {e}")
            return 0

    def get_skill_center_applications(self,
                                    keyword: Optional[str] = None,
                                    status: Optional[int] = None,
                                    app_type: Optional[int] = None,
                                    limit: int = 20,
                                    offset: int = 0) -> Tuple[List[AIApplicationVO], int]:
        """
        获取技能中心应用列表（带分页和总数）

        Args:
            keyword: 搜索关键词
            status: 应用状态过滤
            app_type: 应用类型过滤
            limit: 每页大小
            offset: 偏移量

        Returns:
            Tuple[List[AIApplicationVO], int]: 应用列表和总数
        """
        try:
            # 构建过滤条件，强制 isSkillCenter=1
            filters = {
                'keyword': keyword,
                'status': status,
                'appType': app_type,
                'isSkillCenter': 1  # 强制只查询技能中心应用
            }

            # 调用现有的搜索方法
            applications, total = self.search_applications_with_count(
                filters=filters,
                limit=limit,
                offset=offset
            )

            return applications, total

        except Exception as e:
            logging.error(f"获取技能中心应用失败: {e}")
            raise

    def validate_app_key(self, app_key: str, exclude_id: int = None) -> bool:
        """验证app_key是否可用"""
        try:
            existing = self.dao.get_by_app_key(app_key)
            if not existing:
                return True

            # 如果是更新操作，排除当前应用ID
            if exclude_id and existing.id == exclude_id:
                return True

            return False
        except Exception as e:
            logging.error(f"验证app_key失败: {e}")
            return False

    # 根据dify的app表的id查询我们配置的智能体信息
    def get_application_by_dify_app_id(self, dify_app_id: str) -> Optional[AIApplicationVO]:
        try:
            app = self.dao.get_by_dify_app_id(dify_app_id)
            return AIApplicationVO.from_orm(app) if app else None
        except Exception as e:
            logging.error(f"获取AI应用失败, DIFY_APP_ID: {dify_app_id}, 错误: {e}")
            raise