"""
AI应用收藏服务类 - 重构版本
提供AI应用收藏的业务逻辑处理，使用DAO层进行数据访问
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from multi_agent.model.ai_application_favorites_model import (
    AIApplicationFavoritesDO,
    AIApplicationFavoritesVO,
    AIApplicationFavoritesWithAppVO
)
from multi_agent.model.ai_application_model import AIApplicationVO, AIApplicationModel
from multi_agent.dao.ai_application_favorites_dao import AIApplicationFavoritesDAO
from multi_agent.dao.ai_application_dao import AIApplicationDAO


class AIApplicationFavoritesService:
    """AI应用收藏服务类，处理AI应用收藏的业务逻辑"""
    
    def __init__(self):
        self.favorites_dao = AIApplicationFavoritesDAO()
        self.app_dao = AIApplicationDAO()
    
    def add_favorite(self, request_data: AIApplicationFavoritesDO, creator: str = None) -> AIApplicationFavoritesVO:
        """
        添加应用收藏
        
        Args:
            request_data: 收藏请求数据
            creator: 创建者用户名
            
        Returns:
            创建的收藏记录
            
        Raises:
            ValueError: 当应用不存在或已收藏时
            IntegrityError: 数据库约束错误
        """
        try:
            # 1. 验证应用是否存在
            app = self.app_dao.get_by_id(request_data.applicationId)
            if not app:
                raise ValueError(f"应用不存在: ID={request_data.applicationId}")
            
            # 2. 检查是否已收藏
            existing = self.favorites_dao.get_by_user_app(
                request_data.userId,
                request_data.applicationId
            )
            if existing:
                # 如果已收藏，更新收藏时间
                if self.favorites_dao.update_favorite_time(request_data.userId, request_data.applicationId, creator):
                    # 重新获取更新后的记录
                    updated_favorite = self.favorites_dao.get_by_user_app(request_data.userId, request_data.applicationId)
                    return AIApplicationFavoritesVO.from_orm(updated_favorite)
                else:
                    raise RuntimeError("更新收藏时间失败")
            
            # 3. 创建收藏记录
            model_data = request_data.dict(exclude_unset=True, by_alias=True)
            if creator:
                model_data['creator'] = creator
            
            favorite = self.favorites_dao.create(model_data)
            if not favorite:
                raise RuntimeError("创建收藏记录失败")
            
            return AIApplicationFavoritesVO.from_orm(favorite)
            
        except ValueError:
            raise
        except Exception as e:
            logging.error(f"添加收藏失败: {e}")
            raise
    
    def remove_favorite(self, user_id: str, application_id: int) -> bool:
        """
        取消应用收藏
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            
        Returns:
            删除成功返回True，记录不存在返回False
        """
        try:
            return self.favorites_dao.delete(user_id, application_id)
        except Exception as e:
            logging.error(f"取消收藏失败: {e}")
            raise
    
    def get_user_favorites(self, user_id: str, limit: int = 20, offset: int = 0) -> List[AIApplicationModel]:
        """
        获取用户收藏列表（包含应用信息）
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            收藏列表（包含应用详细信息）
        """
        try:
            favorites = self.favorites_dao.get_by_user_id(user_id, limit, offset)
            
            result = []
            for favorite in favorites:
                # 获取应用信息
                app = self.app_dao.get_by_id(favorite.application_id)
                if app:
                    result.append(app)
            return result
            
        except Exception as e:
            logging.error(f"获取用户收藏列表失败: {e}")
            raise
    
    def get_device_favorites(self, device_id: str, limit: int = 20, offset: int = 0) -> List[AIApplicationFavoritesWithAppVO]:
        """
        获取设备收藏列表（包含应用信息）
        
        Args:
            device_id: 设备ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            收藏列表（包含应用详细信息）
        """
        try:
            favorites = self.favorites_dao.get_by_device_id(device_id, limit, offset)
            
            result = []
            for favorite in favorites:
                # 获取应用信息
                app = self.app_dao.get_by_id(favorite.application_id)
                if app:
                    # 构建收藏基础数据
                    favorite_data = {
                        'id': favorite.id,
                        'userId': favorite.user_id,
                        'applicationId': favorite.application_id,
                        'tenantId': favorite.tenant_id,
                        'creator': favorite.creator,
                        'createTime': favorite.create_time,
                        'updater': favorite.updater,
                        'updateTime': favorite.update_time,
                        'deviceId': getattr(favorite, 'device_id', None),
                    }
                    
                    # 使用AIApplicationVO获取应用的所有数据
                    app_vo = AIApplicationVO.from_orm(app)
                    app_data = app_vo.dict(by_alias=True)
                    
                    # 合并数据并创建VO对象
                    favorite_with_app = AIApplicationFavoritesWithAppVO(**{**favorite_data, **app_data})
                    result.append(favorite_with_app)
            
            return result
            
        except Exception as e:
            logging.error(f"获取设备收藏列表失败: {e}")
            raise
    
    def get_application_favorites(self, application_id: int, limit: int = 20, offset: int = 0) -> List[AIApplicationFavoritesVO]:
        """
        获取应用的收藏列表
        
        Args:
            application_id: 应用ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            收藏列表
        """
        try:
            favorites = self.favorites_dao.get_by_application_id(application_id, limit, offset)
            return [AIApplicationFavoritesVO.from_orm(fav) for fav in favorites]
        except Exception as e:
            logging.error(f"获取应用收藏列表失败: {e}")
            raise
    
    def search_favorites(self,
                        device_id: Optional[str] = None,
                        user_id: Optional[str] = None,
                        application_id: Optional[int] = None,
                        tenant_id: Optional[str] = None,
                        start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None,
                        limit: int = 20,
                        offset: int = 0) -> Tuple[List[AIApplicationFavoritesVO], int]:
        """
        搜索收藏记录

        Args:
            device_id: 设备ID
            user_id: 用户ID
            application_id: 应用ID
            tenant_id: 租户ID
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制数量
            offset: 偏移量

        Returns:
            (收藏列表, 总数量)
        """
        try:
            # 构建过滤条件字典
            filters = {
                'deviceId': device_id,
                'userId': user_id,
                'applicationId': application_id,
                'tenantId': tenant_id,
                'startTime': start_time,
                'endTime': end_time
            }

            # 调用DAO层搜索
            favorites = self.favorites_dao.search_favorites(filters, limit, offset)
            total = self.favorites_dao.count_favorites(filters)

            # 转换为VO对象
            favorite_vos = [AIApplicationFavoritesVO.from_orm(fav) for fav in favorites]

            return favorite_vos, total

        except Exception as e:
            logging.error(f"搜索收藏记录失败: {e}")
            raise
    
    def is_favorited(self, user_id: str, application_id: int) -> bool:
        """
        检查应用是否已被收藏
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            
        Returns:
            已收藏返回True，未收藏返回False
        """
        try:
            return self.favorites_dao.exists(user_id, application_id)
        except Exception as e:
            logging.error(f"检查收藏状态失败: {e}")
            return False
    
    def get_favorite_statistics(self, tenant_id: str = None) -> Dict[str, Any]:
        """
        获取收藏统计信息
        
        Args:
            tenant_id: 租户ID（可选）
            
        Returns:
            统计信息字典
        """
        try:
            stats = {}
            
            # 获取热门收藏应用
            popular_apps = self.favorites_dao.get_popular_applications(limit=10)
            stats['popular_applications'] = []
            
            for app_data in popular_apps:
                app = self.app_dao.get_by_id(app_data['application_id'])
                if app:
                    stats['popular_applications'].append({
                        'application_id': app.id,
                        'application_name': app.name,
                        'application_app_key': app.app_key,
                        'favorite_count': app_data['favorite_count']
                    })
            
            # 如果指定了租户，可以添加更多租户相关的统计
            if tenant_id:
                # 这里可以根据需要添加租户相关的统计逻辑
                pass
            
            return stats
            
        except Exception as e:
            logging.error(f"获取收藏统计信息失败: {e}")
            return {}
    
    def get_user_favorite_count(self, user_id: str) -> int:
        """
        获取用户收藏数量
        
        Args:
            user_id: 用户ID
            
        Returns:
            收藏数量
        """
        try:
            return self.favorites_dao.count_by_user(user_id)
        except Exception as e:
            logging.error(f"获取用户收藏数量失败: {e}")
            return 0
    
    def get_application_favorite_count(self, application_id: int) -> int:
        """
        获取应用收藏数量
        
        Args:
            application_id: 应用ID
            
        Returns:
            收藏数量
        """
        try:
            return self.favorites_dao.count_by_application(application_id)
        except Exception as e:
            logging.error(f"获取应用收藏数量失败: {e}")
            return 0
    
    def batch_remove_favorites(self, user_id: str, application_ids: List[int]) -> int:
        """
        批量取消收藏
        
        Args:
            user_id: 用户ID
            application_ids: 应用ID列表
            
        Returns:
            成功取消的数量
        """
        try:
            success_count = 0
            
            for app_id in application_ids:
                if self.favorites_dao.delete(user_id, app_id):
                    success_count += 1
            
            return success_count
            
        except Exception as e:
            logging.error(f"批量取消收藏失败: {e}")
            return 0
