"""
AI应用数据访问对象
提供ai_application表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import SQLAlchemyError
from multi_agent.model.ai_application_model import AIApplicationModel, db


class AIApplicationDAO:
    """AI应用数据访问对象"""
    
    @staticmethod
    def create(application_data: Dict[str, Any]) -> Optional[AIApplicationModel]:
        """
        创建AI应用
        
        Args:
            application_data: 应用数据字典
            
        Returns:
            创建的应用模型对象，失败返回None
        """
        try:
            application = AIApplicationModel(**application_data)
            db.session.add(application)
            db.session.commit()
            logging.info(f"成功创建AI应用: {application.id}")
            return application
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建AI应用失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(app_id: int) -> Optional[AIApplicationModel]:
        """
        根据ID获取AI应用
        
        Args:
            app_id: 应用ID
            
        Returns:
            应用模型对象，不存在返回None
        """
        try:
            return db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.id == app_id,
                    AIApplicationModel.status == 1,
                    AIApplicationModel.deleted == False,
                    AIApplicationModel.is_show == 1
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取AI应用失败: {e}")
            return None
    
    @staticmethod
    def get_by_app_key(app_key: str) -> Optional[AIApplicationModel]:
        """
        根据app_key获取AI应用
        
        Args:
            app_key: 应用标识
            
        Returns:
            应用模型对象，不存在返回None
        """
        try:
            return db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.app_key == app_key,
                    AIApplicationModel.deleted == False
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据app_key获取AI应用失败: {e}")
            return None
    
    @staticmethod
    def get_all(limit: int = 100, offset: int = 0) -> List[AIApplicationModel]:
        """
        获取所有AI应用
        
        Args:
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            应用模型对象列表
        """
        try:
            return db.session.query(AIApplicationModel).filter(
                AIApplicationModel.deleted == False,
                AIApplicationModel.status == 1,
                AIApplicationModel.is_show == 1
            ).order_by(asc(AIApplicationModel.sort)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"获取所有AI应用失败: {e}")
            return []
    
    @staticmethod
    def search_applications(filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[AIApplicationModel]:
        """
        搜索AI应用

        Args:
            filters: 过滤条件字典
            limit: 限制数量
            offset: 偏移量

        Returns:
            应用模型对象列表
        """
        try:
            query = db.session.query(AIApplicationModel).filter(
                AIApplicationModel.deleted == False,
                AIApplicationModel.status == 1,
                AIApplicationModel.is_show == 1
            )

            # 构建查询条件
            conditions = []

            # 关键词搜索
            keyword = filters.get('keyword')
            if keyword:
                conditions.append(
                    or_(
                        AIApplicationModel.name.ilike(f'%{keyword}%'),
                        AIApplicationModel.description.ilike(f'%{keyword}%')
                    )
                )

            # 精确匹配条件
            exact_match_fields = [
                'app_type', 'status', 'visibility', 'is_public',
                'is_featured', 'is_skill_center', 'tenant_id', 'company_id'
            ]

            for field in exact_match_fields:
                value = filters.get(field)
                if value is not None:
                    conditions.append(getattr(AIApplicationModel, field) == value)

            if conditions:
                query = query.filter(and_(*conditions))

            # 获取分页数据
            applications = query.order_by(asc(AIApplicationModel.sort)).offset(offset).limit(limit).all()

            return applications

        except SQLAlchemyError as e:
            logging.error(f"搜索AI应用失败: {e}")
            return []
    
    @staticmethod
    def get_by_tenant_and_company(tenant_id: int = None, company_id: int = None,
                                 limit: int = 100, offset: int = 0) -> List[AIApplicationModel]:
        """
        根据租户ID和公司ID获取AI应用
        
        Args:
            tenant_id: 租户ID
            company_id: 公司ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            应用模型对象列表
        """
        try:
            query = db.session.query(AIApplicationModel).filter(
                AIApplicationModel.deleted == False,
                AIApplicationModel.status == 1,
                AIApplicationModel.is_show == 1
            )
            
            if tenant_id is not None:
                query = query.filter(AIApplicationModel.tenant_id == tenant_id)
            if company_id is not None:
                query = query.filter(AIApplicationModel.company_id == company_id)
            
            return query.order_by(asc(AIApplicationModel.sort)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据租户和公司获取AI应用失败: {e}")
            return []
    
    @staticmethod
    def get_featured_applications(limit: int = 10, offset: int = 0) -> List[AIApplicationModel]:
        """
        获取推荐应用
        
        Args:
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            推荐应用列表
        """
        try:
            return db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.deleted == False,
                    AIApplicationModel.is_featured == 1,
                    AIApplicationModel.status == 1,
                    AIApplicationModel.is_show == 1
                )
            ).order_by(asc(AIApplicationModel.sort)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"获取推荐应用失败: {e}")
            return []
    
    @staticmethod
    def get_public_applications(limit: int = 20, offset: int = 0) -> List[AIApplicationModel]:
        """
        获取公开应用
        
        Args:
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            公开应用列表
        """
        try:
            return db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.deleted == False,
                    AIApplicationModel.is_public == 1,
                    AIApplicationModel.status == 1,
                    AIApplicationModel.is_show == 1
                )
            ).order_by(asc(AIApplicationModel.sort)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"获取公开应用失败: {e}")
            return []
    
    @staticmethod
    def update(app_id: int, update_data: Dict[str, Any]) -> bool:
        """
        更新AI应用信息
        
        Args:
            app_id: 应用ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.id == app_id,
                    AIApplicationModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新AI应用: {app_id}")
                return True
            else:
                logging.warning(f"未找到要更新的AI应用: {app_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新AI应用失败: {e}")
            return False
    
    @staticmethod
    def soft_delete(app_id: int, deleter: str = None) -> bool:
        """
        软删除AI应用
        
        Args:
            app_id: 应用ID
            deleter: 删除者
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            update_data = {
                'deleted': True,
                'update_time': datetime.now()
            }
            if deleter:
                update_data['updater'] = deleter
            
            result = db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.id == app_id,
                    AIApplicationModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功软删除AI应用: {app_id}")
                return True
            else:
                logging.warning(f"未找到要删除的AI应用: {app_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"软删除AI应用失败: {e}")
            return False
    
    @staticmethod
    def increment_views(app_id: int) -> bool:
        """
        增加应用浏览次数
        
        Args:
            app_id: 应用ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.id == app_id,
                    AIApplicationModel.deleted == False
                )
            ).update({
                'views_count': AIApplicationModel.views_count + 1
            })
            
            if result > 0:
                db.session.commit()
                return True
            return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"增加应用浏览次数失败: {e}")
            return False
    
    @staticmethod
    def count_by_tenant(tenant_id: int) -> int:
        """
        统计租户下的应用数量
        
        Args:
            tenant_id: 租户ID
            
        Returns:
            应用数量
        """
        try:
            return db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.tenant_id == tenant_id,
                    AIApplicationModel.deleted == False
                )
            ).count()
        except SQLAlchemyError as e:
            logging.error(f"统计租户应用数量失败: {e}")
            return 0

    @staticmethod
    def count_applications(filters: Dict[str, Any]) -> int:
        """
        统计满足条件的应用数量

        Args:
            filters: 过滤条件字典

        Returns:
            应用数量
        """
        try:
            query = db.session.query(AIApplicationModel).filter(
                AIApplicationModel.deleted == False
            )

            # 构建查询条件
            conditions = []

            # 关键词搜索
            keyword = filters.get('keyword')
            if keyword:
                conditions.append(
                    or_(
                        AIApplicationModel.name.ilike(f'%{keyword}%'),
                        AIApplicationModel.description.ilike(f'%{keyword}%')
                    )
                )

            # 精确匹配条件
            exact_match_fields = [
                'app_type', 'status', 'visibility', 'is_public',
                'is_featured', 'is_skill_center', 'tenant_id', 'company_id'
            ]

            for field in exact_match_fields:
                value = filters.get(field)
                if value is not None:
                    conditions.append(getattr(AIApplicationModel, field) == value)

            if conditions:
                query = query.filter(and_(*conditions))

            return query.count()

        except SQLAlchemyError as e:
            logging.error(f"统计AI应用数量失败: {e}")
            return 0

    def get_by_dify_app_id(self, dify_app_id):
        try:
            return db.session.query(AIApplicationModel).filter(
                and_(
                    AIApplicationModel.dify_app_id == dify_app_id,
                    AIApplicationModel.deleted == False
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据app_id获取AI应用失败: {e}")
            return None
