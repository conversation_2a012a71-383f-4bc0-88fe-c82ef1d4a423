"""
AI标签数据访问对象
提供ai_tags表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import SQLAlchemyError
from multi_agent.model.ai_tags_model import AITagsModel, AIApplicationTagsModel, db


class AITagsDAO:
    """AI标签数据访问对象"""
    
    @staticmethod
    def create(tag_data: Dict[str, Any]) -> Optional[AITagsModel]:
        """
        创建AI标签
        
        Args:
            tag_data: 标签数据字典
            
        Returns:
            创建的标签模型对象，失败返回None
        """
        try:
            tag = AITagsModel(**tag_data)
            db.session.add(tag)
            db.session.commit()
            logging.info(f"成功创建AI标签: {tag.id}")
            return tag
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建AI标签失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(tag_id: int) -> Optional[AITagsModel]:
        """
        根据ID获取AI标签
        
        Args:
            tag_id: 标签ID
            
        Returns:
            标签模型对象，不存在返回None
        """
        try:
            return db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.id == tag_id,
                    AITagsModel.deleted == False
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取AI标签失败: {e}")
            return None
    
    @staticmethod
    def get_by_slug(slug: str) -> Optional[AITagsModel]:
        """
        根据slug获取AI标签
        
        Args:
            slug: 标签标识
            
        Returns:
            标签模型对象，不存在返回None
        """
        try:
            return db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.slug == slug,
                    AITagsModel.deleted == False
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据slug获取AI标签失败: {e}")
            return None
    
    @staticmethod
    def get_all(limit: int = 100, offset: int = 0) -> List[AITagsModel]:
        """
        获取所有AI标签
        
        Args:
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            标签模型对象列表
        """
        try:
            return db.session.query(AITagsModel).filter(
                AITagsModel.deleted == False
            ).order_by(asc(AITagsModel.sort), desc(AITagsModel.id)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"获取所有AI标签失败: {e}")
            return []
    
    @staticmethod
    def search_tags(filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[AITagsModel]:
        """
        搜索AI标签

        Args:
            filters: 过滤条件字典
            limit: 限制数量
            offset: 偏移量

        Returns:
            标签模型对象列表
        """
        try:
            query = db.session.query(AITagsModel).filter(
                AITagsModel.deleted == False
            )

            # 构建查询条件
            conditions = []

            # 关键词搜索
            keyword = filters.get('keyword')
            if keyword:
                conditions.append(
                    or_(
                        AITagsModel.name.ilike(f'%{keyword}%'),
                        AITagsModel.description.ilike(f'%{keyword}%')
                    )
                )

            # 精确匹配条件
            exact_match_fields = ['tenant_id', 'is_hot']
            for field in exact_match_fields:
                value = filters.get(field)
                if value is not None:
                    conditions.append(getattr(AITagsModel, field) == value)

            if conditions:
                query = query.filter(and_(*conditions))

            # 获取分页数据
            tags = query.order_by(asc(AITagsModel.sort), desc(AITagsModel.id)).offset(offset).limit(limit).all()

            return tags

        except SQLAlchemyError as e:
            logging.error(f"搜索AI标签失败: {e}")
            return []

    @staticmethod
    def count_tags(filters: Dict[str, Any]) -> int:
        """
        统计满足条件的标签数量

        Args:
            filters: 过滤条件字典

        Returns:
            标签数量
        """
        try:
            query = db.session.query(AITagsModel).filter(
                AITagsModel.deleted == False
            )

            # 构建查询条件
            conditions = []

            # 关键词搜索
            keyword = filters.get('keyword')
            if keyword:
                conditions.append(
                    or_(
                        AITagsModel.name.ilike(f'%{keyword}%'),
                        AITagsModel.description.ilike(f'%{keyword}%')
                    )
                )

            # 精确匹配条件
            exact_match_fields = ['tenant_id', 'is_hot']
            for field in exact_match_fields:
                value = filters.get(field)
                if value is not None:
                    conditions.append(getattr(AITagsModel, field) == value)

            if conditions:
                query = query.filter(and_(*conditions))

            return query.count()

        except SQLAlchemyError as e:
            logging.error(f"统计AI标签数量失败: {e}")
            return 0
    
    @staticmethod
    def get_hot_tags(limit: int = 10) -> List[AITagsModel]:
        """
        获取热门标签
        
        Args:
            limit: 限制数量
            
        Returns:
            热门标签列表
        """
        try:
            return db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.deleted == False,
                    AITagsModel.is_hot == 1
                )
            ).order_by(desc(AITagsModel.post_count), asc(AITagsModel.sort)).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"获取热门标签失败: {e}")
            return []
    
    @staticmethod
    def get_by_tenant(tenant_id: str, limit: int = 100, offset: int = 0) -> List[AITagsModel]:
        """
        根据租户ID获取标签
        
        Args:
            tenant_id: 租户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            标签列表
        """
        try:
            return db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.tenant_id == tenant_id,
                    AITagsModel.deleted == False
                )
            ).order_by(asc(AITagsModel.sort), desc(AITagsModel.id)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据租户获取标签失败: {e}")
            return []
    
    @staticmethod
    def update(tag_id: int, update_data: Dict[str, Any]) -> bool:
        """
        更新AI标签信息
        
        Args:
            tag_id: 标签ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.id == tag_id,
                    AITagsModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新AI标签: {tag_id}")
                return True
            else:
                logging.warning(f"未找到要更新的AI标签: {tag_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新AI标签失败: {e}")
            return False
    
    @staticmethod
    def soft_delete(tag_id: int, deleter: str = None) -> bool:
        """
        软删除AI标签
        
        Args:
            tag_id: 标签ID
            deleter: 删除者
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            update_data = {
                'deleted': True,
                'update_time': datetime.now()
            }
            if deleter:
                update_data['updater'] = deleter
            
            result = db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.id == tag_id,
                    AITagsModel.deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功软删除AI标签: {tag_id}")
                return True
            else:
                logging.warning(f"未找到要删除的AI标签: {tag_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"软删除AI标签失败: {e}")
            return False
    
    @staticmethod
    def increment_post_count(tag_id: int) -> bool:
        """
        增加标签使用次数
        
        Args:
            tag_id: 标签ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(AITagsModel).filter(
                and_(
                    AITagsModel.id == tag_id,
                    AITagsModel.deleted == False
                )
            ).update({
                'post_count': AITagsModel.post_count + 1
            })
            
            if result > 0:
                db.session.commit()
                return True
            return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"增加标签使用次数失败: {e}")
            return False


class AIApplicationTagsDAO:
    """AI应用标签关联数据访问对象"""
    
    @staticmethod
    def create(relation_data: Dict[str, Any]) -> Optional[AIApplicationTagsModel]:
        """
        创建应用标签关联
        
        Args:
            relation_data: 关联数据字典
            
        Returns:
            创建的关联模型对象，失败返回None
        """
        try:
            relation = AIApplicationTagsModel(**relation_data)
            db.session.add(relation)
            db.session.commit()
            logging.info(f"成功创建应用标签关联: app_id={relation.application_id}, tag_id={relation.tag_id}")
            return relation
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建应用标签关联失败: {e}")
            return None
    
    @staticmethod
    def get_by_application_id(application_id: int) -> List[AIApplicationTagsModel]:
        """
        根据应用ID获取标签关联
        
        Args:
            application_id: 应用ID
            
        Returns:
            关联列表
        """
        try:
            return db.session.query(AIApplicationTagsModel).filter(
                and_(
                    AIApplicationTagsModel.application_id == application_id,
                    AIApplicationTagsModel.deleted == False
                )
            ).all()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取标签关联失败: {e}")
            return []
    
    @staticmethod
    def get_by_tag_id(tag_id: int) -> List[AIApplicationTagsModel]:
        """
        根据标签ID获取应用关联
        
        Args:
            tag_id: 标签ID
            
        Returns:
            关联列表
        """
        try:
            return db.session.query(AIApplicationTagsModel).filter(
                and_(
                    AIApplicationTagsModel.tag_id == tag_id,
                    AIApplicationTagsModel.deleted == False
                )
            ).all()
        except SQLAlchemyError as e:
            logging.error(f"根据标签ID获取应用关联失败: {e}")
            return []
    
    @staticmethod
    def delete_by_application_and_tag(application_id: int, tag_id: int) -> bool:
        """
        删除应用标签关联
        
        Args:
            application_id: 应用ID
            tag_id: 标签ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(AIApplicationTagsModel).filter(
                and_(
                    AIApplicationTagsModel.application_id == application_id,
                    AIApplicationTagsModel.tag_id == tag_id,
                    AIApplicationTagsModel.deleted == False
                )
            ).update({'deleted': True, 'update_time': datetime.now()})
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功删除应用标签关联: app_id={application_id}, tag_id={tag_id}")
                return True
            return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"删除应用标签关联失败: {e}")
            return False
    
    @staticmethod
    def exists(application_id: int, tag_id: int) -> bool:
        """
        检查应用标签关联是否存在
        
        Args:
            application_id: 应用ID
            tag_id: 标签ID
            
        Returns:
            存在返回True，不存在返回False
        """
        try:
            return db.session.query(AIApplicationTagsModel).filter(
                and_(
                    AIApplicationTagsModel.application_id == application_id,
                    AIApplicationTagsModel.tag_id == tag_id,
                    AIApplicationTagsModel.deleted == False
                )
            ).first() is not None
        except SQLAlchemyError as e:
            logging.error(f"检查应用标签关联失败: {e}")
            return False
