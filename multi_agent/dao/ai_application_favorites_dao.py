"""
AI应用收藏数据访问对象
提供ai_application_favorites表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import SQLAlchemyError
from multi_agent.model.ai_application_favorites_model import AIApplicationFavoritesModel, db


class AIApplicationFavoritesDAO:
    """AI应用收藏数据访问对象"""
    
    @staticmethod
    def create(favorite_data: Dict[str, Any]) -> Optional[AIApplicationFavoritesModel]:
        """
        创建应用收藏
        
        Args:
            favorite_data: 收藏数据字典
            
        Returns:
            创建的收藏模型对象，失败返回None
        """
        try:
            favorite = AIApplicationFavoritesModel(**favorite_data)
            db.session.add(favorite)
            db.session.commit()
            logging.info(f"成功创建应用收藏: device_id={favorite.device_id}, app_id={favorite.application_id}")
            return favorite
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建应用收藏失败: {e}")
            return None
    
    @staticmethod
    def get_by_user_app(user_id: str, application_id: int) -> Optional[AIApplicationFavoritesModel]:
        """
        根据用户ID和应用ID获取收藏记录
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            
        Returns:
            收藏模型对象，不存在返回None
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                and_(
                    AIApplicationFavoritesModel.user_id == user_id,
                    AIApplicationFavoritesModel.application_id == application_id
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据用户应用获取收藏记录失败: {e}")
            return None
    
    @staticmethod
    def get_by_device_id(device_id: str, limit: int = 100, offset: int = 0) -> List[AIApplicationFavoritesModel]:
        """
        根据设备ID获取收藏列表
        
        Args:
            device_id: 设备ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            收藏模型对象列表
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                AIApplicationFavoritesModel.device_id == device_id
            ).order_by(desc(AIApplicationFavoritesModel.create_time)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据设备ID获取收藏列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_user_id(user_id: str, limit: int = 100, offset: int = 0) -> List[AIApplicationFavoritesModel]:
        """
        根据用户ID获取收藏列表
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            收藏模型对象列表
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                AIApplicationFavoritesModel.user_id == user_id
            ).order_by(desc(AIApplicationFavoritesModel.update_time)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据用户ID获取收藏列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_application_id(application_id: int, limit: int = 100, offset: int = 0) -> List[AIApplicationFavoritesModel]:
        """
        根据应用ID获取收藏列表
        
        Args:
            application_id: 应用ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            收藏模型对象列表
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                AIApplicationFavoritesModel.application_id == application_id
            ).order_by(desc(AIApplicationFavoritesModel.create_time)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取收藏列表失败: {e}")
            return []
    
    @staticmethod
    def search_favorites(filters: Dict[str, Any], limit: int = 20, offset: int = 0) -> List[AIApplicationFavoritesModel]:
        """
        搜索应用收藏

        Args:
            filters: 过滤条件字典
            limit: 限制数量
            offset: 偏移量

        Returns:
            收藏模型对象列表
        """
        try:
            query = db.session.query(AIApplicationFavoritesModel)

            # 构建查询条件
            conditions = []

            # 精确匹配条件
            exact_match_fields = ['device_id', 'user_id', 'application_id', 'tenant_id']
            field_mapping = {
                'deviceId': 'device_id',
                'userId': 'user_id', 
                'applicationId': 'application_id',
                'tenantId': 'tenant_id'
            }
            for camel_field, snake_field in field_mapping.items():
                value = filters.get(camel_field)
                if value is not None:
                    conditions.append(getattr(AIApplicationFavoritesModel, snake_field) == value)

            # 时间范围查询
            start_time = filters.get('start_time')
            end_time = filters.get('end_time')
            if start_time:
                conditions.append(AIApplicationFavoritesModel.create_time >= start_time)
            if end_time:
                conditions.append(AIApplicationFavoritesModel.create_time <= end_time)

            if conditions:
                query = query.filter(and_(*conditions))

            # 获取分页数据
            favorites = query.order_by(desc(AIApplicationFavoritesModel.create_time)).offset(offset).limit(limit).all()

            return favorites

        except SQLAlchemyError as e:
            logging.error(f"搜索应用收藏失败: {e}")
            return []

    @staticmethod
    def count_favorites(filters: Dict[str, Any]) -> int:
        """
        统计满足条件的收藏数量

        Args:
            filters: 过滤条件字典

        Returns:
            收藏数量
        """
        try:
            query = db.session.query(AIApplicationFavoritesModel)

            # 构建查询条件
            conditions = []

            # 精确匹配条件
            exact_match_fields = ['device_id', 'user_id', 'application_id', 'tenant_id']
            field_mapping = {
                'deviceId': 'device_id',
                'userId': 'user_id', 
                'applicationId': 'application_id',
                'tenantId': 'tenant_id'
            }
            for camel_field, snake_field in field_mapping.items():
                value = filters.get(camel_field)
                if value is not None:
                    conditions.append(getattr(AIApplicationFavoritesModel, snake_field) == value)

            # 时间范围查询
            start_time = filters.get('start_time')
            end_time = filters.get('end_time')
            if start_time:
                conditions.append(AIApplicationFavoritesModel.create_time >= start_time)
            if end_time:
                conditions.append(AIApplicationFavoritesModel.create_time <= end_time)

            if conditions:
                query = query.filter(and_(*conditions))

            return query.count()

        except SQLAlchemyError as e:
            logging.error(f"统计应用收藏数量失败: {e}")
            return 0
    
    @staticmethod
    def update_favorite_time(user_id: str, application_id: int, updater: str = None) -> bool:
        """
        更新应用收藏时间
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            updater: 更新者
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            update_data = {'update_time': datetime.now()}
            if updater:
                update_data['updater'] = updater
                
            result = db.session.query(AIApplicationFavoritesModel).filter(
                and_(
                    AIApplicationFavoritesModel.user_id == user_id,
                    AIApplicationFavoritesModel.application_id == application_id
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新应用收藏时间: user_id={user_id}, app_id={application_id}")
                return True
            else:
                logging.warning(f"未找到要更新的应用收藏: user_id={user_id}, app_id={application_id}")
                return False
                
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新应用收藏时间失败: {e}")
            return False
    
    @staticmethod
    def delete(user_id: str, application_id: int) -> bool:
        """
        删除应用收藏
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(AIApplicationFavoritesModel).filter(
                and_(
                    AIApplicationFavoritesModel.user_id == user_id,
                    AIApplicationFavoritesModel.application_id == application_id
                )
            ).delete()
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功删除应用收藏: user_id={user_id}, app_id={application_id}")
                return True
            else:
                logging.warning(f"未找到要删除的应用收藏: user_id={user_id}, app_id={application_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"删除应用收藏失败: {e}")
            return False
    
    @staticmethod
    def exists(user_id: str, application_id: int) -> bool:
        """
        检查应用收藏是否存在
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            
        Returns:
            存在返回True，不存在返回False
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                and_(
                    AIApplicationFavoritesModel.user_id == user_id,
                    AIApplicationFavoritesModel.application_id == application_id
                )
            ).first() is not None
        except SQLAlchemyError as e:
            logging.error(f"检查应用收藏是否存在失败: {e}")
            return False
    
    @staticmethod
    def count_by_application(application_id: int) -> int:
        """
        统计应用的收藏数量
        
        Args:
            application_id: 应用ID
            
        Returns:
            收藏数量
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                AIApplicationFavoritesModel.application_id == application_id
            ).count()
        except SQLAlchemyError as e:
            logging.error(f"统计应用收藏数量失败: {e}")
            return 0
    
    @staticmethod
    def count_by_user(user_id: str) -> int:
        """
        统计用户的收藏数量
        
        Args:
            user_id: 用户ID
            
        Returns:
            收藏数量
        """
        try:
            return db.session.query(AIApplicationFavoritesModel).filter(
                AIApplicationFavoritesModel.user_id == user_id
            ).count()
        except SQLAlchemyError as e:
            logging.error(f"统计用户收藏数量失败: {e}")
            return 0
    
    @staticmethod
    def get_popular_applications(limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取热门收藏应用
        
        Args:
            limit: 限制数量
            
        Returns:
            热门应用列表，包含应用ID和收藏数量
        """
        try:
            from sqlalchemy import func
            
            result = db.session.query(
                AIApplicationFavoritesModel.application_id,
                func.count(AIApplicationFavoritesModel.application_id).label('favorite_count')
            ).group_by(
                AIApplicationFavoritesModel.application_id
            ).order_by(
                desc('favorite_count')
            ).limit(limit).all()
            
            return [
                {
                    'application_id': row.application_id,
                    'favorite_count': row.favorite_count
                }
                for row in result
            ]
        except SQLAlchemyError as e:
            logging.error(f"获取热门收藏应用失败: {e}")
            return []
