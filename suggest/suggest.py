import json
import logging

from json_repair import repair_json

from utils.dify_client import get_dify_his_messages
from utils.knowledge_base import extract_answers_and_last_his_messages
from utils.knowledge_base import search_knowledge_base
from utils.volcengine_llm import ask_volcengine_llm


def suggest_question(jsonObj):
    print(f"入参为：%s", json.dumps(jsonObj, ensure_ascii=False))
    user = jsonObj.get("user", '')
    Authorization = jsonObj.get("Authorization", '')
    conversation_id = jsonObj.get("conversation_id", '')
    mobile = jsonObj.get("mobile", False)
    # 调用difyApi接口获取历史消息
    his_list = get_dify_his_messages(user, conversation_id, Authorization)
    # 模拟difyApi上下文信息
    # his_list = mock_get_dify_his_messages(user, conversation_id, Authorization)
    # 提取历史消息
    contextList = extract_answers_and_last_his_messages(his_list)
    logging.info("Context: %s", json.dumps(contextList, ensure_ascii=False))
    keywords = ["点击下方的按钮完成一下认证"]
    has_match = any(any(keyword in item["answer"] for keyword in keywords) for item in contextList)
    if not mobile and has_match:
        print("有认证问题，请完成认证")
        return [{"text":"点击这里完成认证", "action":"authorization"}]
    # 1. 检索本地知识库
    kb_info = search_knowledge_base()
    # kb_info = get_database_knowledge_base(sys.argv[1])



    format_instructions = (
        "# Role: 对话引导专家 \r\n"+
"## Profile \r\n"+
"- language: 中文 \r\n"+
"- description: 专业的对话分析与引导专家，擅长从对话上下文中提取关键信息并生成引导性问题 \r\n"+
"- background: 具备自然语言处理和信息检索背景，专注于对话系统与用户交互优化 \r\n"+
"- personality: 精确、高效、逻辑性强，注重问题质量与引导效果 \r\n"+
"- expertise: 对话分析、语义理解、问题优化、信息检索 \r\n"+
"- target_audience: 对话系统开发人员、客服系统运营者、智能交互设计师 \r\n"+

"## Skills \r\n"+

"1. 核心分析能力 \r\n"+
        "   - 语义分类: 能够准确分析上下文语义，并将其归入预定义的类别标签 \r\n"+
        "   - 问题筛选: 从类别中选取信息互补、引导性强的问题 \r\n"+
        "   - 问题优化: 能够优化问题表述，保持核心语义的同时提高表达效率 \r\n"+
        "  - 联想生成: 在无法归类时，能够生成与当前话题相关的探索性问题 \r\n"+

"2. 辅助技术能力 \r\n"+
        "   - 信息压缩: 在保留核心信息的前提下，将问题压缩至20字以内 \r\n"+
        "   - 术语保留: 优化问题时优先保留行业术语，确保专业性 \r\n"+
        "   - 维度区分: 确保生成问题在时间、政策、流程、主体等维度上具有差异性 \r\n"+
        "   - 重复检测: 避免生成语义重复的问题，提供新的探索角度 \r\n"+

"## Rules \r\n"+

"1. 基本原则： \r\n"+
        "   - 语义一致性: 优化问题必须保持原始语义不变，仅压缩表达方式 \r\n"+
        "   - 维度差异化: 生成的问题必须在不同维度上有所区分，避免重复 \r\n"+
        "   - 引导性优先: 问题应具备延伸性或探索性，引导用户进一步提问 \r\n"+
        "   - 精确表达: 禁止使用模糊措辞、反问或语气词，确保问题清晰明确 \r\n"+

"2. 行为准则： \r\n"+
        "   - 类别匹配: 成功归类时，必须从该类别中选取问题，不可混入生成问题 \r\n"+
        "   - 积极表达: 不得出现'无法判断''暂时没有'等消极措辞 \r\n"+
        "   - 历史避免: 生成的问题不得重复系统历史输出，需提供新的探索角度 \r\n"+
        "   - 澄清优化: 面对模糊问题时，需围绕主体构建具象、可拆解的问题 \r\n"+

"3. 限制条件： \r\n"+
    "   - 长度限制: 每个问题不超过20个中文字符 \r\n"+
    "   - 结构限制: 输出必须为JSON数组格式，如['问题1', '问题2', '问题3'] \r\n"+
    "   - 术语要求: 优化问题时优先保留行业术语，压缩句式但避免模糊化 \r\n"+
    "   - 维度要求: 生成问题必须突出维度差异，如时间、政策、流程、主体等 \r\n"+

"## Workflows \r\n"+

"- 目标: 从对话上下文中提取关键信息并生成3个引导性问题 \r\n"+
"- 步骤 1: 语义分类，分析上下文语义，尝试归入本地问题库中的类别标签 \r\n"+
"- 步骤 2: 若归类成功，从该类别中选取3个信息互补、引导性强的问题，优化表述但保持核心语义 \r\n"+
"- 步骤 3: 若未能归类，则根据对话内容生成3个与当前话题最相关、最可能引导用户深入提问的问题 \r\n"+
"- 预期结果: 返回JSON数组格式的3个优化后问题，每个问题不超过20字，且在语义和结构上避免互相重复 \r\n"+

"## Initialization \r\n"+
"作为对话引导专家，你必须遵守上述Rules，按照Workflows执行任务。"
    )

    prompt = (
        f"已知上下文信息：\n{json.dumps(contextList, ensure_ascii=False)}\n"
        # f"已知问题库列表：{json.dumps(kb_info, ensure_ascii=False)}\n"
        f"f任务要求：\n{format_instructions}"
    )

    logging.info("Prompt: %s", prompt)
    # 3. 调用火山大模型
    suggestion = ask_volcengine_llm(prompt)
    # 4. 解析数据
    result = []
    json_string = repair_json(suggestion, ensure_ascii=False)
    logging.info("json_string: %s", json.dumps(json_string, ensure_ascii=False))
    for i in json.loads(json_string):
        suggestion_dict = {
            "text": i,
            "action": ""
        }
        result.append(suggestion_dict)
        
    return result