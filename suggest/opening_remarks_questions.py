from utils import database_sql, knowledge_base
import json

def get_opening_remarks_questions_dict(env, type):
    select_sql = database_sql.opening_remarks_questions + "'" + type + "'"
    # select_sql = database_sql.opening_remarks_questions +"where"+ type
    data_json = knowledge_base.get_database_knowledge_base(env, select_sql)
    if not data_json:
        return {"suggested_questions": []}
    # data_dict = json.loads(data_json)
    # context_list = [item['text'] for item in data_json]
    return {"suggested_questions": data_json}
