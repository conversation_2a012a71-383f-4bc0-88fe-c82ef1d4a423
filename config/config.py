# encoding:utf-8

import copy
import json
import logging
import os
import pickle


# 将所有可用的配置项写在字典里, 请使用小写字母
# 此处的配置值无实际意义，程序不会读取此处的配置，仅用于提示格式，请将配置加入到config.json中


class Config(dict):
    def __init__(self, d=None):
        super().__init__()
        if d is None:
            d = {}
        for k, v in d.items():
            self[k] = v
        # user_datas: 用户数据，key为用户名，value为用户数据，也是dict
        self.user_datas = {}

    def __getitem__(self, key):

        return super().__getitem__(key)

    def __setitem__(self, key, value):

        return super().__setitem__(key, value)

    def get(self, key, default=None):
        try:
            return self[key]
        except KeyError as e:
            return default
        except Exception as e:
            raise e

    def set(self, key, value):
        try:
            self[key] = value
        except Exception as e:
            raise e

    # Make sure to return a dictionary to ensure atomic
    def get_user_data(self, user) -> dict:
        if self.user_datas.get(user) is None:
            self.user_datas[user] = {}
        return self.user_datas[user]

    def load_user_datas(self):
        try:
            with open(os.path.join(get_appdata_dir(), "user_datas.pkl"), "rb") as f:
                self.user_datas = pickle.load(f)
                logging.info("[Config] User datas loaded.")
        except FileNotFoundError as e:
            logging.info("[Config] User datas file not found, ignore.")
        except Exception as e:
            logging.info("[Config] User datas error: {}".format(e))
            self.user_datas = {}

    def save_user_datas(self):
        try:
            with open(os.path.join(get_appdata_dir(), "user_datas.pkl"), "wb") as f:
                pickle.dump(self.user_datas, f)
                logging.info("[Config] User datas saved.")
        except Exception as e:
            logging.info("[Config] User datas error: {}".format(e))


config = Config()


def drag_sensitive(config):
    try:
        if isinstance(config, str):
            conf_dict: dict = json.loads(config)
            conf_dict_copy = copy.deepcopy(conf_dict)
            for key in conf_dict_copy:
                if "key" in key or "secret" in key:
                    if isinstance(conf_dict_copy[key], str):
                        conf_dict_copy[key] = conf_dict_copy[key][0:3] + "*" * 5 + conf_dict_copy[key][-3:]
            return json.dumps(conf_dict_copy, indent=4)

        elif isinstance(config, dict):
            config_copy = copy.deepcopy(config)
            for key in config:
                if "key" in key or "secret" in key:
                    if isinstance(config_copy[key], str):
                        config_copy[key] = config_copy[key][0:3] + "*" * 5 + config_copy[key][-3:]
            return config_copy
    except Exception as e:
        logging.exception(e)
        return config
    return config


def load_config(env):
    global config
    config_path = f"config/config.{env}.json"
    if not os.path.exists(config_path):
        logging.error(f"配置文件不存在: {config_path}")
        return False

    try:
        config_str = read_file(config_path)
        logging.debug("[INIT] config str: {}".format(drag_sensitive(config_str)))

        # 将json字符串反序列化为dict类型
        config = Config(json.loads(config_str))

        # 设置日志级别
        if config.get("debug", False):
            logging.debug("[INIT] set log level to DEBUG")

        logging.info("[INIT] load config: {}".format(drag_sensitive(config)))

        config.load_user_datas()
        return True

    except Exception as e:
        logging.error(f"配置加载失败: {e}")
        return False


def save_config():
    global config
    config_path = "./config.json"
    try:
        config_dict = dict(config)  # 将Config对象转换为普通字典
        # 创建一个按键排序的有序字典
        sorted_config = {key: config_dict[key] for key in sorted(config_dict.keys())}
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(sorted_config, f, indent=4, ensure_ascii=False)
            logging.info("[Config] Configuration saved.")
    except Exception as e:
        logging.error(f"[Config] Save configuration error: {e}")


def get_root():
    return os.path.dirname(os.path.abspath(__file__))


def read_file(path):
    with open(path, mode="r", encoding="utf-8") as f:
        return f.read()


def conf():
    return config


def get_appdata_dir():
    data_path = os.path.join(get_root(), conf().get("appdata_dir", ""))
    if not os.path.exists(data_path):
        logging.info("[INIT] data path not exists, create it: {}".format(data_path))
        os.makedirs(data_path)
    return data_path


def subscribe_msg():
    trigger_prefix = conf().get("single_chat_prefix", [""])[0]
    msg = conf().get("subscribe_msg", "")
    return msg.format(trigger_prefix=trigger_prefix)


# global plugin config
plugin_config = {}


def write_plugin_config(pconf: dict):
    """
    写入插件全局配置
    :param pconf: 全量插件配置
    """
    global plugin_config
    for k in pconf:
        plugin_config[k.lower()] = pconf[k]


def remove_plugin_config(name: str):
    """
    移除待重新加载的插件全局配置
    :param name: 待重载的插件名
    """
    global plugin_config
    plugin_config.pop(name.lower(), None)


def pconf(plugin_name: str) -> dict:
    """
    根据插件名称获取配置
    :param plugin_name: 插件名称
    :return: 该插件的配置项
    """
    # 如果插件名称作为key获取不到，则尝试使用小写名称
    return plugin_config.get(plugin_name) or plugin_config.get(plugin_name.lower())


# 全局配置，用于存放全局生效的状态
global_config = {"admin_users": []}
