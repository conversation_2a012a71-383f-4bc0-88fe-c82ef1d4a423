#!/usr/bin/env python
"""Test script to check if knowledge_spaces imports work"""
import sys
import traceback

try:
    print("Testing knowledge_spaces import...")
    from knowledge_spaces import knowledge_spaces_bp, source_files_bp, processing_tasks_bp
    print("✓ Knowledge spaces import successful")
    print("✓ All blueprints imported successfully")
    
    # Test creating app
    print("Testing app creation...")
    from app import create_app
    app = create_app()
    print("✓ App creation successful")
    
except Exception as e:
    print(f"✗ Error: {e}")
    print("Full traceback:")
    traceback.print_exc()