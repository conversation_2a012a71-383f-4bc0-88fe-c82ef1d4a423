# 双数据源架构说明

本项目采用双数据源架构，实现了数据库的清晰分离：

- **主应用** → MySQL数据库
- **Dify功能** → PostgreSQL数据库

## 架构设计

### 数据源分离

```
┌─────────────────────────────────────────────────────────────┐
│                      Flask应用                              │
├─────────────────────────────────────────────────────────────┤
│  主应用模块                    │  Dify模块                   │
│  ├─ multi_agent/              │  ├─ dify/controller/        │
│  ├─ app/suggest/              │  ├─ dify/service/           │
│  ├─ app/retrieval/            │  ├─ dify/dao/               │
│  ├─ app/sensitive_words/      │  └─ dify/model/             │
│  └─ ...                       │                             │
├─────────────────────────────────────────────────────────────┤
│  MySQL数据库                   │  PostgreSQL数据库           │
│  ├─ ai_applications           │  ├─ end_users               │
│  ├─ ai_tags                   │  ├─ conversations           │
│  ├─ ai_favorites              │  └─ messages                │
│  └─ ...                       │                             │
└─────────────────────────────────────────────────────────────┘
```

### 配置格式

**config/config.dev.json**
```json
{
  "env": "dev",
  "debug": true,
  "datasources": {
    "main": {
      "host": "********",
      "user": "root",
      "password": "123456",
      "database": "ai-application",
      "database_type": "mysql",
      "description": "主数据库，用于主应用功能"
    },
    "dify": {
      "host": "**************",
      "port": 5444,
      "user": "postgres",
      "password": "difyai123456",
      "database": "dify",
      "database_type": "postgresql",
      "description": "Dify数据库，用于对话和消息数据"
    }
  }
}
```

**config/config.prod.json**
```json
{
  "env": "prod",
  "debug": false,
  "datasources": {
    "main": {
      "host": "rm-2zem3pqxjm6n0xu1u.mysql.rds.aliyuncs.com",
      "user": "fbs",
      "password": "sYTUmwNEBAYP@G3CVTAUCykU7FjkwY",
      "database": "ai-application",
      "database_type": "mysql",
      "description": "主数据库，用于主应用功能"
    },
    "dify": {
      "host": "**************",
      "port": 5444,
      "user": "postgres",
      "password": "difyai123456",
      "database": "dify",
      "database_type": "postgresql",
      "description": "Dify数据库，用于对话和消息数据"
    }
  }
}
```

## 核心组件

### 1. 主数据库连接 (MySQL)

**位置**: `multi_agent.model.ai_application_model.db`

**用途**: 
- 主应用的所有功能
- AI应用管理
- 用户标签
- 收藏夹
- 敏感词过滤
- 其他业务数据

### 2. Dify数据库连接 (PostgreSQL)

**位置**: `dify.database.dify_db`

**用途**:
- Dify对话数据
- 用户会话记录
- 消息历史
- Dify相关的所有数据

### 3. 数据库初始化

**主数据库初始化** (`app/__init__.py`):
```python
from multi_agent.model.ai_application_model import db
db.init_app(app)
```

**Dify数据库初始化** (`app/__init__.py`):
```python
# 在create_app函数中直接调用
init_dify_database(app)
```

## 使用方法

### 1. 启动应用

```bash
# 开发环境
python run.py dev

# 生产环境  
python run.py prod
```

### 2. 主应用API

所有非dify的API继续使用MySQL数据库：

```bash
# AI应用管理
GET /multiagent/application/list

# 标签管理
GET /multiagent/tags/list

# 收藏夹
GET /multiagent/favorites/list

# 敏感词过滤
POST /ai-answer/sensitive-words/filter
```

### 3. Dify API

所有dify相关的API自动使用PostgreSQL数据库：

```bash
# 用户对话查询
GET /api/dify/users/{user_id}/conversations

# 对话消息查询
GET /api/dify/conversations/{conversation_id}/messages

# 对话搜索
POST /api/dify/conversations/search

# 用户统计
GET /api/dify/users/{user_id}/statistics
```

## 开发指南

### 1. 主应用开发

使用标准的SQLAlchemy模型：

```python
from multi_agent.model.ai_application_model import db

class MyModel(db.Model):
    __tablename__ = 'my_table'
    id = db.Column(db.Integer, primary_key=True)
    
# 使用
db.session.add(instance)
db.session.commit()
```

### 2. Dify功能开发

使用dify专用的数据库连接：

```python
from dify.model.dify_models import dify_db

class DifyModel(dify_db.Model):
    __tablename__ = 'dify_table'
    __bind_key__ = 'dify'
    id = dify_db.Column(dify_db.String, primary_key=True)
    
# 使用
dify_db.session.add(instance)
dify_db.session.commit()
```

### 3. 模型定义规范

**主应用模型**:
- 继承 `db.Model`
- 使用 `db.session`
- 放在对应的模块目录下

**Dify模型**:
- 继承 `dify_db.Model`
- 添加 `__bind_key__ = 'dify'`
- 使用 `dify_db.session`
- 放在 `dify/model/` 目录下

## 测试验证

运行测试脚本验证双数据源功能：

```bash
python test_dual_datasource.py
```

测试内容包括：
- 配置加载测试
- 双数据库连接测试
- Dify模型测试
- API端点测试

## 数据库管理

### 1. 主数据库（MySQL）

```bash
# 连接主数据库
mysql -h ******** -u root -p ai-application

# 查看表
SHOW TABLES;
```

### 2. Dify数据库（PostgreSQL）

```bash
# 连接dify数据库
psql -h ************** -p 5444 -U postgres -d dify

# 查看表
\dt
```

## 迁移指南

### 从单数据源迁移

1. **备份现有配置**
2. **更新配置文件**，添加 `dify_database` 配置
3. **安装PostgreSQL依赖**：`pip install psycopg2-binary`
4. **运行测试脚本**验证配置
5. **启动应用**

### 数据迁移

如果需要将现有的dify数据从MySQL迁移到PostgreSQL：

1. 导出MySQL中的dify相关表
2. 转换数据格式（MySQL → PostgreSQL）
3. 导入到PostgreSQL数据库
4. 验证数据完整性

## 注意事项

### 1. 事务管理

- 主数据库和Dify数据库的事务是独立的
- 跨数据库操作需要特别处理
- 建议避免跨数据库的复杂事务

### 2. 连接池

- 每个数据库都有独立的连接池
- 配置合适的连接池大小
- 监控连接池使用情况

### 3. 性能考虑

- 避免跨数据库的JOIN操作
- 合理设计API，减少数据库查询次数
- 使用适当的索引优化查询

### 4. 备份策略

- MySQL和PostgreSQL需要分别备份
- 制定合适的备份计划
- 定期测试恢复流程

## 故障排除

### 常见问题

1. **主数据库连接失败**
   - 检查MySQL服务状态
   - 验证连接参数
   - 检查网络连通性

2. **Dify数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证dify_database配置
   - 确认psycopg2-binary已安装

3. **模型导入错误**
   - 检查模型定义是否正确
   - 确认使用了正确的db实例
   - 验证__bind_key__设置

### 日志查看

应用启动时会输出数据库连接信息：

```
INFO - 数据库连接配置: mysql+pymysql://root:***@********/ai-application
INFO - Dify数据库连接配置: postgresql+psycopg2://postgres:***@**************:5444/dify
```

## 更新日志

- **v1.0** - 实现双数据源架构
- **v1.1** - 完善错误处理和日志记录
- **v1.2** - 添加测试脚本和文档
