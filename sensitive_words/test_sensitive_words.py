#!/usr/bin/env python3
# coding=utf-8
"""
敏感词过滤功能测试脚本
"""
import os
import sys
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from sensitive_words.sensitive_words_service import SensitiveWordsService, check_sensitive_words_comprehensive
from sensitive_words.custom_sensitive_words import D<PERSON>Filter


def test_dfa_filter():
    """测试DFA过滤器"""
    print("=" * 50)
    print("测试DFA过滤器")
    print("=" * 50)
    
    # 创建DFA过滤器
    dfa_filter = DFAFilter()
    
    # 添加测试敏感词
    test_words = ["测试敏感词", "违法", "暴力", "色情", "赌博"]
    dfa_filter.build_keywords(test_words)
    
    # 测试文本
    test_texts = [
        "这是一个正常的文本",
        "这里包含测试敏感词",
        "违法行为是不被允许的",
        "暴力和色情内容都不合适",
        "网络赌博害人不浅",
        "这个文本包含多个违法和暴力词汇"
    ]
    
    for text in test_texts:
        has_sensitive = dfa_filter.has_sensitive_word(text)
        sensitive_words = dfa_filter.get_sensitive_words(text)
        filtered_text = dfa_filter.filter_message(text)
        
        print(f"原文: {text}")
        print(f"包含敏感词: {has_sensitive}")
        print(f"敏感词列表: {sensitive_words}")
        print(f"过滤后: {filtered_text}")
        print("-" * 30)


def test_file_loading():
    """测试从file/vocabulary目录加载敏感词"""
    print("=" * 50)
    print("测试从file/vocabulary目录加载敏感词文件")
    print("=" * 50)

    dfa_filter = DFAFilter()

    # 从file/vocabulary目录加载所有敏感词文件
    vocabulary_dir = os.path.join(current_dir, 'file', 'vocabulary')

    if os.path.exists(vocabulary_dir) and os.path.isdir(vocabulary_dir):
        loaded_files = []
        total_words = 0

        # 遍历vocabulary目录下的所有txt文件
        for filename in os.listdir(vocabulary_dir):
            if filename.endswith('.txt'):
                file_path = os.path.join(vocabulary_dir, filename)
                try:
                    # 统计文件中的词汇数量
                    with open(file_path, 'r', encoding='utf-8') as f:
                        word_count = sum(1 for line in f if line.strip())

                    # 加载敏感词文件
                    dfa_filter.parse_file(file_path)
                    loaded_files.append(filename)
                    total_words += word_count
                    print(f"成功加载: {filename} ({word_count} 个词汇)")
                except Exception as e:
                    print(f"加载失败 {filename}: {e}")

        if loaded_files:
            print(f"\n总共加载了 {len(loaded_files)} 个文件，包含 {total_words} 个敏感词")
        else:
            print("没有找到有效的敏感词文件")
            return
        # 测试一些可能的敏感词
        test_texts = [
            "这是正常文本",
            "法轮功相关内容",
            "共产党万岁",
            "台湾独立",
            "这里有色情内容",
            "暴力恐怖主义",
            "贪污腐败官员"
        ]

        print(f"\n开始测试敏感词检测:")
        for text in test_texts:
            has_sensitive = dfa_filter.has_sensitive_word(text)
            sensitive_words = dfa_filter.get_sensitive_words(text)
            filtered_text = dfa_filter.filter_message(text)

            print(f"原文: {text}")
            print(f"包含敏感词: {has_sensitive}")
            if sensitive_words:
                print(f"敏感词: {sensitive_words}")
            print(f"过滤后: {filtered_text}")
            print("-" * 30)
    else:
        print(f"未找到file/vocabulary目录: {vocabulary_dir}")


def test_comprehensive_service():
    """测试综合敏感词服务"""
    print("=" * 50)
    print("测试综合敏感词服务")
    print("=" * 50)
    
    # 测试文本
    test_texts = [
        "这是一个正常的测试文本",
        "这里包含一些可能的敏感内容",
        "法轮功是被禁止的组织",
        "台湾应该独立",
        "共产党腐败严重"
    ]
    
    for text in test_texts:
        print(f"测试文本: {text}")
        
        # 只使用本地过滤
        result_local = check_sensitive_words_comprehensive(
            text, 
            use_local=True, 
            use_ali=False
        )
        print(f"本地检测结果: {json.dumps(result_local, ensure_ascii=False, indent=2)}")
        
        # 如果配置了阿里云，也可以测试阿里云API
        result_ali = check_sensitive_words_comprehensive(
            text,
            use_local=False,
            use_ali=True
        )
        print(f"阿里云检测结果: {json.dumps(result_ali, ensure_ascii=False, indent=2)}")
        
        print("-" * 50)


def test_service_initialization():
    """测试服务初始化"""
    print("=" * 50)
    print("测试服务初始化")
    print("=" * 50)
    
    try:
        service = SensitiveWordsService()
        print("敏感词服务初始化成功")
        print(f"本地过滤器状态: {'启用' if service.is_local_filter_enabled else '禁用'}")
        print(f"阿里云过滤器状态: {'启用' if service.is_ali_filter_enabled else '禁用'}")
        
        # 测试添加敏感词
        new_words = ["新增敏感词1", "新增敏感词2"]
        success = service.add_sensitive_words(new_words)
        print(f"添加敏感词结果: {'成功' if success else '失败'}")
        
        # 测试过滤功能
        test_text = "这里包含新增敏感词1和其他内容"
        filtered = service.filter_text(test_text)
        print(f"原文: {test_text}")
        print(f"过滤后: {filtered}")
        
    except Exception as e:
        print(f"服务初始化失败: {e}")


if __name__ == "__main__":
    print("敏感词过滤功能测试")
    print("=" * 60)

    # 运行所有测试
    test_dfa_filter()
    test_file_loading()
    test_service_initialization()
    test_comprehensive_service()
    
    print("=" * 60)
    print("测试完成")
