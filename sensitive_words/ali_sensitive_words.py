# coding=utf-8
# python version >= 3.6
from alibabacloud_green20220302.client import Client
from alibabacloud_green20220302 import models
from alibabacloud_tea_openapi.models import Config
import json
import os
from flask import jsonify

# 从环境变量或配置文件获取阿里云配置
def get_ali_config():
    """获取阿里云配置，优先从环境变量获取"""
    access_key_id = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
    access_key_secret = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')

    # 如果环境变量不存在，从配置文件获取（开发环境使用）
    if not access_key_id or not access_key_secret:
        try:
            from config.config import conf
            config_data = conf()
            access_key_id = config_data.get('alibaba_access_key_id', 'LTAI5tGtbM6jX7UUfYbWQhUY')
            access_key_secret = config_data.get('alibaba_access_key_secret', '******************************')
        except:
            # 兜底配置（仅用于开发测试）
            access_key_id = 'LTAI5tGtbM6jX7UUfYbWQhUY'
            access_key_secret = '******************************'

    return Config(
        access_key_id=access_key_id,
        access_key_secret=access_key_secret,
        # 连接超时时间 单位毫秒(ms)
        connect_timeout=10000,
        # 读超时时间 单位毫秒(ms)
        read_timeout=3000,
        region_id='cn-beijing',
        endpoint='green-cip.cn-beijing.aliyuncs.com'
    )

config = get_ali_config()

clt = Client(config)


def ali_check_sensitive_words(content, serviceType):
    serviceParameters = {
        'content': content
    }
    textModerationPlusRequest = models.TextModerationPlusRequest(
        # 检测类型  输入：llm_query_moderation，输出：llm_response_moderation
        service=serviceType,
        service_parameters=json.dumps(serviceParameters)
    )
    # 参考文章：https://help.aliyun.com/document_detail/2684669.html?spm=a2c4g.11186623.0.preDoc.501c3104RTvfeF
    try:
        response = clt.text_moderation_plus(textModerationPlusRequest)
        if response.status_code == 200:
            # 调用成功
            result = response.body
            print('response success. result:{}'.format(result))
            # 风险等级，根据设置的高低风险分返回，返回值包括：
            # high：高风险（若命中自定义词库，风险等级默认为高风险）
            # medium：中风险
            # low：低风险
            # none：未检测到风险
            risk_level = result.data.risk_level

            # 将响应对象转换为可序列化的字典
            if hasattr(result, 'to_map'):
                return result.to_map()
            else:
                # 手动构建响应字典
                return {
                    'Code': getattr(result, 'code', 200),
                    'Data': {
                        'RiskLevel': getattr(result.data, 'risk_level', 'none'),
                        'Result': getattr(result.data, 'result', []),
                        'Advice': getattr(result.data, 'advice', [])
                    },
                    'Message': getattr(result, 'message', 'OK'),
                    'RequestId': getattr(result, 'request_id', '')
                }
        else:
            print('response not success. status:{} ,result:{}'.format(response.status_code, response))
            return jsonify({"code": response.status_code})
    except Exception as err:
        print(err)
        return jsonify({"code": 500})


if __name__ == '__main__':
    ali_check_sensitive_words("中国共产党您好","llm_query_moderation")
