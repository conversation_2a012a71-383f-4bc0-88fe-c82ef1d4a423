# 敏感词过滤功能

## 功能概述

本模块提供完整的敏感词过滤功能，结合本地DFA算法和阿里云内容安全API，支持多词库分类管理和双重检测机制。

## 核心特性

- **双重检测**：本地DFA算法（毫秒级） + 阿里云API（高准确性）
- **多词库支持**：从`file/vocabulary`目录自动加载10个分类词库
- **安全配置**：支持环境变量配置，避免密钥泄露
- **完整API**：提供RESTful接口，支持多种检测模式
- **管理工具**：词库统计、验证和管理功能

## 文件结构

```
sensitive_words/
├── ali_sensitive_words.py         # 阿里云API封装（安全配置优化）
├── custom_sensitive_words.py      # 本地DFA算法实现
├── sensitive_words_service.py     # 综合服务类（新增）
├── vocabulary_manager.py          # 词库管理工具（新增）
├── test_sensitive_words.py        # 测试脚本
└── file/vocabulary/               # 敏感词库目录
    ├── COVID-19词库.txt          # 疫情相关
    ├── GFW补充词库.txt           # 网络审查
    ├── 反动词库.txt              # 政治敏感
    ├── 暴恐词库.txt              # 暴力恐怖
    ├── 色情词库.txt              # 色情内容
    ├── 贪腐词库.txt              # 贪污腐败
    └── 其他分类词库...            # 更多分类
```

## 快速开始

### 1. 环境配置
```bash
# 设置阿里云密钥（推荐）
export ALIBABA_CLOUD_ACCESS_KEY_ID="your_access_key_id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your_access_key_secret"
```

### 2. 基本使用
```python
from sensitive_words.sensitive_words_service import check_sensitive_words_comprehensive

# 综合检测
result = check_sensitive_words_comprehensive(
    content="要检查的文本内容",
    use_local=True,  # 本地DFA检测
    use_ali=True     # 阿里云API检测
)
```

### 3. API接口

```bash
# 检查输入内容
POST /ai-answer/sensitive-words/check_input
{
  "content": "要检查的文本",
  "use_local": true,
  "use_ali": true
}

# 检查输出内容
POST /ai-answer/sensitive-words/check_output
{
  "content": "要检查的文本",
  "use_local": true,
  "use_ali": true
}

# 过滤敏感词
POST /ai-answer/sensitive-words/filter
{
  "content": "包含敏感词的文本",
  "replacement": "*"
}
```

### 4. 词库管理
```python
from sensitive_words.vocabulary_manager import VocabularyManager

# 查看词库统计
manager = VocabularyManager()
manager.print_report()

# 验证文件完整性
issues = manager.validate_files()
```

## API响应格式
```json
{
  "content": "原始文本内容",
  "has_sensitive": true,
  "risk_level": "high",
  "sensitive_words": ["敏感词1", "敏感词2"],
  "filtered_content": "过滤后的内容",
  "local_result": {...},
  "ali_result": {...}
}
```

## 配置说明

**风险等级**：`none` | `low` | `medium` | `high`
**服务类型**：`llm_query_moderation`（输入） | `llm_response_moderation`（输出）

## 测试和管理

```bash
# 运行功能测试
python test_simple.py
python test_vocabulary.py

# 词库管理工具
python sensitive_words/vocabulary_manager.py

# 测试模块功能
python sensitive_words/test_sensitive_words.py
```

## 性能特点

- **DFA算法**：毫秒级检测速度，内存中构建敏感词树
- **批量加载**：启动时一次性加载所有词库文件
- **分类管理**：按需启用/禁用特定类别词库
- **双重检测**：本地高速检测 + 云端专业检测

## 安全建议

1. **使用环境变量**存储阿里云AccessKey
2. **定期更新词库**保持敏感词的时效性
3. **权限控制**限制API访问权限
4. **日志记录**记录检测结果但不记录具体内容

## 故障排除

**找不到词库文件**：确认`sensitive_words/file/vocabulary`目录存在
**阿里云API失败**：检查AccessKey配置和网络连接
**编码问题**：确保所有txt文件使用UTF-8编码

## 功能总结

✅ **多词库分类管理** - 10个分类词库，按类别组织
✅ **双重检测机制** - 本地DFA + 阿里云API
✅ **安全配置管理** - 环境变量配置，避免密钥泄露
✅ **完整API接口** - 支持多种检测和过滤模式
✅ **管理和测试工具** - 词库统计、验证和测试功能
