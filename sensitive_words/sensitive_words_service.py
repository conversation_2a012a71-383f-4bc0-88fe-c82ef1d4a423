# coding=utf-8
"""
综合敏感词过滤服务
结合阿里云API和本地DFA算法，提供多层次的敏感词检测
"""
import os
import logging
from typing import Dict, List

from .ali_sensitive_words import ali_check_sensitive_words
from .custom_sensitive_words import DFAFilter


class SensitiveWordsService:
    """敏感词过滤服务类"""
    
    def __init__(self):
        self.dfa_filter = DFAFilter()
        self.is_local_filter_enabled = True
        self.is_ali_filter_enabled = True
        self._init_local_filter()
    
    def _init_local_filter(self):
        """初始化本地敏感词过滤器"""
        try:
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # 从file/vocabulary目录加载所有敏感词文件
            vocabulary_dir = os.path.join(current_dir, 'file', 'vocabulary')

            if os.path.exists(vocabulary_dir) and os.path.isdir(vocabulary_dir):
                loaded_files = []
                total_words = 0

                # 遍历vocabulary目录下的所有文件
                for filename in os.listdir(vocabulary_dir):
                    if filename.endswith('.txt'):
                        file_path = os.path.join(vocabulary_dir, filename)
                        try:
                            # 统计文件中的词汇数量
                            with open(file_path, 'r', encoding='utf-8') as f:
                                word_count = sum(1 for line in f if line.strip())

                            # 加载敏感词文件
                            self.dfa_filter.parse_file(file_path)
                            loaded_files.append(filename)
                            total_words += word_count
                            logging.info(f"成功加载敏感词文件: {filename} ({word_count} 个词汇)")
                        except Exception as e:
                            logging.error(f"加载敏感词文件失败 {filename}: {e}")

                if loaded_files:
                    logging.info(f"总共加载了 {len(loaded_files)} 个敏感词文件，包含 {total_words} 个敏感词")
                    logging.info(f"加载的文件: {', '.join(loaded_files)}")
                else:
                    logging.warning("vocabulary目录中没有找到有效的敏感词文件")
                    self._load_default_words()
            else:
                logging.warning(f"未找到file/vocabulary目录: {vocabulary_dir}，将使用默认敏感词")
                self._load_default_words()

        except Exception as e:
            logging.error(f"初始化本地敏感词过滤器失败: {e}")
            self.is_local_filter_enabled = False

    def _load_default_words(self):
        """加载默认敏感词"""
        default_words = [
            "测试敏感词", "违法", "暴力", "色情", "赌博", "毒品", "恐怖主义",
            "诈骗", "洗钱", "人口贩卖", "儿童色情", "自杀", "自残"
        ]
        self.dfa_filter.build_keywords(default_words)
        logging.info(f"加载了 {len(default_words)} 个默认敏感词")
    
    def check_sensitive_words(self, content: str, service_type: str = "llm_query_moderation", 
                            use_local: bool = True, use_ali: bool = True) -> Dict:
        """
        检查敏感词
        
        Args:
            content: 要检查的文本内容
            service_type: 阿里云服务类型 (llm_query_moderation/llm_response_moderation)
            use_local: 是否使用本地过滤
            use_ali: 是否使用阿里云过滤
            
        Returns:
            检查结果字典
        """
        result = {
            "content": content,
            "has_sensitive": False,
            "risk_level": "none",
            "local_result": None,
            "ali_result": None,
            "sensitive_words": [],
            "filtered_content": content,
            "message": "检查完成"
        }
        
        try:
            # 本地敏感词检查
            if use_local and self.is_local_filter_enabled:
                local_result = self._check_local_sensitive(content)
                result["local_result"] = local_result
                
                if local_result["has_sensitive"]:
                    result["has_sensitive"] = True
                    result["risk_level"] = "high"  # 本地检测到的设为高风险
                    result["sensitive_words"].extend(local_result["sensitive_words"])
                    result["filtered_content"] = local_result["filtered_content"]
            
            # 阿里云敏感词检查
            if use_ali and self.is_ali_filter_enabled:
                try:
                    ali_result = ali_check_sensitive_words(content, service_type)
                    result["ali_result"] = ali_result
                    
                    # 解析阿里云结果
                    if isinstance(ali_result, dict):
                        if ali_result.get("Data", {}).get("RiskLevel", "none") != "none":
                            result["has_sensitive"] = True
                            # 如果本地没有检测到，使用阿里云的风险等级
                            # if result["risk_level"] == "none":
                            result["risk_level"] = ali_result.get("Data", {}).get("RiskLevel", "none")
                            
                            # 提取阿里云检测到的敏感词
                            ali_words = self._extract_ali_sensitive_words(ali_result)
                            result["sensitive_words"].extend(ali_words)
                            
                except Exception as e:
                    logging.error(f"阿里云敏感词检查失败: {e}")
                    result["message"] += f" (阿里云检查失败: {str(e)})"
            
            # 去重敏感词
            result["sensitive_words"] = list(set(result["sensitive_words"]))
            
        except Exception as e:
            logging.error(f"敏感词检查失败: {e}")
            result["message"] = f"检查失败: {str(e)}"
            
        return result
    
    def _check_local_sensitive(self, content: str) -> Dict:
        """本地敏感词检查"""
        try:
            has_sensitive = self.dfa_filter.has_sensitive_word(content)
            sensitive_words = self.dfa_filter.get_sensitive_words(content)
            filtered_content = self.dfa_filter.filter_message(content, "*")
            
            return {
                "has_sensitive": has_sensitive,
                "sensitive_words": sensitive_words,
                "filtered_content": filtered_content,
                "method": "local_dfa"
            }
        except Exception as e:
            logging.error(f"本地敏感词检查失败: {e}")
            return {
                "has_sensitive": False,
                "sensitive_words": [],
                "filtered_content": content,
                "method": "local_dfa",
                "error": str(e)
            }
    
    def _extract_ali_sensitive_words(self, ali_result: Dict) -> List[str]:
        """从阿里云结果中提取敏感词"""
        sensitive_words = []
        try:
            if isinstance(ali_result, dict):
                data = ali_result.get("Data", {})
                results = data.get("Result", [])
                
                for item in results:
                    if isinstance(item, dict):
                        # 提取检测到的敏感内容
                        if "Label" in item:
                            sensitive_words.append(item["Label"])
                        if "Details" in item:
                            for detail in item["Details"]:
                                if isinstance(detail, dict) and "Label" in detail:
                                    sensitive_words.append(detail["Label"])
                                    
        except Exception as e:
            logging.error(f"提取阿里云敏感词失败: {e}")
            
        return sensitive_words
    
    def add_sensitive_words(self, words: List[str]) -> bool:
        """添加敏感词到本地过滤器"""
        try:
            self.dfa_filter.build_keywords(words)
            return True
        except Exception as e:
            logging.error(f"添加敏感词失败: {e}")
            return False
    
    def filter_text(self, content: str, replacement: str = "*") -> str:
        """过滤文本中的敏感词"""
        try:
            if self.is_local_filter_enabled:
                return self.dfa_filter.filter_message(content, replacement)
            return content
        except Exception as e:
            logging.error(f"过滤文本失败: {e}")
            return content


# 全局服务实例
sensitive_service = SensitiveWordsService()


def check_sensitive_words_comprehensive(content: str, service_type: str = "llm_query_moderation", 
                                      use_local: bool = True, use_ali: bool = True) -> Dict:
    """
    综合敏感词检查函数
    
    Args:
        content: 要检查的文本内容
        service_type: 阿里云服务类型
        use_local: 是否使用本地过滤
        use_ali: 是否使用阿里云过滤
        
    Returns:
        检查结果字典
    """
    return sensitive_service.check_sensitive_words(content, service_type, use_local, use_ali)


def filter_sensitive_words(content: str, replacement: str = "*") -> str:
    """
    过滤敏感词
    
    Args:
        content: 要过滤的文本内容
        replacement: 替换字符
        
    Returns:
        过滤后的文本
    """
    return sensitive_service.filter_text(content, replacement)
