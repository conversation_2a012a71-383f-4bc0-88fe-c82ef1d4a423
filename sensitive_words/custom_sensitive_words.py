class DFAFilter:
    def __init__(self):
        self.keyword_chains = {}
        self.delimit = '\x00'  # 结束标识符

    def add_keyword(self, keyword):
        """添加敏感词"""
        chars = keyword.strip()
        if not chars:
            return
        level = self.keyword_chains
        for char in chars:
            if char not in level:
                level[char] = {}
            level = level[char]
        level[self.delimit] = 0  # 标记敏感词结束

    def build_keywords(self, keywords):
        """批量添加敏感词"""
        for keyword in keywords:
            self.add_keyword(keyword)

    def parse_file(self, path):
        """从文件加载敏感词"""
        with open(path, 'r', encoding='utf-8') as f:
            for line in f:
                self.add_keyword(line.strip())

    def filter_message(self, message, repl="*"):
        """过滤敏感词"""
        message = str(message)
        ret = []
        start = 0

        while start < len(message):
            level = self.keyword_chains
            step_ins = 0
            matched = False

            # 从当前位置开始匹配
            for i in range(start, len(message)):
                char = message[i]
                if char in level:
                    step_ins += 1
                    if self.delimit in level[char]:
                        # 找到完整敏感词
                        ret.append(repl * step_ins)
                        start += step_ins - 1
                        matched = True
                        break
                    level = level[char]
                else:
                    break

            if not matched:
                ret.append(message[start])
            start += 1

        return ''.join(ret)

    def has_sensitive_word(self, message):
        """检查是否包含敏感词"""
        message = str(message)
        for start in range(len(message)):
            level = self.keyword_chains
            for i in range(start, len(message)):
                char = message[i]
                if char in level:
                    if self.delimit in level[char]:
                        return True
                    level = level[char]
                else:
                    break
        return False

    def get_sensitive_words(self, message):
        """获取文本中的所有敏感词"""
        message = str(message)
        sensitive_words = []

        for start in range(len(message)):
            level = self.keyword_chains
            step_ins = 0

            for i in range(start, len(message)):
                char = message[i]
                if char in level:
                    step_ins += 1
                    if self.delimit in level[char]:
                        word = message[start:start + step_ins]
                        sensitive_words.append(word)
                        break
                    level = level[char]
                else:
                    break

        return sensitive_words

# 使用示例
if __name__ == "__main__":
    # 创建过滤器
    dfa_filter = DFAFilter()

    # 添加敏感词
    sensitive_words = ["我爱你", "我爱他", "我爱她", "大王八", "傻瓜"]
    dfa_filter.build_keywords(sensitive_words)

    # 测试文本
    test_text = "我爱你呀，但是你是个大王八和傻瓜"

    # 检查是否包含敏感词
    print(f"包含敏感词: {dfa_filter.has_sensitive_word(test_text)}")

    # 获取敏感词列表
    print(f"敏感词列表: {dfa_filter.get_sensitive_words(test_text)}")

    # 过滤敏感词
    filtered_text = dfa_filter.filter_message(test_text)
    print(f"过滤后文本: {filtered_text}")
