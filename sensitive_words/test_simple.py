#!/usr/bin/env python3
# coding=utf-8
"""
简单的敏感词过滤功能测试
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("测试基本敏感词过滤功能")
    print("=" * 50)
    
    try:
        from sensitive_words.custom_sensitive_words import DFAFilter
        
        # 创建DFA过滤器
        dfa_filter = DFAFilter()
        
        # 添加测试敏感词
        test_words = ["测试敏感词", "违法", "暴力", "色情", "赌博"]
        dfa_filter.build_keywords(test_words)
        
        print("成功创建DFA过滤器并添加敏感词")
        
        # 测试文本
        test_texts = [
            "这是一个正常的文本",
            "这里包含测试敏感词",
            "违法行为是不被允许的",
            "暴力和色情内容都不合适",
            "网络赌博害人不浅"
        ]
        
        for text in test_texts:
            has_sensitive = dfa_filter.has_sensitive_word(text)
            sensitive_words = dfa_filter.get_sensitive_words(text)
            filtered_text = dfa_filter.filter_message(text)
            
            print(f"原文: {text}")
            print(f"包含敏感词: {has_sensitive}")
            print(f"敏感词列表: {sensitive_words}")
            print(f"过滤后: {filtered_text}")
            print("-" * 30)
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_file_loading():
    """测试从file/vocabulary目录加载敏感词文件"""
    print("=" * 50)
    print("测试从file/vocabulary目录加载敏感词")
    print("=" * 50)

    try:
        from sensitive_words.custom_sensitive_words import DFAFilter

        # 创建DFA过滤器
        dfa_filter = DFAFilter()

        # 检查file/vocabulary目录是否存在
        vocabulary_dir = "file/vocabulary"
        if os.path.exists(vocabulary_dir) and os.path.isdir(vocabulary_dir):
            print(f"找到file/vocabulary目录: {vocabulary_dir}")

            loaded_files = []
            total_words = 0

            # 遍历vocabulary目录下的所有txt文件
            for filename in os.listdir(vocabulary_dir):
                if filename.endswith('.txt'):
                    file_path = os.path.join(vocabulary_dir, filename)
                    try:
                        # 统计文件中的词汇数量
                        with open(file_path, 'r', encoding='utf-8') as f:
                            word_count = sum(1 for line in f if line.strip())

                        # 加载敏感词文件
                        dfa_filter.parse_file(file_path)
                        loaded_files.append(filename)
                        total_words += word_count
                        print(f"成功加载: {filename} ({word_count} 个词汇)")
                    except Exception as e:
                        print(f"加载失败 {filename}: {e}")

            if loaded_files:
                print(f"\n总共加载了 {len(loaded_files)} 个文件，包含 {total_words} 个敏感词")

                # 测试一些可能的敏感词
                test_texts = [
                    "这是正常文本",
                    "法轮功相关内容",
                    "共产党万岁",
                    "台湾独立",
                    "色情暴力内容"
                ]

                print(f"\n开始测试敏感词检测:")
                for text in test_texts:
                    has_sensitive = dfa_filter.has_sensitive_word(text)
                    sensitive_words = dfa_filter.get_sensitive_words(text)

                    print(f"原文: {text}")
                    print(f"包含敏感词: {has_sensitive}")
                    if sensitive_words:
                        print(f"敏感词: {sensitive_words}")
                    print("-" * 30)
            else:
                print("没有找到有效的敏感词文件")

        else:
            print(f"file/vocabulary目录不存在: {vocabulary_dir}")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_service():
    """测试敏感词服务"""
    print("=" * 50)
    print("测试敏感词服务")
    print("=" * 50)
    
    try:
        from sensitive_words.sensitive_words_service import SensitiveWordsService
        
        # 创建服务实例
        service = SensitiveWordsService()
        print("敏感词服务初始化成功")
        print(f"本地过滤器状态: {'启用' if service.is_local_filter_enabled else '禁用'}")
        
        # 测试检查功能
        test_text = "这里包含一些测试内容"
        result = service.check_sensitive_words(test_text, use_local=True, use_ali=False)
        
        print(f"测试文本: {test_text}")
        print(f"检测结果: {result}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("敏感词过滤功能简单测试")
    print("=" * 60)
    
    # 运行测试
    test_basic_functionality()
    test_file_loading()
    test_service()
    
    print("=" * 60)
    print("测试完成")
