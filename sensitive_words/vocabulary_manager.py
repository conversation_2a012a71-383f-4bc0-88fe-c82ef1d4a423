#!/usr/bin/env python3
# coding=utf-8
"""
敏感词库管理工具
用于管理vocabulary目录下的敏感词文件
"""
import os
import json
import logging
from typing import Dict, List, Tuple


class VocabularyManager:
    """敏感词库管理器"""
    
    def __init__(self, vocabulary_dir: str = None):
        if vocabulary_dir is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            vocabulary_dir = os.path.join(current_dir, 'file', 'vocabulary')

        self.vocabulary_dir = vocabulary_dir
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            config_path = os.path.join(
                os.path.dirname(self.vocabulary_dir), 
                '..', 'config', 'vocabulary_config.json'
            )
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logging.warning(f"加载配置文件失败: {e}")
        
        # 返回默认配置
        return {
            "vocabulary_settings": {
                "file_encoding": "utf-8",
                "file_extensions": [".txt"]
            }
        }
    
    def scan_vocabulary_files(self) -> List[Dict]:
        """扫描vocabulary目录下的所有敏感词文件"""
        if not os.path.exists(self.vocabulary_dir):
            return []
        
        files_info = []
        extensions = self.config.get("vocabulary_settings", {}).get("file_extensions", [".txt"])
        
        for filename in os.listdir(self.vocabulary_dir):
            if any(filename.endswith(ext) for ext in extensions):
                file_path = os.path.join(self.vocabulary_dir, filename)
                file_info = self._analyze_file(file_path, filename)
                files_info.append(file_info)
        
        return sorted(files_info, key=lambda x: x['filename'])
    
    def _analyze_file(self, file_path: str, filename: str) -> Dict:
        """分析单个敏感词文件"""
        info = {
            'filename': filename,
            'path': file_path,
            'exists': os.path.exists(file_path),
            'size_bytes': 0,
            'word_count': 0,
            'encoding': 'utf-8',
            'category': 'unknown',
            'sample_words': [],
            'error': None
        }
        
        try:
            # 获取文件大小
            info['size_bytes'] = os.path.getsize(file_path)
            
            # 从配置中获取分类信息
            categories = self.config.get("word_categories", {})
            if filename in categories:
                info['category'] = categories[filename].get('category', 'unknown')
                info['priority'] = categories[filename].get('priority', 'medium')
                info['enabled'] = categories[filename].get('enabled', True)
                info['description'] = categories[filename].get('description', '')
            
            # 分析文件内容
            encoding = self.config.get("vocabulary_settings", {}).get("file_encoding", "utf-8")
            with open(file_path, 'r', encoding=encoding) as f:
                lines = [line.strip() for line in f if line.strip()]
                info['word_count'] = len(lines)
                info['sample_words'] = lines[:5]  # 前5个词作为样本
                
        except Exception as e:
            info['error'] = str(e)
            
        return info
    
    def get_statistics(self) -> Dict:
        """获取词库统计信息"""
        files_info = self.scan_vocabulary_files()
        
        stats = {
            'total_files': len(files_info),
            'total_words': 0,
            'total_size_bytes': 0,
            'categories': {},
            'enabled_files': 0,
            'disabled_files': 0,
            'error_files': 0
        }
        
        for file_info in files_info:
            stats['total_words'] += file_info['word_count']
            stats['total_size_bytes'] += file_info['size_bytes']
            
            # 统计分类
            category = file_info['category']
            if category not in stats['categories']:
                stats['categories'][category] = {
                    'files': 0,
                    'words': 0
                }
            stats['categories'][category]['files'] += 1
            stats['categories'][category]['words'] += file_info['word_count']
            
            # 统计状态
            if file_info.get('error'):
                stats['error_files'] += 1
            elif file_info.get('enabled', True):
                stats['enabled_files'] += 1
            else:
                stats['disabled_files'] += 1
        
        return stats
    
    def print_report(self):
        """打印详细报告"""
        print("=" * 80)
        print("敏感词库管理报告")
        print("=" * 80)
        
        # 基本信息
        print(f"词库目录: {self.vocabulary_dir}")
        print(f"目录存在: {'是' if os.path.exists(self.vocabulary_dir) else '否'}")
        
        if not os.path.exists(self.vocabulary_dir):
            print("错误: 词库目录不存在!")
            return
        
        # 统计信息
        stats = self.get_statistics()
        print(f"\n总体统计:")
        print(f"  文件总数: {stats['total_files']}")
        print(f"  词汇总数: {stats['total_words']:,}")
        print(f"  总大小: {stats['total_size_bytes']:,} 字节")
        print(f"  启用文件: {stats['enabled_files']}")
        print(f"  禁用文件: {stats['disabled_files']}")
        print(f"  错误文件: {stats['error_files']}")
        
        # 分类统计
        if stats['categories']:
            print(f"\n分类统计:")
            for category, info in stats['categories'].items():
                print(f"  {category}: {info['files']} 个文件, {info['words']:,} 个词汇")
        
        # 文件详情
        files_info = self.scan_vocabulary_files()
        if files_info:
            print(f"\n文件详情:")
            print("-" * 80)
            for file_info in files_info:
                status = "✓" if not file_info.get('error') else "✗"
                enabled = "启用" if file_info.get('enabled', True) else "禁用"
                
                print(f"{status} {file_info['filename']}")
                print(f"    分类: {file_info['category']} | 状态: {enabled}")
                print(f"    词汇数: {file_info['word_count']:,} | 大小: {file_info['size_bytes']:,} 字节")
                
                if file_info.get('description'):
                    print(f"    描述: {file_info['description']}")
                
                if file_info.get('sample_words'):
                    sample = ', '.join(file_info['sample_words'][:3])
                    print(f"    示例: {sample}...")
                
                if file_info.get('error'):
                    print(f"    错误: {file_info['error']}")
                
                print()
    
    def validate_files(self) -> List[Dict]:
        """验证所有文件的完整性"""
        files_info = self.scan_vocabulary_files()
        issues = []
        
        for file_info in files_info:
            if file_info.get('error'):
                issues.append({
                    'file': file_info['filename'],
                    'type': 'error',
                    'message': file_info['error']
                })
            elif file_info['word_count'] == 0:
                issues.append({
                    'file': file_info['filename'],
                    'type': 'warning',
                    'message': '文件为空'
                })
            elif file_info['word_count'] < 10:
                issues.append({
                    'file': file_info['filename'],
                    'type': 'warning',
                    'message': f'词汇数量较少 ({file_info["word_count"]} 个)'
                })
        
        return issues


def main():
    """主函数"""
    manager = VocabularyManager()
    
    # 打印报告
    manager.print_report()
    
    # 验证文件
    issues = manager.validate_files()
    if issues:
        print("=" * 80)
        print("发现的问题:")
        print("=" * 80)
        for issue in issues:
            icon = "⚠️" if issue['type'] == 'warning' else "❌"
            print(f"{icon} {issue['file']}: {issue['message']}")
    else:
        print("=" * 80)
        print("✅ 所有文件验证通过!")


if __name__ == "__main__":
    main()
