#!/usr/bin/env python3
# coding=utf-8
"""
测试vocabulary目录下的敏感词文件加载功能
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_vocabulary_loading():
    """测试从file/vocabulary目录加载敏感词"""
    print("=" * 60)
    print("测试从file/vocabulary目录加载敏感词文件")
    print("=" * 60)

    try:
        from sensitive_words.custom_sensitive_words import DFAFilter

        # 创建DFA过滤器
        dfa_filter = DFAFilter()

        # 检查file/vocabulary目录
        vocabulary_dir = "file/vocabulary"
        if not os.path.exists(vocabulary_dir):
            print(f"错误: file/vocabulary目录不存在: {vocabulary_dir}")
            return

        print(f"找到file/vocabulary目录: {vocabulary_dir}")
        
        # 列出所有txt文件
        txt_files = [f for f in os.listdir(vocabulary_dir) if f.endswith('.txt')]
        print(f"发现 {len(txt_files)} 个txt文件:")
        for f in txt_files:
            print(f"  - {f}")
        
        print("\n开始加载敏感词文件...")
        
        loaded_files = []
        total_words = 0
        
        # 加载每个文件
        for filename in txt_files:
            file_path = os.path.join(vocabulary_dir, filename)
            try:
                # 统计词汇数量
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f if line.strip()]
                    word_count = len(lines)
                
                # 加载到DFA过滤器
                dfa_filter.parse_file(file_path)
                loaded_files.append(filename)
                total_words += word_count
                
                print(f"✓ {filename}: {word_count} 个词汇")
                
                # 显示前几个词汇作为示例
                if word_count > 0:
                    sample_words = lines[:3]
                    print(f"  示例词汇: {', '.join(sample_words)}")
                
            except Exception as e:
                print(f"✗ {filename}: 加载失败 - {e}")
        
        print(f"\n加载总结:")
        print(f"成功加载: {len(loaded_files)} 个文件")
        print(f"总词汇数: {total_words} 个")
        
        if loaded_files:
            # 测试敏感词检测
            print(f"\n" + "=" * 40)
            print("测试敏感词检测功能")
            print("=" * 40)
            
            test_texts = [
                "这是一个正常的测试文本",
                "法轮功是被禁止的组织",
                "台湾应该独立",
                "共产党腐败严重",
                "色情暴力内容不合适",
                "恐怖主义活动",
                "贪污受贿官员",
                "新冠疫情相关内容"
            ]
            
            for text in test_texts:
                has_sensitive = dfa_filter.has_sensitive_word(text)
                sensitive_words = dfa_filter.get_sensitive_words(text)
                filtered_text = dfa_filter.filter_message(text, "*")
                
                print(f"\n原文: {text}")
                print(f"包含敏感词: {'是' if has_sensitive else '否'}")
                if sensitive_words:
                    print(f"检测到的敏感词: {sensitive_words}")
                    print(f"过滤后文本: {filtered_text}")
                print("-" * 40)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_service_with_vocabulary():
    """测试敏感词服务使用file/vocabulary目录"""
    print("\n" + "=" * 60)
    print("测试敏感词服务使用file/vocabulary目录")
    print("=" * 60)
    
    try:
        from sensitive_words.sensitive_words_service import SensitiveWordsService
        
        # 创建服务实例
        service = SensitiveWordsService()
        print(f"敏感词服务初始化: {'成功' if service.is_local_filter_enabled else '失败'}")
        
        if service.is_local_filter_enabled:
            # 测试检查功能
            test_texts = [
                "这是正常内容",
                "包含法轮功的敏感内容",
                "台独分子活动",
                "色情暴力内容"
            ]
            
            for text in test_texts:
                result = service.check_sensitive_words(
                    text, 
                    use_local=True, 
                    use_ali=False
                )
                
                print(f"\n测试文本: {text}")
                print(f"检测结果: {'发现敏感词' if result['has_sensitive'] else '无敏感词'}")
                print(f"风险等级: {result['risk_level']}")
                if result['sensitive_words']:
                    print(f"敏感词列表: {result['sensitive_words']}")
                print(f"过滤后文本: {result['filtered_content']}")
        
    except Exception as e:
        print(f"服务测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("敏感词vocabulary目录功能测试")
    print("=" * 80)
    
    # 运行测试
    test_vocabulary_loading()
    test_service_with_vocabulary()
    
    print("\n" + "=" * 80)
    print("测试完成")
