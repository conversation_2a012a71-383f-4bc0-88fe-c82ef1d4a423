# base stage

FROM python:3.12-slim

ADD . /ai-answer

WORKDIR /ai-answer

RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources


# Update the package list and install necessary packages
#RUN apt-get update && \
#    apt-get install -y wkhtmltopdf \
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libpq-dev \
    wkhtmltopdf \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

RUN tar -xzf ta-lib-0.4.0-src.tar.gz && \
    cd ta-lib && \
    ./configure --prefix=/usr && \
    make && \
    make install && \
    cd .. && \
    rm -rf ta-lib ta-lib-0.4.0-src.tar.gz


RUN pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple

# builder stage
RUN pip install -i https://mirrors.aliyun.com/pypi/simple \
    --default-timeout=100 \
    -r requirements.txt

RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

# # 暴露容器的端口
EXPOSE 9000

CMD ["python","-u","run.py","prod"]