"""
知识空间API测试用例
演示如何使用知识空间的各种功能
"""
import unittest
import json
from flask import Flask
from flask_testing import TestCase
from knowledge_spaces import knowledge_spaces_bp
from knowledge_spaces.model.knowledge_spaces_model import db


class KnowledgeSpaceTestCase(TestCase):
    """知识空间测试用例"""
    
    def create_app(self):
        """创建测试应用"""
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        # 注册蓝图
        app.register_blueprint(knowledge_spaces_bp, url_prefix='/api/v1/knowledge-spaces')
        
        # 初始化数据库
        db.init_app(app)
        with app.app_context():
            db.create_all()
        
        return app
    
    def setUp(self):
        """测试前置设置"""
        self.user_id = 1001
        self.creator_id = 1001
        
    def tearDown(self):
        """测试后清理"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_create_space(self):
        """测试创建知识空间"""
        # 创建顶级空间
        data = {
            "userId": self.user_id,
            "name": "技术文档",
            "description": "存放技术相关文档",
            "creator": self.creator_id
        }
        
        response = self.client.post('/api/v1/knowledge-spaces', 
                                  json=data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['name'], '技术文档')
        self.assertEqual(result['data']['level'], 0)
    
    def test_create_sub_space(self):
        """测试创建子空间"""
        # 先创建父空间
        parent_data = {
            "userId": self.user_id,
            "name": "技术文档",
            "description": "存放技术相关文档", 
            "creator": self.creator_id
        }
        
        parent_response = self.client.post('/api/v1/knowledge-spaces',
                                         json=parent_data,
                                         content_type='application/json')
        
        parent_result = json.loads(parent_response.data)
        parent_id = parent_result['data']['id']
        
        # 创建子空间
        child_data = {
            "userId": self.user_id,
            "parentId": parent_id,
            "name": "Python文档",
            "description": "Python相关文档",
            "creator": self.creator_id
        }
        
        response = self.client.post('/api/v1/knowledge-spaces',
                                  json=child_data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['name'], 'Python文档')
        self.assertEqual(result['data']['level'], 1)
        self.assertEqual(result['data']['parentId'], parent_id)
    
    def test_get_spaces_list(self):
        """测试获取空间列表"""
        # 先创建一些测试数据
        spaces = [
            {"name": "技术文档", "description": "技术文档"},
            {"name": "产品文档", "description": "产品文档"},
            {"name": "设计文档", "description": "设计文档"}
        ]
        
        for space in spaces:
            data = {
                "userId": self.user_id,
                "name": space["name"],
                "description": space["description"],
                "creator": self.creator_id
            }
            self.client.post('/api/v1/knowledge-spaces', json=data)
        
        # 获取列表
        response = self.client.get(f'/api/v1/knowledge-spaces?userId={self.user_id}&page=1&pageSize=10')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(len(result['data']), 3)
        self.assertEqual(result['total'], 3)
    
    def test_get_space_tree(self):
        """测试获取空间树结构"""
        # 创建多级空间结构
        # 顶级: 技术文档
        parent_data = {
            "userId": self.user_id,
            "name": "技术文档",
            "creator": self.creator_id
        }
        parent_response = self.client.post('/api/v1/knowledge-spaces', json=parent_data)
        parent_id = json.loads(parent_response.data)['data']['id']
        
        # 二级: Python文档
        child_data = {
            "userId": self.user_id,
            "parentId": parent_id,
            "name": "Python文档",
            "creator": self.creator_id
        }
        child_response = self.client.post('/api/v1/knowledge-spaces', json=child_data)
        child_id = json.loads(child_response.data)['data']['id']
        
        # 三级: Django文档
        grandchild_data = {
            "userId": self.user_id,
            "parentId": child_id,
            "name": "Django文档",
            "creator": self.creator_id
        }
        self.client.post('/api/v1/knowledge-spaces', json=grandchild_data)
        
        # 获取树结构
        response = self.client.get(f'/api/v1/knowledge-spaces/tree?userId={self.user_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(len(result['data']), 1)  # 一个根节点
        self.assertEqual(result['data'][0]['name'], '技术文档')
        self.assertEqual(len(result['data'][0]['children']), 1)  # 一个子节点
    
    def test_update_space(self):
        """测试更新空间"""
        # 创建空间
        data = {
            "userId": self.user_id,
            "name": "技术文档",
            "creator": self.creator_id
        }
        response = self.client.post('/api/v1/knowledge-spaces', json=data)
        space_id = json.loads(response.data)['data']['id']
        
        # 更新空间
        update_data = {
            "name": "技术资料库",
            "description": "更新后的描述",
            "updater": self.creator_id
        }
        
        response = self.client.put(f'/api/v1/knowledge-spaces/{space_id}?userId={self.user_id}',
                                 json=update_data)
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['name'], '技术资料库')
        self.assertEqual(result['data']['description'], '更新后的描述')
    
    def test_move_space(self):
        """测试移动空间"""
        # 创建两个顶级空间
        parent1_data = {"userId": self.user_id, "name": "文档库1", "creator": self.creator_id}
        parent2_data = {"userId": self.user_id, "name": "文档库2", "creator": self.creator_id}
        
        parent1_response = self.client.post('/api/v1/knowledge-spaces', json=parent1_data)
        parent2_response = self.client.post('/api/v1/knowledge-spaces', json=parent2_data)
        
        parent1_id = json.loads(parent1_response.data)['data']['id']
        parent2_id = json.loads(parent2_response.data)['data']['id']
        
        # 在parent1下创建子空间
        child_data = {
            "userId": self.user_id,
            "parentId": parent1_id,
            "name": "子文档",
            "creator": self.creator_id
        }
        child_response = self.client.post('/api/v1/knowledge-spaces', json=child_data)
        child_id = json.loads(child_response.data)['data']['id']
        
        # 将子空间移动到parent2下
        move_data = {
            "targetParentId": parent2_id,
            "updater": self.creator_id
        }
        
        response = self.client.put(f'/api/v1/knowledge-spaces/{child_id}/move?userId={self.user_id}',
                                 json=move_data)
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
    
    def test_delete_space(self):
        """测试删除空间"""
        # 创建空间
        data = {
            "userId": self.user_id,
            "name": "待删除空间",
            "creator": self.creator_id
        }
        response = self.client.post('/api/v1/knowledge-spaces', json=data)
        space_id = json.loads(response.data)['data']['id']
        
        # 软删除
        response = self.client.delete(f'/api/v1/knowledge-spaces/{space_id}?userId={self.user_id}&deleter={self.creator_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        
        # 验证已删除 - 获取应该返回404
        response = self.client.get(f'/api/v1/knowledge-spaces/{space_id}?userId={self.user_id}')
        self.assertEqual(response.status_code, 404)
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        # 创建一些测试数据
        spaces_data = [
            {"name": "空间1", "status": 1},
            {"name": "空间2", "status": 1}, 
            {"name": "空间3", "status": 0}  # 禁用状态
        ]
        
        for space_data in spaces_data:
            data = {
                "userId": self.user_id,
                "name": space_data["name"],
                "creator": self.creator_id
            }
            self.client.post('/api/v1/knowledge-spaces', json=data)
        
        # 获取统计
        response = self.client.get(f'/api/v1/knowledge-spaces/statistics?userId={self.user_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['total'], 3)
        self.assertEqual(result['data']['active'], 3)  # 创建时默认状态为1


if __name__ == '__main__':
    unittest.main()