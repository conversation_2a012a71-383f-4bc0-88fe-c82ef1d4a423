"""
AI应用收藏功能测试
"""
import pytest
import json
from unittest.mock import patch, MagicMock
from flask import Flask
from multi_agent.controller.ai_application_favorites_controller import ai_application_favorites_bp
from multi_agent.service.ai_application_favorites_service import AIApplicationFavoritesService
from multi_agent.model.ai_application_favorites_model import AIApplicationFavoritesDO


class TestAIApplicationFavoritesController:
    """AI应用收藏控制器测试类"""
    
    @pytest.fixture
    def app(self):
        """创建测试应用"""
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.register_blueprint(ai_application_favorites_bp, url_prefix='/favorites')
        return app
    
    @pytest.fixture
    def client(self, app):
        """创建测试客户端"""
        return app.test_client()
    
    @patch.object(AIApplicationFavoritesService, 'add_favorite')
    def test_add_favorite_success(self, mock_add_favorite, client):
        """测试添加收藏成功"""
        # 模拟服务层返回
        mock_favorite = MagicMock()
        mock_favorite.dict.return_value = {
            'id': 1,
            'device_id': 'device123',
            'user_id': 1,
            'application_id': 1
        }
        mock_add_favorite.return_value = mock_favorite
        
        # 发送请求
        response = client.post('/favorites', 
                             json={
                                 'device_id': 'device123',
                                 'user_id': 1,
                                 'application_id': 1
                             },
                             content_type='application/json')
        
        # 验证响应
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['message'] == '收藏成功'
        assert data['data']['id'] == 1
    
    @patch.object(AIApplicationFavoritesService, 'add_favorite')
    def test_add_favorite_already_exists(self, mock_add_favorite, client):
        """测试添加已存在的收藏"""
        # 模拟服务层抛出异常
        mock_add_favorite.side_effect = ValueError("应用已收藏")
        
        # 发送请求
        response = client.post('/favorites',
                             json={
                                 'device_id': 'device123',
                                 'user_id': 1,
                                 'application_id': 1
                             },
                             content_type='application/json')
        
        # 验证响应
        assert response.status_code == 409
        data = json.loads(response.data)
        assert data['code'] == 409
        assert '应用已收藏' in data['message']
    
    def test_add_favorite_missing_params(self, client):
        """测试添加收藏缺少参数"""
        # 发送缺少参数的请求
        response = client.post('/favorites',
                             json={
                                 'device_id': 'device123',
                                 'user_id': 1
                                 # 缺少 application_id
                             },
                             content_type='application/json')
        
        # 验证响应
        assert response.status_code == 409
        data = json.loads(response.data)
        assert data['code'] == 409
        assert 'application_id为必填字段' in data['message']
    
    @patch.object(AIApplicationFavoritesService, 'remove_favorite')
    def test_remove_favorite_success(self, mock_remove_favorite, client):
        """测试取消收藏成功"""
        # 模拟服务层返回
        mock_remove_favorite.return_value = True
        
        # 发送请求
        response = client.delete('/favorites?device_id=device123&user_id=1&application_id=1')
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['message'] == '取消收藏成功'
    
    @patch.object(AIApplicationFavoritesService, 'remove_favorite')
    def test_remove_favorite_not_found(self, mock_remove_favorite, client):
        """测试取消不存在的收藏"""
        # 模拟服务层返回
        mock_remove_favorite.return_value = False
        
        # 发送请求
        response = client.delete('/favorites?device_id=device123&user_id=1&application_id=1')
        
        # 验证响应
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['code'] == 404
        assert data['message'] == '收藏记录不存在'
    
    @patch.object(AIApplicationFavoritesService, 'is_favorite')
    def test_check_favorite_exists(self, mock_is_favorite, client):
        """测试检查收藏状态 - 已收藏"""
        # 模拟服务层返回
        mock_is_favorite.return_value = True
        
        # 发送请求
        response = client.get('/favorites/check?device_id=device123&user_id=1&application_id=1')
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['is_favorite'] is True
    
    @patch.object(AIApplicationFavoritesService, 'is_favorite')
    def test_check_favorite_not_exists(self, mock_is_favorite, client):
        """测试检查收藏状态 - 未收藏"""
        # 模拟服务层返回
        mock_is_favorite.return_value = False
        
        # 发送请求
        response = client.get('/favorites/check?device_id=device123&user_id=1&application_id=1')
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['is_favorite'] is False
    
    @patch.object(AIApplicationFavoritesService, 'get_user_favorites')
    def test_get_user_favorites_success(self, mock_get_user_favorites, client):
        """测试获取用户收藏列表成功"""
        # 模拟服务层返回
        mock_favorites = [
            MagicMock(dict=lambda: {'id': 1, 'application_id': 1, 'app_name': 'App1'}),
            MagicMock(dict=lambda: {'id': 2, 'application_id': 2, 'app_name': 'App2'})
        ]
        mock_get_user_favorites.return_value = (mock_favorites, 2)
        
        # 发送请求
        response = client.get('/favorites/list?device_id=device123&user_id=1&pageNo=1&pageSize=20')
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['total'] == 2
        assert len(data['data']['list']) == 2
    
    @patch.object(AIApplicationFavoritesService, 'get_user_favorites_count')
    def test_get_user_favorites_count(self, mock_get_count, client):
        """测试获取用户收藏总数"""
        # 模拟服务层返回
        mock_get_count.return_value = 5
        
        # 发送请求
        response = client.get('/favorites/count?type=user&device_id=device123&user_id=1')
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['count'] == 5
    
    @patch.object(AIApplicationFavoritesService, 'get_application_favorites_count')
    def test_get_application_favorites_count(self, mock_get_count, client):
        """测试获取应用收藏数量"""
        # 模拟服务层返回
        mock_get_count.return_value = 10
        
        # 发送请求
        response = client.get('/favorites/count?type=app&application_id=1')
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['count'] == 10


class TestAIApplicationFavoritesService:
    """AI应用收藏服务测试类"""
    
    def test_favorites_do_validation(self):
        """测试数据对象验证"""
        # 测试有效数据
        valid_data = {
            'device_id': 'device123',
            'user_id': 1,
            'application_id': 1,
            'tenant_id': '8'
        }
        
        favorite_do = AIApplicationFavoritesDO(**valid_data)
        assert favorite_do.device_id == 'device123'
        assert favorite_do.user_id == 1
        assert favorite_do.application_id == 1
        assert favorite_do.tenant_id == '8'
    
    def test_favorites_do_defaults(self):
        """测试数据对象默认值"""
        minimal_data = {
            'device_id': 'device123',
            'user_id': 1,
            'application_id': 1
        }
        
        favorite_do = AIApplicationFavoritesDO(**minimal_data)
        assert favorite_do.tenant_id == '8'  # 默认值
        assert favorite_do.creator is None
        assert favorite_do.updater is None
