"""
源文件API测试用例
演示如何使用源文件的各种功能
"""
import unittest
import json
from flask import Flask
from flask_testing import TestCase
from source_files import source_files_bp
from source_files.model.source_files_model import db, UploadStatus


class SourceFileTestCase(TestCase):
    """源文件测试用例"""
    
    def create_app(self):
        """创建测试应用"""
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        # 注册蓝图
        app.register_blueprint(source_files_bp, url_prefix='/api/v1/source-files')
        
        # 初始化数据库
        db.init_app(app)
        with app.app_context():
            db.create_all()
        
        return app
    
    def setUp(self):
        """测试前置设置"""
        self.knowledge_spaces_id = 1001
        self.creator_id = 2001
        
        # 测试文件数据
        self.test_file_data = {
            "knowledgeSpacesId": self.knowledge_spaces_id,
            "fileName": "test_file_20231201_001.pdf",
            "originalFileName": "测试文档.pdf",
            "url": "https://example.com/files/test_file_20231201_001.pdf",
            "fileFormat": "application/pdf",
            "fileSuffix": ".pdf",
            "fileSize": 1024000,
            "uploadStatus": UploadStatus.COMPLETED,
            "creator": self.creator_id
        }
        
    def tearDown(self):
        """测试后清理"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_create_file(self):
        """测试创建文件记录"""
        response = self.client.post('/api/v1/source-files', 
                                  json=self.test_file_data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['originalFileName'], '测试文档.pdf')
        self.assertEqual(result['data']['fileSize'], 1024000)
        self.assertEqual(result['data']['uploadStatus'], UploadStatus.COMPLETED)
    
    def test_create_file_duplicate_name(self):
        """测试创建重名文件"""
        # 先创建一个文件
        self.client.post('/api/v1/source-files', json=self.test_file_data)
        
        # 尝试创建同名文件
        response = self.client.post('/api/v1/source-files', json=self.test_file_data)
        
        self.assertEqual(response.status_code, 409)
        result = json.loads(response.data)
        self.assertFalse(result['success'])
        self.assertIn('已存在', result['message'])
    
    def test_get_file_by_id(self):
        """测试根据ID获取文件"""
        # 先创建文件
        create_response = self.client.post('/api/v1/source-files', json=self.test_file_data)
        create_result = json.loads(create_response.data)
        file_id = create_result['data']['id']
        
        # 获取文件
        response = self.client.get(f'/api/v1/source-files/{file_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['id'], file_id)
        self.assertEqual(result['data']['originalFileName'], '测试文档.pdf')
    
    def test_get_files_by_knowledge_space(self):
        """测试根据知识空间获取文件列表"""
        # 创建多个文件
        files_data = [
            {**self.test_file_data, "fileName": "file1.pdf", "originalFileName": "文件1.pdf"},
            {**self.test_file_data, "fileName": "file2.pdf", "originalFileName": "文件2.pdf"},
            {**self.test_file_data, "fileName": "file3.pdf", "originalFileName": "文件3.pdf"}
        ]
        
        for file_data in files_data:
            self.client.post('/api/v1/source-files', json=file_data)
        
        # 获取文件列表
        response = self.client.get(f'/api/v1/source-files/knowledge-space/{self.knowledge_spaces_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(len(result['data']), 3)
    
    def test_search_files(self):
        """测试搜索文件"""
        # 创建测试数据
        files_data = [
            {**self.test_file_data, "fileName": "doc1.pdf", "originalFileName": "技术文档1.pdf"},
            {**self.test_file_data, "fileName": "doc2.docx", "originalFileName": "产品文档.docx", 
             "fileFormat": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 
             "fileSuffix": ".docx"},
            {**self.test_file_data, "fileName": "img1.png", "originalFileName": "截图.png", 
             "fileFormat": "image/png", "fileSuffix": ".png", "fileSize": 512000}
        ]
        
        for file_data in files_data:
            self.client.post('/api/v1/source-files', json=file_data)
        
        # 搜索PDF文件
        response = self.client.get('/api/v1/source-files?fileFormat=application/pdf&page=1&pageSize=10')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(len(result['data']), 2)  # 应该有2个PDF文件
        self.assertEqual(result['total'], 2)
    
    def test_update_file(self):
        """测试更新文件"""
        # 先创建文件
        create_response = self.client.post('/api/v1/source-files', json=self.test_file_data)
        create_result = json.loads(create_response.data)
        file_id = create_result['data']['id']
        
        # 更新文件信息
        update_data = {
            "originalFileName": "更新后的文档.pdf",
            "fileSize": 2048000,
            "updater": self.creator_id
        }
        
        response = self.client.put(f'/api/v1/source-files/{file_id}', 
                                 json=update_data,
                                 content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['originalFileName'], '更新后的文档.pdf')
        self.assertEqual(result['data']['fileSize'], 2048000)
    
    def test_update_upload_status(self):
        """测试更新上传状态"""
        # 先创建上传中的文件
        file_data = {**self.test_file_data, "uploadStatus": UploadStatus.UPLOADING}
        create_response = self.client.post('/api/v1/source-files', json=file_data)
        create_result = json.loads(create_response.data)
        file_id = create_result['data']['id']
        
        # 更新状态为已完成
        response = self.client.put(f'/api/v1/source-files/{file_id}/status?uploadStatus={UploadStatus.COMPLETED}&updater={self.creator_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertIn('已完成', result['message'])
    
    def test_batch_update_status(self):
        """测试批量更新状态"""
        # 创建多个上传中的文件
        file_ids = []
        for i in range(3):
            file_data = {
                **self.test_file_data, 
                "fileName": f"batch_file_{i}.pdf",
                "uploadStatus": UploadStatus.UPLOADING
            }
            create_response = self.client.post('/api/v1/source-files', json=file_data)
            create_result = json.loads(create_response.data)
            file_ids.append(create_result['data']['id'])
        
        # 批量更新状态
        batch_data = {
            "fileIds": file_ids,
            "uploadStatus": UploadStatus.COMPLETED,
            "updater": self.creator_id
        }
        
        response = self.client.put('/api/v1/source-files/batch/status', 
                                 json=batch_data,
                                 content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertIn('成功更新3个', result['message'])
    
    def test_delete_file(self):
        """测试删除文件"""
        # 先创建文件
        create_response = self.client.post('/api/v1/source-files', json=self.test_file_data)
        create_result = json.loads(create_response.data)
        file_id = create_result['data']['id']
        
        # 软删除文件
        response = self.client.delete(f'/api/v1/source-files/{file_id}?deleter={self.creator_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        
        # 验证文件已被标记为删除 - 获取应该返回404
        get_response = self.client.get(f'/api/v1/source-files/{file_id}')
        self.assertEqual(get_response.status_code, 404)
    
    def test_batch_delete_files(self):
        """测试批量删除文件"""
        # 创建多个文件
        file_ids = []
        for i in range(3):
            file_data = {**self.test_file_data, "fileName": f"delete_file_{i}.pdf"}
            create_response = self.client.post('/api/v1/source-files', json=file_data)
            create_result = json.loads(create_response.data)
            file_ids.append(create_result['data']['id'])
        
        # 批量删除
        batch_data = {
            "fileIds": file_ids,
            "deleter": self.creator_id
        }
        
        response = self.client.delete('/api/v1/source-files/batch', 
                                    json=batch_data,
                                    content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertIn('删除数量: 3', result['message'])
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        # 创建不同状态的文件
        files_data = [
            {**self.test_file_data, "fileName": "completed1.pdf", "uploadStatus": UploadStatus.COMPLETED},
            {**self.test_file_data, "fileName": "completed2.pdf", "uploadStatus": UploadStatus.COMPLETED},
            {**self.test_file_data, "fileName": "uploading1.pdf", "uploadStatus": UploadStatus.UPLOADING},
            {**self.test_file_data, "fileName": "failed1.pdf", "uploadStatus": UploadStatus.FAILED}
        ]
        
        for file_data in files_data:
            self.client.post('/api/v1/source-files', json=file_data)
        
        # 获取统计
        response = self.client.get(f'/api/v1/source-files/statistics?knowledgeSpacesId={self.knowledge_spaces_id}')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['totalCount'], 4)
        self.assertEqual(result['data']['completedCount'], 2)
        self.assertEqual(result['data']['uploadingCount'], 1)
        self.assertEqual(result['data']['failedCount'], 1)
    
    def test_validate_file_upload(self):
        """测试文件上传验证"""
        # 有效文件验证
        valid_data = {
            "fileFormat": "application/pdf",
            "fileSize": 1024000  # 1MB
        }
        
        response = self.client.post('/api/v1/source-files/validate', 
                                  json=valid_data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        
        # 无效格式验证
        invalid_data = {
            "fileFormat": "invalid/format",
            "fileSize": 1024000
        }
        
        response = self.client.post('/api/v1/source-files/validate', 
                                  json=invalid_data,
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.data)
        self.assertFalse(result['success'])
    
    def test_get_files_by_format(self):
        """测试根据格式获取文件"""
        # 创建不同格式的文件
        pdf_file = {**self.test_file_data, "fileName": "test.pdf"}
        docx_file = {
            **self.test_file_data, 
            "fileName": "test.docx",
            "fileFormat": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "fileSuffix": ".docx"
        }
        
        self.client.post('/api/v1/source-files', json=pdf_file)
        self.client.post('/api/v1/source-files', json=docx_file)
        
        # 获取PDF文件
        response = self.client.get('/api/v1/source-files/format/application%2Fpdf')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])
        self.assertEqual(len(result['data']), 1)
        self.assertEqual(result['data'][0]['fileFormat'], 'application/pdf')


if __name__ == '__main__':
    unittest.main()