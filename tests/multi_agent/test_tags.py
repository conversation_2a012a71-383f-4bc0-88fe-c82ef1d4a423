#!/usr/bin/env python3
"""
测试标签功能的基本导入和语法检查
"""

def test_imports():
    """测试模块导入"""
    try:
        print("测试标签模型导入...")
        from multi_agent.model.ai_tags_model import (
            AITagsModel, 
            AIApplicationTagsModel,
            AITagsDO, 
            AITagsVO,
            AIApplicationTagsDO,
            AIApplicationTagsVO
        )
        print("✓ 标签模型导入成功")
        
        print("测试标签服务导入...")
        from multi_agent.service.ai_tags_service import AITagsService
        print("✓ 标签服务导入成功")
        
        print("测试标签控制器导入...")
        from multi_agent.controller.ai_tags_controller import ai_tags_bp
        print("✓ 标签控制器导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    try:
        from multi_agent.model.ai_tags_model import AITagsDO, AITagsVO
        
        # 测试DO模型
        tag_do = AITagsDO(
            name="测试标签",
            slug="test-tag",
            description="这是一个测试标签",
            color="#FF0000"
        )
        print(f"✓ 创建标签DO成功: {tag_do.name}")
        
        # 测试VO模型
        tag_vo = AITagsVO(
            id=1,
            name="测试标签",
            slug="test-tag",
            description="这是一个测试标签",
            color="#FF0000"
        )
        print(f"✓ 创建标签VO成功: {tag_vo.name}")
        
        return True
    except Exception as e:
        print(f"❌ 模型创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_service_creation():
    """测试服务创建"""
    try:
        from multi_agent.service.ai_tags_service import AITagsService
        
        service = AITagsService()
        print("✓ 标签服务创建成功")
        
        # 测试方法是否存在
        methods = [
            'get_all_tags',
            'get_tag_by_id', 
            'get_tag_by_slug',
            'create_tag',
            'update_tag',
            'delete_tag',
            'search_tags_with_count',
            'add_application_tag',
            'remove_application_tag',
            'get_applications_by_tag_slug',
            'get_application_tags'
        ]
        
        for method in methods:
            if hasattr(service, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 服务创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_controller_creation():
    """测试控制器创建"""
    try:
        from multi_agent.controller.ai_tags_controller import ai_tags_bp
        
        print(f"✓ 标签控制器Blueprint创建成功: {ai_tags_bp.name}")
        
        # 检查路由是否注册
        routes = []
        for rule in ai_tags_bp.url_map.iter_rules():
            routes.append(f"{rule.methods} {rule.rule}")
        
        print(f"✓ 注册的路由数量: {len(routes)}")
        for route in routes:
            print(f"  - {route}")
        
        return True
    except Exception as e:
        print(f"❌ 控制器创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("开始测试标签功能")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("模型创建测试", test_model_creation),
        ("服务创建测试", test_service_creation),
        ("控制器创建测试", test_controller_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
