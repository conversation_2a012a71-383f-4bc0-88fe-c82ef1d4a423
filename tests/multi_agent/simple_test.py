#!/usr/bin/env python3
"""
简单的语法检查测试
"""

def test_syntax():
    """测试语法"""
    try:
        # 测试编译标签模型
        with open('../../multi_agent/model/ai_tags_model.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, '../../multi_agent/model/ai_tags_model.py', 'exec')
        print("✓ ai_tags_model.py 语法正确")
        
        # 测试编译标签服务
        with open('../../multi_agent/service/ai_tags_service.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, '../../multi_agent/service/ai_tags_service.py', 'exec')
        print("✓ ai_tags_service.py 语法正确")
        
        # 测试编译标签控制器
        with open('../../multi_agent/controller/ai_tags_controller.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, '../../multi_agent/controller/ai_tags_controller.py', 'exec')
        print("✓ ai_tags_controller.py 语法正确")
        
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    if test_syntax():
        print("✓ 所有文件语法检查通过")
    else:
        print("❌ 语法检查失败")
