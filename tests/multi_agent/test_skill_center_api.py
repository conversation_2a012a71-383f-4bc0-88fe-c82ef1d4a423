#!/usr/bin/env python3
"""
技能中心API测试脚本
"""

import requests
import json
import sys

def test_skill_center_api():
    """测试技能中心API接口"""
    
    # API基础URL - 根据实际部署情况调整
    base_url = "http://localhost:5000/multiagent/application"
    
    # 测试用例
    test_cases = [
        {
            "name": "获取技能中心应用列表 - 默认参数",
            "url": f"{base_url}/skill-center",
            "params": {}
        },
        {
            "name": "获取技能中心应用列表 - 带分页",
            "url": f"{base_url}/skill-center",
            "params": {"pageNo": 1, "pageSize": 10}
        },
        {
            "name": "获取技能中心应用列表 - 带关键词搜索",
            "url": f"{base_url}/skill-center",
            "params": {"keyword": "AI", "pageNo": 1, "pageSize": 20}
        },
        {
            "name": "获取技能中心应用列表 - 带状态过滤",
            "url": f"{base_url}/skill-center",
            "params": {"status": 1, "pageNo": 1, "pageSize": 20}
        },
        {
            "name": "获取技能中心应用列表 - 带应用类型过滤",
            "url": f"{base_url}/skill-center",
            "params": {"app_type": 1, "pageNo": 1, "pageSize": 20}
        }
    ]
    
    print("开始测试技能中心API接口...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print(f"URL: {test_case['url']}")
        print(f"参数: {test_case['params']}")
        
        try:
            response = requests.get(test_case['url'], params=test_case['params'], timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("响应成功!")
                print(f"消息: {data.get('message', 'N/A')}")
                
                # 检查分页信息
                if 'data' in data:
                    page_data = data['data']
                    print(f"总数: {page_data.get('total', 'N/A')}")
                    print(f"当前页: {page_data.get('pageNo', 'N/A')}")
                    print(f"每页大小: {page_data.get('pageSize', 'N/A')}")
                    print(f"返回记录数: {len(page_data.get('list', []))}")
                    
                    # 显示前几条记录的基本信息
                    applications = page_data.get('list', [])
                    if applications:
                        print("前3条记录:")
                        for j, app in enumerate(applications[:3], 1):
                            print(f"  {j}. ID: {app.get('id')}, 名称: {app.get('name')}, "
                                  f"技能中心: {app.get('is_skill_center')}, 状态: {app.get('status')}")
                else:
                    print("响应数据格式异常")
            else:
                print(f"请求失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON解析异常: {e}")
        except Exception as e:
            print(f"其他异常: {e}")
        
        print("-" * 40)
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_skill_center_api()
