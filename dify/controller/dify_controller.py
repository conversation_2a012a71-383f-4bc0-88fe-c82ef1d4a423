"""
Dify控制器
提供REST API端点
"""
import logging
from flask import Blueprint, request
from dify.service.dify_service import DifyService
from utils.request import get_str_param
from utils.response_utils import success_response, error_response


# 创建蓝图
dify_bp = Blueprint('dify', __name__)

# 初始化服务
dify_service = DifyService()


@dify_bp.route('/users/<user_id>/conversations', methods=['GET'])
def get_user_conversations_with_messages(user_id: str):
    """
    根据用户信息查询对话会话记录
    
    Args:
        user_id: 用户ID  对应数据库end_users表的Id
        
    Query Parameters:
        appId: 应用ID（可选）
        limit: 限制数量（默认20）
        offset: 偏移量（默认0）
        
    Returns:
        JSON响应包含用户信息、对话列表和消息
    """
    try:
        # 获取查询参数
        app_id = request.args.get('appId')
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        
        # 参数验证
        if limit > 100:
            return error_response('limit不能超过100', 400)
        if offset < 0:
            return error_response('offset不能小于0', 400)
        
        # 调用服务
        result = dify_service.get_user_conversations_with_messages(
            user_id=user_id,
            app_id=app_id,
            limit=limit,
            offset=offset
        )
        
        if 'error' in result:
            return error_response(result['error'], 500)
        
        return success_response(result, '成功获取用户对话和消息记录')
        
    except ValueError as e:
        return error_response(f'参数错误: {str(e)}', 400)
    except Exception as e:
        logging.error(f"获取用户对话和消息记录失败: {e}")
        return error_response('服务器内部错误', 500)


@dify_bp.route('/messages', methods=['GET'])
def get_conversation_messages():
    """
    根据对话会话查询对话消息记录
    
    Args:
        conversation_id: 对话ID
        
    Query Parameters:
        limit: 限制数量（默认50）
        offset: 偏移量（默认0）
        include_context: 是否包含上下文信息（默认true）
        
    Returns:
        JSON响应包含对话信息和消息列表
    """
    try:
        # 获取查询参数
        user = get_str_param('user')
        conversation_id = get_str_param('conversationId')
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))
        include_context = request.args.get('include_context', 'true').lower() == 'true'
        
        # 参数验证
        if limit > 200:
            return error_response('limit不能超过200', 400)
        if offset < 0:
            return error_response('offset不能小于0', 400)
        
        # 调用服务
        result = dify_service.get_conversation_messages(
            conversation_id=conversation_id,
            limit=limit,
            offset=offset,
            include_context=include_context
        )
        
        if 'error' in result:
            return error_response(result['error'], 404 if '不存在' in result['error'] else 500)
        
        return success_response(result, '成功获取对话消息记录')
        
    except ValueError as e:
        return error_response(f'参数错误: {str(e)}', 400)
    except Exception as e:
        logging.error(f"获取对话消息记录失败: {e}")
        return error_response('服务器内部错误', 500)


@dify_bp.route('/conversations/search', methods=['POST'])
def search_conversations():
    """
    搜索对话会话
    
    Request Body:
        {
            "appId": "应用ID（可选）",
            "fromSource": "来源（可选）",
            "fromEndUserId": "终端用户ID（可选）",
            "fromAccountId": "账户ID（可选）",
            "status": "状态（可选）",
            "mode": "模式（可选）",
            "nameKeyword": "名称关键词（可选）",
            "startTime": "开始时间（可选）",
            "endTime": "结束时间（可选）",
            "limit": 20,
            "offset": 0
        }
        
    Returns:
        JSON响应包含搜索结果
    """
    try:
        # 获取请求数据
        search_params = request.get_json() or {}
        
        # 参数验证
        limit = search_params.get('limit', 20)
        offset = search_params.get('offset', 0)
        
        if limit > 100:
            return error_response('limit不能超过100', 400)
        if offset < 0:
            return error_response('offset不能小于0', 400)
        
        # 调用服务
        result = dify_service.search_user_conversations(search_params)
        
        if 'error' in result:
            return error_response(result['error'], 500)
        
        return success_response(result, '成功搜索对话会话')
        
    except Exception as e:
        logging.error(f"搜索对话会话失败: {e}")
        return error_response('服务器内部错误', 500)


@dify_bp.route('/users/<user_id>/statistics', methods=['GET'])
def get_user_statistics(user_id: str):
    """
    获取用户统计信息
    
    Args:
        user_id: 用户ID
        
    Query Parameters:
        appId: 应用ID（可选）
        
    Returns:
        JSON响应包含用户统计信息
    """
    try:
        # 获取查询参数
        app_id = request.args.get('appId')
        
        # 调用服务
        result = dify_service.get_user_statistics(
            user_id=user_id,
            app_id=app_id
        )
        
        if 'error' in result:
            return error_response(result['error'], 404 if '不存在' in result['error'] else 500)
        
        return success_response(result, '成功获取用户统计信息')
        
    except Exception as e:
        logging.error(f"获取用户统计信息失败: {e}")
        return error_response('服务器内部错误', 500)


@dify_bp.route('/conversations', methods=['GET'])
def get_conversations_by_session_id():
    """
    根据session_id查询对话会话

    Args:
        session_id: 会话ID

    Query Parameters:
        appId: 应用ID（可选）
        limit: 限制数量（默认20）
        offset: 偏移量（默认0）

    Returns:
        JSON响应包含用户信息、对话列表和消息
    """
    try:
        # 获取查询参数
        session_id = request.args.get('user')
        app_id = request.args.get('appId')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        # 参数验证
        if limit > 100:
            return error_response('limit不能超过100', 400)
        if offset < 0:
            return error_response('offset不能小于0', 400)

        # 调用服务
        result = dify_service.get_conversations_by_session_id(
            session_id=session_id,
            app_id=app_id,
            limit=limit,
            offset=offset
        )

        if 'error' in result:
            return error_response(result['error'], 500)

        return success_response(result, '成功根据session_id获取对话和消息记录')

    except ValueError as e:
        return error_response(f'参数错误: {str(e)}', 400)
    except Exception as e:
        logging.error(f"根据session_id获取对话和消息记录失败: {e}")
        return error_response('服务器内部错误', 500)
