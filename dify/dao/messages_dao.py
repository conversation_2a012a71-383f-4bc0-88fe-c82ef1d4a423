"""
消息数据访问对象
提供messages表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy import and_, desc, asc
from sqlalchemy.exc import SQLAlchemyError
from dify.model.dify_models import MessagesModel
from multi_agent.model.ai_application_model import db


class MessagesDAO:
    """消息数据访问对象"""
    
    @staticmethod
    def create(message_data: Dict[str, Any]) -> Optional[MessagesModel]:
        """
        创建消息
        
        Args:
            message_data: 消息数据字典
            
        Returns:
            创建的消息模型对象，失败返回None
        """
        try:
            message = MessagesModel(**message_data)
            # 使用绑定到dify数据库的会话
            session = db.session
            session.add(message)
            session.commit()
            logging.info(f"成功创建消息: {message.id}")
            return message
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建消息失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(message_id: str) -> Optional[MessagesModel]:
        """
        根据ID获取消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            消息模型对象，不存在返回None
        """
        try:
            # 使用绑定到dify数据库的查询
            return db.session.query(MessagesModel).filter(
                MessagesModel.id == message_id
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取消息失败: {e}")
            return None
    
    @staticmethod
    def get_by_conversation_id(conversation_id: str, limit: int = 100, 
                              offset: int = 0, order_desc: bool = True) -> List[MessagesModel]:
        """
        根据对话ID获取消息列表
        
        Args:
            conversation_id: 对话ID
            limit: 限制数量
            offset: 偏移量
            order_desc: 是否降序排列
            
        Returns:
            消息模型对象列表
        """
        try:
            query = db.session.query(MessagesModel).filter(
                MessagesModel.conversation_id == conversation_id
            )
            
            if order_desc:
                query = query.order_by(desc(MessagesModel.created_at))
            else:
                query = query.order_by(asc(MessagesModel.created_at))
            
            return query.offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据对话ID获取消息列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_app_id(app_id: str, limit: int = 100, offset: int = 0) -> List[MessagesModel]:
        """
        根据应用ID获取消息列表
        
        Args:
            app_id: 应用ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            消息模型对象列表
        """
        try:
            return db.session.query(MessagesModel).filter(
                MessagesModel.app_id == app_id
            ).order_by(desc(MessagesModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取消息列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_end_user_id(end_user_id: str, app_id: str = None,
                          limit: int = 100, offset: int = 0) -> List[MessagesModel]:
        """
        根据终端用户ID获取消息列表
        
        Args:
            end_user_id: 终端用户ID
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            消息模型对象列表
        """
        try:
            query = db.session.query(MessagesModel).filter(
                MessagesModel.from_end_user_id == end_user_id
            )
            if app_id:
                query = query.filter(MessagesModel.app_id == app_id)
            
            return query.order_by(desc(MessagesModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据终端用户ID获取消息列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_account_id(account_id: str, app_id: str = None,
                         limit: int = 100, offset: int = 0) -> List[MessagesModel]:
        """
        根据账户ID获取消息列表
        
        Args:
            account_id: 账户ID
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            消息模型对象列表
        """
        try:
            query = db.session.query(MessagesModel).filter(
                MessagesModel.from_account_id == account_id
            )
            if app_id:
                query = query.filter(MessagesModel.app_id == app_id)
            
            return query.order_by(desc(MessagesModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据账户ID获取消息列表失败: {e}")
            return []
    
    @staticmethod
    def search_messages(app_id: str = None, conversation_id: str = None,
                       from_source: str = None, from_end_user_id: str = None,
                       from_account_id: str = None, status: str = None,
                       query_keyword: str = None, answer_keyword: str = None,
                       start_time: datetime = None, end_time: datetime = None,
                       limit: int = 100, offset: int = 0) -> List[MessagesModel]:
        """
        搜索消息
        
        Args:
            app_id: 应用ID
            conversation_id: 对话ID
            from_source: 来源
            from_end_user_id: 来源终端用户ID
            from_account_id: 来源账户ID
            status: 状态
            query_keyword: 查询关键词
            answer_keyword: 回答关键词
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            消息模型对象列表
        """
        try:
            query = db.session.query(MessagesModel)
            
            # 构建查询条件
            conditions = []
            if app_id:
                conditions.append(MessagesModel.app_id == app_id)
            if conversation_id:
                conditions.append(MessagesModel.conversation_id == conversation_id)
            if from_source:
                conditions.append(MessagesModel.from_source == from_source)
            if from_end_user_id:
                conditions.append(MessagesModel.from_end_user_id == from_end_user_id)
            if from_account_id:
                conditions.append(MessagesModel.from_account_id == from_account_id)
            if status:
                conditions.append(MessagesModel.status == status)
            if query_keyword:
                conditions.append(MessagesModel.query.ilike(f'%{query_keyword}%'))
            if answer_keyword:
                conditions.append(MessagesModel.answer.ilike(f'%{answer_keyword}%'))
            if start_time:
                conditions.append(MessagesModel.created_at >= start_time)
            if end_time:
                conditions.append(MessagesModel.created_at <= end_time)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(desc(MessagesModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"搜索消息失败: {e}")
            return []
    
    @staticmethod
    def get_conversation_messages_with_context(conversation_id: str, 
                                             current_message_id: str = None,
                                             context_size: int = 10) -> List[MessagesModel]:
        """
        获取对话的上下文消息
        
        Args:
            conversation_id: 对话ID
            current_message_id: 当前消息ID（可选）
            context_size: 上下文大小
            
        Returns:
            消息模型对象列表
        """
        try:
            query = db.session.query(MessagesModel).filter(
                MessagesModel.conversation_id == conversation_id
            )
            
            if current_message_id:
                # 获取当前消息的创建时间
                current_message = db.session.query(MessagesModel).filter(
                    MessagesModel.id == current_message_id
                ).first()
                
                if current_message:
                    # 获取当前消息之前的消息作为上下文
                    query = query.filter(
                        MessagesModel.created_at <= current_message.created_at
                    )
            
            return query.order_by(desc(MessagesModel.created_at)).limit(context_size).all()
        except SQLAlchemyError as e:
            logging.error(f"获取对话上下文消息失败: {e}")
            return []
    
    @staticmethod
    def update(message_id: str, update_data: Dict[str, Any]) -> bool:
        """
        更新消息信息
        
        Args:
            message_id: 消息ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(MessagesModel).filter(
                MessagesModel.id == message_id
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新消息: {message_id}")
                return True
            else:
                logging.warning(f"未找到要更新的消息: {message_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新消息失败: {e}")
            return False
    
    @staticmethod
    def delete(message_id: str) -> bool:
        """
        删除消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(MessagesModel).filter(
                MessagesModel.id == message_id
            ).delete()
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功删除消息: {message_id}")
                return True
            else:
                logging.warning(f"未找到要删除的消息: {message_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"删除消息失败: {e}")
            return False
    
    @staticmethod
    def count_by_conversation(conversation_id: str) -> int:
        """
        统计对话下的消息数量
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            消息数量
        """
        try:
            return db.session.query(MessagesModel).filter(
                MessagesModel.conversation_id == conversation_id
            ).count()
        except SQLAlchemyError as e:
            logging.error(f"统计对话消息数量失败: {e}")
            return 0
    
    @staticmethod
    def get_latest_message_by_conversation(conversation_id: str) -> Optional[MessagesModel]:
        """
        获取对话的最新消息
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            最新消息模型对象，不存在返回None
        """
        try:
            return db.session.query(MessagesModel).filter(
                MessagesModel.conversation_id == conversation_id
            ).order_by(desc(MessagesModel.created_at)).first()
        except SQLAlchemyError as e:
            logging.error(f"获取对话最新消息失败: {e}")
            return None
