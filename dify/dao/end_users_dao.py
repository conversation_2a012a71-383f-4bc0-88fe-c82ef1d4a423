"""
终端用户数据访问对象
提供end_users表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, desc
from sqlalchemy.exc import SQLAlchemyError
from dify.model.dify_models import EndUsersModel, MessagesDO, MessagesModel
from multi_agent.model.ai_application_model import db


class EndUsersDAO:
    """终端用户数据访问对象"""

    @staticmethod
    def create(end_user_data: Dict[str, Any]) -> Optional[EndUsersModel]:
        """
        创建终端用户
        
        Args:
            end_user_data: 用户数据字典
            
        Returns:
            创建的用户模型对象，失败返回None
        """
        try:
            end_user = EndUsersModel(**end_user_data)
            # 使用绑定到dify数据库的会话
            db.session.add(end_user)
            db.session.commit()
            logging.info(f"成功创建终端用户: {end_user.id}")
            return end_user
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建终端用户失败: {e}")
            return None

    @staticmethod
    def get_by_id(user_id: str) -> Optional[EndUsersModel]:
        """
        根据ID获取终端用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户模型对象，不存在返回None
        """
        try:
            return db.session.query(EndUsersModel).filter(
                EndUsersModel.id == user_id
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取终端用户失败: {e}")
            return None

    @staticmethod
    def get_by_session_id(session_id: str, limit: int = 100, offset: int = 0) -> Optional[EndUsersModel]:
        """
        根据会话ID获取终端用户
        
        Args:
            session_id: 会话ID
            user_type: 用户类型（可选）
            
        Returns:
            用户模型对象，不存在返回None
        """
        try:
            return db.session.query(EndUsersModel).filter(
                EndUsersModel.session_id == session_id
            ).order_by(desc(EndUsersModel.updated_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据会话ID获取终端用户失败: {e}")
            return None

    @staticmethod
    def get_by_external_user_id(external_user_id: str, tenant_id: str = None) -> Optional[EndUsersModel]:
        """
        根据外部用户ID获取终端用户
        
        Args:
            external_user_id: 外部用户ID
            tenant_id: 租户ID（可选）
            
        Returns:
            用户模型对象，不存在返回None
        """
        try:
            query = EndUsersModel.query.filter(
                EndUsersModel.external_user_id == external_user_id
            )
            if tenant_id:
                query = query.filter(EndUsersModel.tenant_id == tenant_id)
            return query.first()
        except SQLAlchemyError as e:
            logging.error(f"根据外部用户ID获取终端用户失败: {e}")
            return None

    @staticmethod
    def get_by_tenant_and_app(tenant_id: str, app_id: str = None,
                              limit: int = 100, offset: int = 0) -> List[EndUsersModel]:
        """
        根据租户ID和应用ID获取终端用户列表
        
        Args:
            tenant_id: 租户ID
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            用户模型对象列表
        """
        try:
            query = EndUsersModel.query.filter(
                EndUsersModel.tenant_id == tenant_id
            )
            if app_id:
                query = query.filter(EndUsersModel.app_id == app_id)

            return query.order_by(desc(EndUsersModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据租户和应用获取终端用户列表失败: {e}")
            return []

    @staticmethod
    def search_users(tenant_id: str = None, app_id: str = None, user_type: str = None,
                     is_anonymous: bool = None, name_keyword: str = None,
                     limit: int = 100, offset: int = 0) -> List[EndUsersModel]:
        """
        搜索终端用户
        
        Args:
            tenant_id: 租户ID
            app_id: 应用ID
            user_type: 用户类型
            is_anonymous: 是否匿名
            name_keyword: 姓名关键词
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            用户模型对象列表
        """
        try:
            query = EndUsersModel.query

            # 构建查询条件
            conditions = []
            if tenant_id:
                conditions.append(EndUsersModel.tenant_id == tenant_id)
            if app_id:
                conditions.append(EndUsersModel.app_id == app_id)
            if user_type:
                conditions.append(EndUsersModel.type == user_type)
            if is_anonymous is not None:
                conditions.append(EndUsersModel.is_anonymous == is_anonymous)
            if name_keyword:
                conditions.append(EndUsersModel.name.ilike(f'%{name_keyword}%'))

            if conditions:
                query = query.filter(and_(*conditions))

            return query.order_by(desc(EndUsersModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"搜索终端用户失败: {e}")
            return []

    @staticmethod
    def update(user_id: str, update_data: Dict[str, Any]) -> bool:
        """
        更新终端用户信息
        
        Args:
            user_id: 用户ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = EndUsersModel.query.filter(
                EndUsersModel.id == user_id
            ).update(update_data)

            if result > 0:
                db.session.commit()
                logging.info(f"成功更新终端用户: {user_id}")
                return True
            else:
                logging.warning(f"未找到要更新的终端用户: {user_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新终端用户失败: {e}")
            return False

    @staticmethod
    def delete(user_id: str) -> bool:
        """
        删除终端用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = EndUsersModel.query.filter(
                EndUsersModel.id == user_id
            ).delete()

            if result > 0:
                db.session.commit()
                logging.info(f"成功删除终端用户: {user_id}")
                return True
            else:
                logging.warning(f"未找到要删除的终端用户: {user_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"删除终端用户失败: {e}")
            return False

    @staticmethod
    def count_by_tenant(tenant_id: str, app_id: str = None) -> int:
        """
        统计租户下的用户数量
        
        Args:
            tenant_id: 租户ID
            app_id: 应用ID（可选）
            
        Returns:
            用户数量
        """
        try:
            query = EndUsersModel.query.filter(
                EndUsersModel.tenant_id == tenant_id
            )
            if app_id:
                query = query.filter(EndUsersModel.app_id == app_id)

            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"统计租户用户数量失败: {e}")
            return 0

    @staticmethod
    def get_by_app_id(app_id: str, limit: int = None, offset: int = 0) -> List[EndUsersModel]:
        """
        根据应用ID获取终端用户列表
        
        Args:
            app_id: 应用ID
            limit: 限制数量，None表示不限制
            offset: 偏移量
            
        Returns:
            用户模型对象列表
        """
        try:
            query = db.session.query(EndUsersModel).filter(
                EndUsersModel.app_id == app_id
            ).order_by(desc(EndUsersModel.created_at))
            
            if offset > 0:
                query = query.offset(offset)
            
            if limit is not None:
                query = query.limit(limit)
                
            return query.all()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取终端用户列表失败: {e}")
            return []

    @staticmethod
    def count_by_app_id(app_id: str) -> int:
        """
        根据应用ID获取终端用户列表

        Args:
            app_id: 应用ID

        Returns:
            用户模型对象列表
        """
        try:
            query = db.session.query(EndUsersModel).filter(
                EndUsersModel.app_id == app_id
            )
            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取终端用户列表失败: {e}")
            return []

    @staticmethod
    def message_count_by_app_id(app_id):
        try:
            query = db.session.query(MessagesModel).filter(
                MessagesModel.app_id == app_id
            )
            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取消息列表失败: {e}")
            return []