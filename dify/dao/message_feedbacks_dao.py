"""
消息反馈数据访问对象
提供message_feedbacks表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy import and_, desc, asc
from sqlalchemy.exc import SQLAlchemyError
from dify.model.dify_models import MessageFeedbacksModel
from multi_agent.model.ai_application_model import db


class MessageFeedbacksDAO:
    """消息反馈数据访问对象"""
    
    @staticmethod
    def get_by_message_ids(message_ids: List[str]) -> List[MessageFeedbacksModel]:
        """
        根据消息ID列表批量获取反馈信息
        
        Args:
            message_ids: 消息ID列表
            
        Returns:
            消息反馈列表
        """
        try:
            feedbacks = db.session.query(MessageFeedbacksModel).filter(
                    MessageFeedbacksModel.message_id.in_(message_ids)
                ).all()
            return feedbacks
        except SQLAlchemyError as e:
            logging.error(f"批量获取消息反馈失败: {e}")
            return []
    
    @staticmethod
    def get_by_message_id(message_id: str) -> Optional[MessageFeedbacksModel]:
        """
        根据消息ID获取反馈信息
        
        Args:
            message_id: 消息ID
            
        Returns:
            消息反馈对象，不存在返回None
        """
        try:
            feedback = db.session.query(MessageFeedbacksModel).filter_by(
                    message_id=message_id
                ).first()
            return feedback
        except SQLAlchemyError as e:
            logging.error(f"获取消息反馈失败: {e}")
            return None
    
    @staticmethod
    def create(feedback_data: Dict[str, Any]) -> Optional[MessageFeedbacksModel]:
        """
        创建消息反馈
        
        Args:
            feedback_data: 反馈数据字典
            
        Returns:
            创建的反馈模型对象，失败返回None
        """
        try:
            feedback = MessageFeedbacksModel(**feedback_data)
            session = db.session
            session.add(feedback)
            session.commit()
            logging.info(f"成功创建消息反馈: {feedback.id}")
            return feedback
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建消息反馈失败: {e}")
            return None
    
    @staticmethod
    def update(feedback_id: str, update_data: Dict[str, Any]) -> Optional[MessageFeedbacksModel]:
        """
        更新消息反馈
        
        Args:
            feedback_id: 反馈ID
            update_data: 更新数据字典
            
        Returns:
            更新后的反馈模型对象，失败返回None
        """
        try:
            session = db.session
            feedback = session.query(MessageFeedbacksModel).filter_by(id=feedback_id).first()
            if feedback:
                for key, value in update_data.items():
                    if hasattr(feedback, key):
                        setattr(feedback, key, value)
                session.commit()
                logging.info(f"成功更新消息反馈: {feedback_id}")
                return feedback
            else:
                logging.warning(f"消息反馈不存在: {feedback_id}")
                return None
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新消息反馈失败: {e}")
            return None