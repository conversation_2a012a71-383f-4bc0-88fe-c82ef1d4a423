"""
对话会话数据访问对象
提供conversations表的基础CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy import and_, desc
from sqlalchemy.exc import SQLAlchemyError
from dify.model.dify_models import ConversationsModel
from multi_agent.model.ai_application_model import db


class ConversationsDAO:
    """对话会话数据访问对象"""
    
    @staticmethod
    def create(conversation_data: Dict[str, Any]) -> Optional[ConversationsModel]:
        """
        创建对话会话
        
        Args:
            conversation_data: 对话数据字典
            
        Returns:
            创建的对话模型对象，失败返回None
        """
        try:
            conversation = ConversationsModel(**conversation_data)
            # 使用绑定到dify数据库的会话
            session = db.session
            session.add(conversation)
            session.commit()
            logging.info(f"成功创建对话会话: {conversation.id}")
            return conversation
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建对话会话失败: {e}")
            return None
    
    @staticmethod
    def get_by_id(conversation_id: str) -> Optional[ConversationsModel]:
        """
        根据ID获取对话会话
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            对话模型对象，不存在返回None
        """
        try:
            # 使用绑定到dify数据库的查询
            return db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.id == conversation_id,
                    ConversationsModel.is_deleted == False
                )
            ).first()
        except SQLAlchemyError as e:
            logging.error(f"根据ID获取对话会话失败: {e}")
            return None
    
    @staticmethod
    def get_by_app_id(app_id: str, limit: int = 100, offset: int = 0) -> List[ConversationsModel]:
        """
        根据应用ID获取对话会话列表
        
        Args:
            app_id: 应用ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            对话模型对象列表
        """
        try:
            return db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.app_id == app_id,
                    ConversationsModel.is_deleted == False
                )
            ).order_by(desc(ConversationsModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据应用ID获取对话会话列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_end_user_id(end_user_id: str, app_id: str = None, 
                          limit: int = 100, offset: int = 0) -> List[ConversationsModel]:
        """
        根据终端用户ID获取对话会话列表
        
        Args:
            end_user_id: 终端用户ID
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            对话模型对象列表
        """
        try:
            query = db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.from_end_user_id == end_user_id,
                    ConversationsModel.is_deleted == False
                )
            )
            if app_id:
                query = query.filter(ConversationsModel.app_id == app_id)
            
            return query.order_by(desc(ConversationsModel.updated_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据终端用户ID获取对话会话列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_end_user_ids(end_user_ids: List[str], app_id: str = None, 
                           limit: int = 100, offset: int = 0) -> List[ConversationsModel]:
        """
        根据多个终端用户ID获取对话会话列表（合并查询）
        
        Args:
            end_user_ids: 终端用户ID列表
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            对话模型对象列表，按更新时间倒序排序
        """
        try:
            query = db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.from_end_user_id.in_(end_user_ids),
                    ConversationsModel.is_deleted == False
                )
            )
            if app_id:
                query = query.filter(ConversationsModel.app_id == app_id)
            
            return query.order_by(desc(ConversationsModel.updated_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据多个终端用户ID获取对话会话列表失败: {e}")
            return []
    
    @staticmethod
    def get_by_account_id(account_id: str, app_id: str = None,
                         limit: int = 100, offset: int = 0) -> List[ConversationsModel]:
        """
        根据账户ID获取对话会话列表
        
        Args:
            account_id: 账户ID
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            对话模型对象列表
        """
        try:
            query = db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.from_account_id == account_id,
                    ConversationsModel.is_deleted == False
                )
            )
            if app_id:
                query = query.filter(ConversationsModel.app_id == app_id)
            
            return query.order_by(desc(ConversationsModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"根据账户ID获取对话会话列表失败: {e}")
            return []
    
    @staticmethod
    def search_conversations(app_id: str = None, from_source: str = None, 
                           from_end_user_id: str = None, from_account_id: str = None,
                           status: str = None, mode: str = None, name_keyword: str = None,
                           start_time: datetime = None, end_time: datetime = None,
                           limit: int = 100, offset: int = 0) -> List[ConversationsModel]:
        """
        搜索对话会话
        
        Args:
            app_id: 应用ID
            from_source: 来源
            from_end_user_id: 来源终端用户ID
            from_account_id: 来源账户ID
            status: 状态
            mode: 模式
            name_keyword: 名称关键词
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            对话模型对象列表
        """
        try:
            query = db.session.query(ConversationsModel).filter(
                ConversationsModel.is_deleted == False
            )
            
            # 构建查询条件
            conditions = []
            if app_id:
                conditions.append(ConversationsModel.app_id == app_id)
            if from_source:
                conditions.append(ConversationsModel.from_source == from_source)
            if from_end_user_id:
                conditions.append(ConversationsModel.from_end_user_id == from_end_user_id)
            if from_account_id:
                conditions.append(ConversationsModel.from_account_id == from_account_id)
            if status:
                conditions.append(ConversationsModel.status == status)
            if mode:
                conditions.append(ConversationsModel.mode == mode)
            if name_keyword:
                conditions.append(ConversationsModel.name.ilike(f'%{name_keyword}%'))
            if start_time:
                conditions.append(ConversationsModel.created_at >= start_time)
            if end_time:
                conditions.append(ConversationsModel.created_at <= end_time)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            return query.order_by(desc(ConversationsModel.created_at)).offset(offset).limit(limit).all()
        except SQLAlchemyError as e:
            logging.error(f"搜索对话会话失败: {e}")
            return []
    
    @staticmethod
    def update(conversation_id: str, update_data: Dict[str, Any]) -> bool:
        """
        更新对话会话信息
        
        Args:
            conversation_id: 对话ID
            update_data: 更新数据字典
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.id == conversation_id,
                    ConversationsModel.is_deleted == False
                )
            ).update(update_data)
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功更新对话会话: {conversation_id}")
                return True
            else:
                logging.warning(f"未找到要更新的对话会话: {conversation_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新对话会话失败: {e}")
            return False
    
    @staticmethod
    def soft_delete(conversation_id: str) -> bool:
        """
        软删除对话会话
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(ConversationsModel).filter(
                ConversationsModel.id == conversation_id
            ).update({'is_deleted': True, 'updated_at': datetime.now()})
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功软删除对话会话: {conversation_id}")
                return True
            else:
                logging.warning(f"未找到要删除的对话会话: {conversation_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"软删除对话会话失败: {e}")
            return False
    
    @staticmethod
    def hard_delete(conversation_id: str) -> bool:
        """
        硬删除对话会话
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            删除成功返回True，失败返回False
        """
        try:
            result = db.session.query(ConversationsModel).filter(
                ConversationsModel.id == conversation_id
            ).delete()
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功硬删除对话会话: {conversation_id}")
                return True
            else:
                logging.warning(f"未找到要删除的对话会话: {conversation_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"硬删除对话会话失败: {e}")
            return False
    
    @staticmethod
    def count_by_app(app_id: str, from_source: str = None) -> int:
        """
        统计应用下的对话数量
        
        Args:
            app_id: 应用ID
            from_source: 来源（可选）
            
        Returns:
            对话数量
        """
        try:
            query = db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.app_id == app_id,
                    ConversationsModel.is_deleted == False
                )
            )
            if from_source:
                query = query.filter(ConversationsModel.from_source == from_source)
            
            return query.count()
        except SQLAlchemyError as e:
            logging.error(f"统计应用对话数量失败: {e}")
            return 0
    
    @staticmethod
    def increment_dialogue_count(conversation_id: str) -> bool:
        """
        增加对话轮数
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            result = db.session.query(ConversationsModel).filter(
                and_(
                    ConversationsModel.id == conversation_id,
                    ConversationsModel.is_deleted == False
                )
            ).update({
                'dialogue_count': ConversationsModel.dialogue_count + 1,
                'updated_at': datetime.now()
            })
            
            if result > 0:
                db.session.commit()
                logging.info(f"成功增加对话轮数: {conversation_id}")
                return True
            else:
                logging.warning(f"未找到要更新的对话会话: {conversation_id}")
                return False
        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"增加对话轮数失败: {e}")
            return False
