"""
Dify服务层
提供业务逻辑处理和复杂查询功能
"""
import logging
from typing import Dict, Any, List
from datetime import datetime
from dify.dao.end_users_dao import EndUsersDAO
from dify.dao.conversations_dao import ConversationsDAO
from dify.dao.messages_dao import MessagesDAO
from dify.dao.message_feedbacks_dao import MessageFeedbacksDAO
from multi_agent.service.ai_application_service import AIApplicationService


class DifyService:
    """Dify业务服务类"""
    
    def __init__(self):
        self.end_users_dao = EndUsersDAO()
        self.conversations_dao = ConversationsDAO()
        self.messages_dao = MessagesDAO()
        self.message_feedbacks_dao = MessageFeedbacksDAO()
        self.ai_application_service = AIApplicationService()

    def get_user_conversations_with_messages(self, user_id: str, app_id: str = None,
                                           limit: int = 20, offset: int = 0) -> Dict[str, Any]:
        """
        根据用户信息查询对话会话和对话消息记录
        
        Args:
            user_id: 用户ID（end_users表的id）
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            包含用户信息、对话列表和消息的字典
        """
        try:
            result = {
                'userInfo': None,
                'conversations': [],
                'totalConversations': 0,
                'totalMessages': 0
            }
            
            # 根据session_id查询用户信息
            user_info = self.end_users_dao.get_by_id(user_id)
            if user_info:
                result['userInfo'] = user_info.to_dict()
                conversations = self.conversations_dao.get_by_end_user_id(
                    user_id, app_id, limit, offset
                )
                result['totalConversations'] = len(conversations)
            else:
                # 如果不是end_user，尝试作为account_id查询
                conversations = self.conversations_dao.get_by_account_id(
                    user_id, app_id, limit, offset
                )
                result['totalConversations'] = len(conversations)
            
            # 为每个对话获取消息
            conversation_list = []
            total_messages = 0
            
            for conversation in conversations:
                conv_dict = conversation.to_dict()
                
                # 获取该对话的消息（最新的10条）
                messages = self.messages_dao.get_by_conversation_id(
                    conversation.id, limit=10, offset=0
                )
                conv_dict['messages'] = [msg.to_dict() for msg in messages]
                conv_dict['messageCount'] = len(messages)
                total_messages += len(messages)
                
                # 获取最新消息时间
                if messages:
                    conv_dict['latestMessageTime'] = messages[0].created_at.isoformat()
                
                conversation_list.append(conv_dict)
            
            result['conversations'] = conversation_list
            result['totalMessages'] = total_messages
            
            logging.info(f"成功获取用户对话和消息: user_id={user_id}, conversations={len(conversations)}")
            return result
            
        except Exception as e:
            logging.error(f"获取用户对话和消息失败: {e}")
            return {
                'userInfo': None,
                'conversations': [],
                'totalConversations': 0,
                'totalMessages': 0,
                'error': str(e)
            }
    
    def get_conversation_messages(self, conversation_id: str, limit: int = 50, 
                                offset: int = 0, include_context: bool = True) -> Dict[str, Any]:
        """
        根据对话会话查询对话消息记录
        
        Args:
            conversation_id: 对话ID
            limit: 限制数量
            offset: 偏移量
            include_context: 是否包含对话上下文信息
            
        Returns:
            包含对话信息和消息列表的字典
        """
        try:
            result = {
                'conversationInfo': None,
                'messages': [],
                'totalMessages': 0,
                'userInfo': None
            }
            
            # 获取对话信息
            conversation = self.conversations_dao.get_by_id(conversation_id)
            if not conversation:
                return {
                    'error': f'对话不存在: {conversation_id}',
                    'conversationInfo': None,
                    'messages': [],
                    'totalMessages': 0
                }
            
            result['conversationInfo'] = conversation.to_dict()
            
            # 获取用户信息
            if conversation.from_end_user_id:
                user_info = self.end_users_dao.get_by_id(conversation.from_end_user_id)
                if user_info:
                    result['userInfo'] = user_info.to_dict()
            
            # 获取消息列表
            messages = self.messages_dao.get_by_conversation_id(
                conversation_id, limit, offset, order_desc=False  # 按时间正序
            )
            
            # 批量获取消息的评分状态
            message_ids = [str(msg.id) for msg in messages]
            feedbacks = []
            if message_ids:
                feedbacks = self.message_feedbacks_dao.get_by_message_ids(message_ids)
            
            # 创建消息ID到评分的映射
            feedback_map = {str(feedback.message_id): feedback.rating for feedback in feedbacks}
            
            # 为消息添加评分状态
            messages_with_rating = []
            for msg in messages:
                msg_dict = msg.to_dict()
                msg_dict['rating'] = feedback_map.get(str(msg.id))
                messages_with_rating.append(msg_dict)
            
            result['messages'] = messages_with_rating
            result['totalMessages'] = self.messages_dao.count_by_conversation(conversation_id)
            
            # 如果需要上下文信息，添加统计数据
            if include_context:
                result['conversationStats'] = {
                    'totalTokens': sum(msg.message_tokens + msg.answer_tokens for msg in messages),
                    'totalCost': sum(float(msg.total_price or 0) for msg in messages),
                    'avgResponseTime': sum(msg.provider_response_latency for msg in messages) / len(messages) if messages else 0,
                    'messageCount': len(messages)
                }
            
            logging.info(f"成功获取对话消息: conversation_id={conversation_id}, messages={len(messages)}")
            return result
            
        except Exception as e:
            logging.error(f"获取对话消息失败: {e}")
            return {
                'error': str(e),
                'conversationInfo': None,
                'messages': [],
                'totalMessages': 0
            }
    
    def search_user_conversations(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        搜索用户对话
        
        Args:
            search_params: 搜索参数字典
            
        Returns:
            搜索结果字典
        """
        try:
            # 提取搜索参数
            app_id = search_params.get('app_id')
            from_source = search_params.get('from_source')
            from_end_user_id = search_params.get('from_end_user_id')
            from_account_id = search_params.get('from_account_id')
            status = search_params.get('status')
            mode = search_params.get('mode')
            name_keyword = search_params.get('name_keyword')
            start_time = search_params.get('start_time')
            end_time = search_params.get('end_time')
            limit = search_params.get('limit', 20)
            offset = search_params.get('offset', 0)
            
            # 转换时间格式
            if start_time and isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            if end_time and isinstance(end_time, str):
                end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            
            # 搜索对话
            conversations = self.conversations_dao.search_conversations(
                app_id=app_id,
                from_source=from_source,
                from_end_user_id=from_end_user_id,
                from_account_id=from_account_id,
                status=status,
                mode=mode,
                name_keyword=name_keyword,
                start_time=start_time,
                end_time=end_time,
                limit=limit,
                offset=offset
            )
            
            # 组装结果
            result = {
                'conversations': [],
                'totalCount': len(conversations),
                'searchParams': search_params
            }
            
            for conversation in conversations:
                conv_dict = conversation.to_dict()
                
                # 获取最新消息
                latest_message = self.messages_dao.get_latest_message_by_conversation(conversation.id)
                if latest_message:
                    conv_dict['latest_message'] = {
                        'id': str(latest_message.id),
                        'query': latest_message.query[:100] + '...' if len(latest_message.query) > 100 else latest_message.query,
                        'answer': latest_message.answer[:100] + '...' if len(latest_message.answer) > 100 else latest_message.answer,
                        'created_at': latest_message.created_at.isoformat()
                    }
                
                # 获取消息统计
                message_count = self.messages_dao.count_by_conversation(conversation.id)
                conv_dict['messageCount'] = message_count
                
                result['conversations'].append(conv_dict)
            
            logging.info(f"成功搜索用户对话: 找到{len(conversations)}个结果")
            return result
            
        except Exception as e:
            logging.error(f"搜索用户对话失败: {e}")
            return {
                'error': str(e),
                'conversations': [],
                'totalCount': 0
            }
    
    def get_user_statistics(self, user_id: str, app_id: str = None) -> Dict[str, Any]:
        """
        获取用户统计信息
        
        Args:
            user_id: 用户ID
            app_id: 应用ID（可选）
            
        Returns:
            用户统计信息字典
        """
        try:
            # 获取用户信息
            user_info = self.end_users_dao.get_by_id(user_id)
            if not user_info:
                return {'error': f'用户不存在: {user_id}'}
            
            # 获取对话统计
            conversations = self.conversations_dao.get_by_end_user_id(user_id, app_id, limit=1000)
            
            # 获取消息统计
            messages = self.messages_dao.get_by_end_user_id(user_id, app_id, limit=1000)
            
            # 计算统计数据
            total_tokens = sum(msg.message_tokens + msg.answer_tokens for msg in messages)
            total_cost = sum(float(msg.total_price or 0) for msg in messages)
            avg_response_time = sum(msg.provider_response_latency for msg in messages) / len(messages) if messages else 0
            
            # 按日期统计
            daily_stats = {}
            for msg in messages:
                date_key = msg.created_at.date().isoformat()
                if date_key not in daily_stats:
                    daily_stats[date_key] = {
                        'messageCount': 0,
                        'totalTokens': 0,
                        'totalCost': 0
                    }
                daily_stats[date_key]['messageCount'] += 1
                daily_stats[date_key]['totalTokens'] += msg.message_tokens + msg.answer_tokens
                daily_stats[date_key]['totalCost'] += float(msg.total_price or 0)
            
            result = {
                'userInfo': user_info.to_dict(),
                'statistics': {
                    'totalConversations': len(conversations),
                    'totalMessages': len(messages),
                    'totalTokens': total_tokens,
                    'totalCost': total_cost,
                    'avgResponseTime': avg_response_time,
                    'activeDays': len(daily_stats)
                },
                'dailyStats': daily_stats
            }
            
            logging.info(f"成功获取用户统计信息: user_id={user_id}")
            return result
            
        except Exception as e:
            logging.error(f"获取用户统计信息失败: {e}")
            return {'error': str(e)}
    
    def create_conversation(self, conversation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建对话会话
        
        Args:
            conversation_data: 对话数据
            
        Returns:
            创建结果
        """
        try:
            conversation = self.conversations_dao.create(conversation_data)
            if conversation:
                return {
                    'success': True,
                    'conversation': conversation.to_dict()
                }
            else:
                return {
                    'success': False,
                    'error': '创建对话失败'
                }
        except Exception as e:
            logging.error(f"创建对话失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建消息
        
        Args:
            message_data: 消息数据
            
        Returns:
            创建结果
        """
        try:
            message = self.messages_dao.create(message_data)
            if message:
                # 更新对话的轮数
                self.conversations_dao.increment_dialogue_count(message.conversation_id)
                
                return {
                    'success': True,
                    'message': message.to_dict()
                }
            else:
                return {
                    'success': False,
                    'error': '创建消息失败'
                }
        except Exception as e:
            logging.error(f"创建消息失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_conversations_by_session_id(self, session_id: str, app_id: str = None,
                                       limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """
        根据session_id查询对话会话和对话消息记录

        Args:
            session_id: 会话ID
            app_id: 应用ID（可选）
            limit: 限制数量
            offset: 偏移量

        Returns:
            包含用户信息、对话列表和消息的字典
        """
        try:
            result = {
                'user_info': None,
                'conversations': [],
                'totalConversations': 0,
                'totalMessages': 0
            }

            # 根据session_id查询用户信息
            # 先查询有过对话记录的所有智能体
            end_users_list = self.end_users_dao.get_by_session_id(session_id,limit,offset)
            if end_users_list:
                # 记录第一个用户信息用于返回
                result['user_info'] = end_users_list[0].to_dict() if end_users_list else None
                
                # 收集所有end_user_id
                end_user_ids = [user.id for user in end_users_list]
                
                # 一次性查询所有用户的会话记录（倒序）
                conversations = self.conversations_dao.get_by_end_user_ids(
                    end_user_ids, app_id, limit, offset
                )
                result['totalConversations'] = len(conversations)
                
                # 处理所有对话
                conversation_list = []
                total_messages = 0
                
                for conversation in conversations:
                    conv_dict = conversation.to_dict()
                    # 获取智能体对应的图标
                    aIApplicationVO = self.ai_application_service.get_application_by_dify_app_id(conv_dict["appId"])
                    conv_dict["apiKey"] = aIApplicationVO.apiKey if aIApplicationVO else None
                    conv_dict["apiBaseUrl"] = aIApplicationVO.apiBaseUrl if aIApplicationVO else None
                    conv_dict["appLogoUrl"] = aIApplicationVO.logoUrl if aIApplicationVO else None
                    conv_dict["isHasFile"] = aIApplicationVO.isHasFile if aIApplicationVO else None
                    conv_dict["agentName"] = aIApplicationVO.name if aIApplicationVO else None
                    # 获取该对话的消息（最新的30条）
                    # messages = self.messages_dao.get_by_conversation_id(
                    #     conversation.id, limit=10, offset=0
                    # )
                    # conv_dict['messages'] = [msg.to_dict() for msg in messages]
                    # conv_dict['messageCount'] = len(messages)
                    # total_messages += len(messages)
                    #
                    # # 获取最新消息时间
                    # if messages:
                    #     conv_dict['latestMessageTime'] = messages[0].created_at.isoformat()
                        
                    conversation_list.append(conv_dict)
                # result['totalMessages'] = total_messages
                result['conversations'] = conversation_list

                
                logging.info(f"成功根据session_id获取对话和消息: session_id={session_id}, conversations={len(conversations)}")
            else:
                logging.warning(f"未找到session_id为 {session_id} 的用户")

            return result

        except Exception as e:
            logging.error(f"根据session_id查询对话失败: {e}")
            return {
                'user_info': None,
                'conversations': [],
                'totalConversations': 0,
                'totalMessages': 0,
                'error': str(e)
            }

    def get_end_users_by_app_id(self, app_id: str) -> int:
        """
        获取app_id关联的用户总数
        
        Args:
            app_id: 应用ID
            
        Returns:
            用户总数
        """
        try:
            # 直接使用count方法获取总数
            total_count = self.end_users_dao.count_by_app_id(app_id)
            return total_count
        except Exception as e:
            logging.error(f"获取app_id={app_id}的用户总数失败: {e}")
            return 0

    def get_message_count_by_app_id(self, app_id: str):
        try:
            # 直接使用count方法获取总数
            total_count = self.end_users_dao.message_count_by_app_id(app_id)
            return total_count
        except Exception as e:
            logging.error(f"获取app_id={app_id}的消息总数失败: {e}")
            return 0
