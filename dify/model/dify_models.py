"""
Dify数据库模型定义
包含end_users、conversations、messages表的SQLAlchemy模型
使用独立的PostgreSQL数据库连接
"""
from typing import Optional, Dict, Any
import uuid
from sqlalchemy import Column, String, Boolean, Integer, DateTime, Text, Float, Numeric, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel

# 使用主应用的数据库实例，通过bind_key指定dify数据库
from multi_agent.model.ai_application_model import db


Base = declarative_base()

__all__ = ['EndUsersModel', 'ConversationsModel', 'MessagesModel', 'MessageFeedbacksModel']


class EndUsersModel(db.Model):
    """终端用户表SQLAlchemy模型"""
    __tablename__ = 'end_users'
    __bind_key__ = 'dify'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment='用户ID')
    tenant_id = Column(UUID(as_uuid=True), nullable=False, comment='租户ID')
    app_id = Column(UUID(as_uuid=True), nullable=True, comment='应用ID')
    type = Column(String(255), nullable=False, comment='用户类型')
    external_user_id = Column(String(255), nullable=True, comment='外部用户ID')
    name = Column(String(255), nullable=True, comment='用户名称')
    is_anonymous = Column(Boolean, default=True, nullable=False, comment='是否匿名用户')
    session_id = Column(String(255), nullable=False, comment='会话ID')
    created_at = Column(DateTime, default=func.current_timestamp(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'tenantId': str(self.tenant_id),
            'appId': str(self.app_id) if self.app_id else None,
            'type': self.type,
            'externalUserId': self.external_user_id,
            'name': self.name,
            'isAnonymous': self.is_anonymous,
            'sessionId': self.session_id,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None
        }


class ConversationsModel(db.Model):
    """对话会话表SQLAlchemy模型"""
    __tablename__ = 'conversations'
    __bind_key__ = 'dify'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment='对话ID')
    app_id = Column(UUID(as_uuid=True), nullable=False, comment='应用ID')
    app_model_config_id = Column(UUID(as_uuid=True), nullable=True, comment='应用模型配置ID')
    model_provider = Column(String(255), nullable=True, comment='模型提供商')
    override_model_configs = Column(Text, nullable=True, comment='覆盖模型配置')
    model_id = Column(String(255), nullable=True, comment='模型ID')
    mode = Column(String(255), nullable=False, comment='对话模式')
    name = Column(String(255), nullable=False, comment='对话名称')
    summary = Column(Text, nullable=True, comment='对话摘要')
    inputs = Column(JSON, nullable=False, comment='输入参数')
    introduction = Column(Text, nullable=True, comment='介绍')
    system_instruction = Column(Text, nullable=True, comment='系统指令')
    system_instruction_tokens = Column(Integer, default=0, nullable=False, comment='系统指令token数')
    status = Column(String(255), nullable=False, comment='状态')
    from_source = Column(String(255), nullable=False, comment='来源')
    from_end_user_id = Column(UUID(as_uuid=True), nullable=True, comment='来源终端用户ID')
    from_account_id = Column(UUID(as_uuid=True), nullable=True, comment='来源账户ID')
    read_at = Column(DateTime, nullable=True, comment='阅读时间')
    read_account_id = Column(UUID(as_uuid=True), nullable=True, comment='阅读账户ID')
    created_at = Column(DateTime, default=func.current_timestamp(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False, comment='更新时间')
    is_deleted = Column(Boolean, default=False, nullable=False, comment='是否删除')
    invoke_from = Column(String(255), nullable=True, comment='调用来源')
    dialogue_count = Column(Integer, default=0, nullable=False, comment='对话轮数')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'appId': str(self.app_id),
            'appModelConfigId': str(self.app_model_config_id) if self.app_model_config_id else None,
            'modelProvider': self.model_provider,
            'overrideModelConfigs': self.override_model_configs,
            'modelId': self.model_id,
            'mode': self.mode,
            'name': self.name,
            'summary': self.summary,
            'inputs': self.inputs,
            'introduction': self.introduction,
            'systemInstruction': self.system_instruction,
            'systemInstructionTokens': self.system_instruction_tokens,
            'status': self.status,
            'fromSource': self.from_source,
            'fromEndUserId': str(self.from_end_user_id) if self.from_end_user_id else None,
            'fromAccountId': str(self.from_account_id) if self.from_account_id else None,
            'readAt': self.read_at.isoformat() if self.read_at else None,
            'readAccountId': str(self.read_account_id) if self.read_account_id else None,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'isDeleted': self.is_deleted,
            'invokeFrom': self.invoke_from,
            'dialogueCount': self.dialogue_count
        }


class MessagesModel(db.Model):
    """消息表SQLAlchemy模型"""
    __tablename__ = 'messages'
    __bind_key__ = 'dify'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment='消息ID')
    app_id = Column(UUID(as_uuid=True), nullable=False, comment='应用ID')
    model_provider = Column(String(255), nullable=True, comment='模型提供商')
    model_id = Column(String(255), nullable=True, comment='模型ID')
    override_model_configs = Column(Text, nullable=True, comment='覆盖模型配置')
    conversation_id = Column(UUID(as_uuid=True), nullable=False, comment='对话ID')
    inputs = Column(JSON, nullable=False, comment='输入参数')
    query = Column(Text, nullable=False, comment='查询内容')
    message = Column(JSON, nullable=False, comment='消息内容')
    message_tokens = Column(Integer, default=0, nullable=False, comment='消息token数')
    message_unit_price = Column(Numeric(10, 4), nullable=False, comment='消息单价')
    answer = Column(Text, nullable=False, comment='回答内容')
    answer_tokens = Column(Integer, default=0, nullable=False, comment='回答token数')
    answer_unit_price = Column(Numeric(10, 4), nullable=False, comment='回答单价')
    provider_response_latency = Column(Float, default=0, nullable=False, comment='提供商响应延迟')
    total_price = Column(Numeric(10, 7), nullable=True, comment='总价格')
    currency = Column(String(255), nullable=False, comment='货币')
    from_source = Column(String(255), nullable=False, comment='来源')
    from_end_user_id = Column(UUID(as_uuid=True), nullable=True, comment='来源终端用户ID')
    from_account_id = Column(UUID(as_uuid=True), nullable=True, comment='来源账户ID')
    created_at = Column(DateTime, default=func.current_timestamp(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False, comment='更新时间')
    agent_based = Column(Boolean, default=False, nullable=False, comment='是否基于代理')
    message_price_unit = Column(Numeric(10, 7), default=0.001, nullable=False, comment='消息价格单位')
    answer_price_unit = Column(Numeric(10, 7), default=0.001, nullable=False, comment='回答价格单位')
    workflow_run_id = Column(UUID(as_uuid=True), nullable=True, comment='工作流运行ID')
    status = Column(String(255), default='normal', nullable=False, comment='状态')
    error = Column(Text, nullable=True, comment='错误信息')
    message_metadata = Column(Text, nullable=True, comment='消息元数据')
    invoke_from = Column(String(255), nullable=True, comment='调用来源')
    parent_message_id = Column(UUID(as_uuid=True), nullable=True, comment='父消息ID')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'appId': str(self.app_id),
            'modelProvider': self.model_provider,
            'modelId': self.model_id,
            'overrideModelConfigs': self.override_model_configs,
            'conversationId': str(self.conversation_id),
            'inputs': self.inputs,
            'query': self.query,
            'message': self.message,
            'messageTokens': self.message_tokens,
            'messageUnitPrice': float(self.message_unit_price),
            'answer': self.answer,
            'answerTokens': self.answer_tokens,
            'answerUnitPrice': float(self.answer_unit_price),
            'providerResponseLatency': self.provider_response_latency,
            'totalPrice': float(self.total_price) if self.total_price else None,
            'currency': self.currency,
            'fromSource': self.from_source,
            'fromEndUserId': str(self.from_end_user_id) if self.from_end_user_id else None,
            'fromAccountId': str(self.from_account_id) if self.from_account_id else None,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'agentBased': self.agent_based,
            'messagePriceUnit': float(self.message_price_unit),
            'answerPriceUnit': float(self.answer_price_unit),
            'workflowRunId': str(self.workflow_run_id) if self.workflow_run_id else None,
            'status': self.status,
            'error': self.error,
            'messageMetadata': self.message_metadata,
            'invokeFrom': self.invoke_from,
            'parentMessageId': str(self.parent_message_id) if self.parent_message_id else None
        }


class MessageFeedbacksModel(db.Model):
    """消息反馈表SQLAlchemy模型"""
    __tablename__ = 'message_feedbacks'
    __bind_key__ = 'dify'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment='反馈ID')
    app_id = Column(UUID(as_uuid=True), nullable=False, comment='应用ID')
    conversation_id = Column(UUID(as_uuid=True), nullable=False, comment='对话ID')
    message_id = Column(UUID(as_uuid=True), nullable=False, comment='消息ID')
    rating = Column(String(255), nullable=False, comment='评分')
    content = Column(Text, nullable=True, comment='反馈内容')
    from_source = Column(String(255), nullable=False, comment='来源')
    from_end_user_id = Column(UUID(as_uuid=True), nullable=True, comment='来源终端用户ID')
    from_account_id = Column(UUID(as_uuid=True), nullable=True, comment='来源账户ID')
    created_at = Column(DateTime, default=func.current_timestamp(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'appId': str(self.app_id),
            'conversationId': str(self.conversation_id),
            'messageId': str(self.message_id),
            'rating': self.rating,
            'content': self.content,
            'fromSource': self.from_source,
            'fromEndUserId': str(self.from_end_user_id) if self.from_end_user_id else None,
            'fromAccountId': str(self.from_account_id) if self.from_account_id else None,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None
        }


# Pydantic模型用于API请求/响应
class EndUsersDO(BaseModel):
    """终端用户数据对象"""
    id: Optional[str] = None
    tenantId: Optional[str] = None
    appId: Optional[str] = None
    type: Optional[str] = None
    externalUserId: Optional[str] = None
    name: Optional[str] = None
    isAnonymous: Optional[bool] = True
    sessionId: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None
    
    class Config:
        from_attributes = True


class ConversationsDO(BaseModel):
    """对话会话数据对象"""
    id: Optional[str] = None
    appId: Optional[str] = None
    appModelConfigId: Optional[str] = None
    modelProvider: Optional[str] = None
    overrideModelConfigs: Optional[str] = None
    modelId: Optional[str] = None
    mode: Optional[str] = None
    name: Optional[str] = None
    summary: Optional[str] = None
    inputs: Optional[Dict[str, Any]] = None
    introduction: Optional[str] = None
    systemInstruction: Optional[str] = None
    systemInstructionTokens: Optional[int] = 0
    status: Optional[str] = None
    fromSource: Optional[str] = None
    fromEndUserId: Optional[str] = None
    fromAccountId: Optional[str] = None
    readAt: Optional[str] = None
    readAccountId: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None
    isDeleted: Optional[bool] = False
    invokeFrom: Optional[str] = None
    dialogueCount: Optional[int] = 0
    
    class Config:
        from_attributes = True


class MessagesDO(BaseModel):
    """消息数据对象"""
    id: Optional[str] = None
    appId: Optional[str] = None
    modelProvider: Optional[str] = None
    modelId: Optional[str] = None
    overrideModelConfigs: Optional[str] = None
    conversationId: Optional[str] = None
    inputs: Optional[Dict[str, Any]] = None
    query: Optional[str] = None
    message: Optional[Dict[str, Any]] = None
    messageTokens: Optional[int] = 0
    messageUnitPrice: Optional[float] = None
    answer: Optional[str] = None
    answerTokens: Optional[int] = 0
    answerUnitPrice: Optional[float] = None
    providerResponseLatency: Optional[float] = 0
    totalPrice: Optional[float] = None
    currency: Optional[str] = None
    fromSource: Optional[str] = None
    fromEndUserId: Optional[str] = None
    fromAccountId: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None
    agentBased: Optional[bool] = False
    messagePriceUnit: Optional[float] = 0.001
    answerPriceUnit: Optional[float] = 0.001
    workflowRunId: Optional[str] = None
    status: Optional[str] = 'normal'
    error: Optional[str] = None
    messageMetadata: Optional[str] = None
    invokeFrom: Optional[str] = None
    parentMessageId: Optional[str] = None
    
    class Config:
        from_attributes = True


class MessageFeedbacksDO(BaseModel):
    """消息反馈数据对象"""
    id: Optional[str] = None
    appId: Optional[str] = None
    conversationId: Optional[str] = None
    messageId: Optional[str] = None
    rating: Optional[str] = None
    content: Optional[str] = None
    fromSource: Optional[str] = None
    fromEndUserId: Optional[str] = None
    fromAccountId: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None
    
    class Config:
        from_attributes = True
