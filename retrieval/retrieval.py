import hashlib
import logging
import os.path
import time

import requests
from dotenv import load_dotenv

import utils.oss_utils
from utils import qichacha
import re
import pdfkit

load_dotenv()


def qichacha_fuzzy_search(search_key):
    # 配置基础参数
    api_url = qichacha.SEARCH_API_URL
    app_key = qichacha.APP_KEY
    secret_key = qichacha.SECRET_KEY

    ## 初始化返回对象
    resultList = []

    try:

        timestamp = str(int(time.time()))  # 生成Unix时间戳（秒级）

        # 生成Token签名（MD5加密）
        raw_sign = app_key + timestamp + secret_key
        token = hashlib.md5(raw_sign.encode()).hexdigest().upper()

        # 构造请求头与参数
        headers = {
            "Token": token,
            "Timespan": timestamp
        }
        params = {
            "key": app_key,
            "searchKey": search_key,
            "pageIndex": "1"  # 默认第1页，可动态调整
        }

        # 发送GET请求
        try:
            response = requests.get(api_url, params=params, headers=headers)
            response.raise_for_status()  # 自动处理HTTP错误码
            # 解析JSON响应
            result = response.json()
            if result.get("Status") == "200":  # 状态码为200时，表示成功
                for data_dic in result.get("Result"):
                    data_dic_ = {}
                    for k, v in data_dic.items():
                        text = re.sub(r'[A-Z]', lambda m: m.group().lower(), k[0])
                        k_ = text + k[1:]
                        data_dic_[k_] = v

                    resultList.append(data_dic_)


            else:
                return []
        except requests.exceptions.RequestException as e:
            return []
    except Exception as e:
        return []

    return resultList


def toPdf(text):
    options = {
        'encoding': "UTF-8",
        'page-size': 'Letter',
        'margin-top': '0.75in',
        'margin-right': '0.75in',
        'margin-bottom': '0.75in',
        'margin-left': '0.75in',
    }
    timestamp = str(int(time.time()))  # 生成Unix时间戳（秒级）
    tempPath = "./temp/"
    fileName = timestamp + ".pdf"
    filePath = tempPath + fileName
    pdfkit.from_string(text, filePath, options=options)
    url = utils.oss_utils.upload_to_oss(filePath, fileName)
    try:
        if os.path.exists(filePath):
            os.rename(filePath)
            logging.info("删除文件成功")
        else:
            logging.info("文件不存在")
    except Exception as e:
        logging.error("删除文件失败")
    return url