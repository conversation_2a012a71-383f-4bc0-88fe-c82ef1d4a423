print("Testing simple imports...")

try:
    from knowledge_spaces.model.database import db
    print("✓ Database import OK")
except Exception as e:
    print(f"✗ Database import failed: {e}")

try:
    from knowledge_spaces.model.knowledge_spaces_model import KnowledgeSpaceModel
    print("✓ Model import OK")  
except Exception as e:
    print(f"✗ Model import failed: {e}")

try:
    from knowledge_spaces.controller.knowledge_spaces_controller import knowledge_spaces_bp
    print("✓ Controller import OK")
except Exception as e:
    print(f"✗ Controller import failed: {e}")

try:
    from knowledge_spaces import knowledge_spaces_bp
    print("✓ Module import OK")
except Exception as e:
    print(f"✗ Module import failed: {e}")

print("Test completed.")